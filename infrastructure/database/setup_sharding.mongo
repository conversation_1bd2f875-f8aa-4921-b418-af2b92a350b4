use FlightOffers

//sh.help()

sh.enableSharding("FlightOffers")

db.flightOffersOW.createIndex({"Route.Multiport": "hashed"})
db.flightOffersRT.createIndex({"Route.Multiport": "hashed"})

sh.shardCollection("FlightOffers.flightOffersOW",{"Route.Multiport": "hashed"})
sh.shardCollection("FlightOffers.flightOffersRT",{"Route.Multiport": "hashed"})

db.runCommand( {collMod: 'flightOffersOW', changeStreamPreAndPostImages: { enabled: true }} )
db.runCommand( {collMod: 'flightOffersRT', changeStreamPreAndPostImages: { enabled: true }} )

db['flightOffersOW.stats'].createIndex({'_id': 'hashed'})
sh.shardCollection('FlightOffers.flightOffersOW.stats', {'_id': 'hashed'} )

db['flightOffersRT.stats'].createIndex({'_id': 'hashed'})
sh.shardCollection('FlightOffers.flightOffersRT.stats', {'_id': 'hashed'} )

db.monthlyOW.createIndex({'_id': 1})
sh.shardCollection('FlightOffers.monthlyOW', {'_id': 1} )

db.monthlyRT.createIndex({'_id': 'hashed'})
sh.shardCollection('FlightOffers.monthlyRT', {'_id': 'hashed'} )

db.travelFusionTimetable.createIndex({'_id.R': 1})
sh.shardCollection('FlightOffers.travelFusionTimetable', {'_id.R': 1} )

/// -- common sharding maintenance commands --
//sh.status()
//sh.stopBalancer()
//sh.startBalancer()