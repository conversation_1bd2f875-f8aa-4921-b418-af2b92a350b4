{"users": [{"name": "cacheuser", "password": "cachepassword", "tags": "administrator"}], "vhosts": [{"name": "/"}], "permissions": [{"user": "cacheuser", "vhost": "/", "configure": ".*", "write": ".*", "read": ".*"}], "exchanges": [{"name": "Esky.LogMessages:LogQueueItem", "vhost": "/", "type": "fanout", "durable": true, "auto_delete": false, "internal": false, "arguments": {}}], "queues": [{"name": "Logs", "vhost": "/", "durable": true, "auto_delete": false}], "bindings": [{"source": "Esky.LogMessages:LogQueueItem", "vhost": "/", "destination": "Logs", "destination_type": "queue", "routing_key": "", "arguments": {}}]}