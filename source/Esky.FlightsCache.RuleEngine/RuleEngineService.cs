using Esky.FlightsCache.PartnerSettings;
using Esky.RuleEngine.Client;
using Esky.RuleEngine.Core;
using Esky.RuleEngine.Wrappers;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Esky.FlightsCache.RuleEngine
{
    public class RuleEngineService<TResult, TRequestWrapper> : IRuleEngineService<TResult, TRequestWrapper>
        where TResult : BaseRuleEngineResultObject, new()
        where TRequestWrapper : Wrapper
    {
        private readonly BasicConfiguration _basicConfig;
        private readonly IRuleEngineServiceClientFactory _clientFactory;
        private readonly ICommunicationLogger _communicationLogger;
        private readonly RuleEngineConfiguration _ruleEngineConfig;

        public RuleEngineService(IRuleEngineServiceClientFactory clientFactory,
            IOptions<BasicConfiguration> basicConfig,
            IOptions<RuleEngineConfiguration> ruleEngineConfig,
            ICommunicationLogger communicationLogger)
        {
            _clientFactory = clientFactory;
            _basicConfig = basicConfig.Value;
            _ruleEngineConfig = ruleEngineConfig.Value;
            _communicationLogger = communicationLogger;
        }

        public async Task<Dictionary<Guid, TResult>> Get(List<TRequestWrapper> wrappers, string ruleEngineName)
        {
            var result = new Dictionary<Guid, TResult>();

            var client = CreateClient();
            var context = CreateContext();

            var response =
                await client.GetMatchingGroupResultAsync<TResult, TRequestWrapper>(context, wrappers, ruleEngineName);

            foreach (var wrapper in wrappers)
            {
                result[wrapper.WrapperId] = default;
            }

            foreach (var groupResult in response)
            {
                result[groupResult.WrapperId] = groupResult.Result;
            }

            return result;
        }

        public async Task<TResult> Get(TRequestWrapper wrapper, string ruleEngineName)
        {
            var client = CreateClient();
            var context = CreateContext();

            return await client.GetMatchingResultAsync<TResult, TRequestWrapper>(context, wrapper, ruleEngineName);
        }

        private IRuleEngineServiceClient CreateClient()
        {
            if (string.IsNullOrWhiteSpace(_ruleEngineConfig.Url))
            {
                throw new InvalidOperationException("RuleEngineApi Url is not configured");
            }

            return _clientFactory.Create(
                _ruleEngineConfig.Url,
                false,
                _communicationLogger);
        }

        private IRuleEngineContext CreateContext()
        {
            return new RuleEngineContext
            {
                EnvironmentCode = _basicConfig.Environment,
                RequestId = Guid.NewGuid().ToString(),
                SessionId = string.Empty,
                RequestType = string.Empty,
                BookingId = string.Empty,
                ProviderBookingId = string.Empty,
                LoggingEnabled = _ruleEngineConfig.IsLoggingEnabled
            };
        }
    }
}