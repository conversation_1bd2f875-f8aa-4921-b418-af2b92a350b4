using Esky.RuleEngine.Core;
using Esky.RuleEngine.Wrappers.CombinedFlights;
using System;
using System.Threading.Tasks;

namespace Esky.FlightsCache.RuleEngine
{
    public class CombinedAirTrafficRulesService : ICombinedAirTrafficRulesService
    {
        private readonly IFlightDestinationFactory _destinationFactory;

        private readonly
            IRuleEngineService<CombinedFlightsFiltersRuleEngineResult, CombinedFlightsFiltersRuleEngineWrapper>
            _ruleEngineService;

        public CombinedAirTrafficRulesService(
            IRuleEngineService<CombinedFlightsFiltersRuleEngineResult, CombinedFlightsFiltersRuleEngineWrapper>
                ruleEngineService,
            IFlightDestinationFactory destinationFactory)
        {
            _ruleEngineService = ruleEngineService;
            _destinationFactory = destinationFactory;
        }

        public Task<CombinedFlightsFiltersRuleEngineResult> GetFilteringRule(CombinedFlightsFiltersRuleParams pars)
        {
            if (pars == null)
            {
                throw new ArgumentNullException(nameof(pars));
            }

            var wrapper = new CombinedFlightsFiltersRuleEngineWrapper
            {
                PartnerCode = pars.PartnerCode,
                MarketCode = pars.ParentPartnerCode,
                Destination = _destinationFactory.CreateDestination(pars.DepartureCode, pars.ArrivalCode),
                NotFoundsSearchMode = false
            };

            return _ruleEngineService.Get(wrapper, RuleEngineNames.CombinedFlightsFilters);
        }
    }
}