using Esky.FlightsCache.ObjectDirectory;
using Esky.RuleEngine.Wrappers.Common;

namespace Esky.FlightsCache.RuleEngine
{
    public class FlightDestinationFactory : IFlightDestinationFactory
    {
        private readonly IObjectDirectoryService _odService;

        public FlightDestinationFactory(IObjectDirectoryService odService)
        {
            _odService = odService;
        }

        public Destination CreateDestination(string departureCode, string arrivalCode)
        {
            return new Destination
            {
                DepartureAirport = departureCode,
                ArrivalAirport = arrivalCode,
                DepartureMultiportCode = _odService.GetMultiportCodeByDestination(departureCode),
                ArrivalMultiportCode = _odService.GetMultiportCodeByDestination(arrivalCode),
                DepartureCountry = _odService.GetCountryCodeByDestination(departureCode),
                ArrivalCountry = _odService.GetCountryCodeByDestination(arrivalCode),
                DepartureContinent = _odService.GetContinentCodeByDestination(departureCode),
                ArrivalContinent = _odService.GetContinentCodeByDestination(arrivalCode)
            };
        }
    }
}