using Esky.RuleEngine.Wrappers;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Esky.FlightsCache.RuleEngine
{
    public interface IRuleEngineService<TResult, TRequestWrapper>
        where TResult : BaseRuleEngineResultObject, new()
        where TRequestWrapper : Wrapper
    {
        Task<Dictionary<Guid, TResult>> Get(List<TRequestWrapper> wrappers, string ruleEngineName);

        Task<TResult> Get(TRequestWrapper wrapper, string ruleEngineName);
    }
}