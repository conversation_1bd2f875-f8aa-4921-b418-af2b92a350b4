<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
        <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
        <RootNamespace>Esky.FlightsCache.CacheRequestConsumer</RootNamespace>
        <UserSecretsId>813f5b86-a212-4711-8dd4-35963295a717</UserSecretsId>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="AspNetCore.HealthChecks.UI.Client" Version="7.1.0" />
        <PackageReference Include="Esky.NLog.RabbitMQ.Target" Version="1.1.3" />
        <PackageReference Include="NLog.Web.AspNetCore" Version="5.3.5" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\Esky.FlightsCache.MessageContract\Esky.FlightsCache.MessageContract.csproj" />
        <ProjectReference Include="..\Esky.FlightsCache.OpenTelemetry\Esky.FlightsCache.OpenTelemetry.csproj" />
        <ProjectReference Include="..\Esky.FlightsCache.Processing\Esky.FlightsCache.Processing.csproj" />
        <ProjectReference Include="..\Esky.FlightsCache.ServiceBus\Esky.FlightsCache.ServiceBus.csproj" />
    </ItemGroup>

    <ItemGroup>
        <Content Update="appsettings.Development.json">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
            <DependentUpon>appsettings.json</DependentUpon>
        </Content>
        <Content Update="appsettings.Production.json">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
            <DependentUpon>appsettings.json</DependentUpon>
        </Content>
        <Content Update="appsettings.Staging.json">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
            <DependentUpon>appsettings.json</DependentUpon>
        </Content>
        <Content Include="..\..\.dockerignore">
          <Link>.dockerignore</Link>
        </Content>
        <Content Update="google.json">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </Content>
    </ItemGroup>

</Project>
