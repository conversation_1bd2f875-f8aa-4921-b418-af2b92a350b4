using Esky.FlightsCache.MessageContract;
using Esky.FlightsCache.Processing;
using MassTransit;

namespace Esky.FlightsCache.CacheRequestConsumer;

public class RemoveFromCacheBySourceIdQueueElementConsumer : IConsumer<RemoveFromCacheBySourceIdQueueElement>
{
    private readonly IFlightsCacheService _cacheService;
    private readonly ILogger _logger;

    public RemoveFromCacheBySourceIdQueueElementConsumer(
        IFlightsCacheService cacheService,
        ILogger<RemoveFromCacheBySourceIdQueueElementConsumer> logger)
    {
        _cacheService = cacheService;
        _logger = logger;
    }

    public async Task Consume(ConsumeContext<RemoveFromCacheBySourceIdQueueElement> context)
    {
        var payload = context.Message;
        var id = $"SID: {payload.SessionId}, RID: {payload.RequestId}";

        _logger.LogInformation(
            "RemoveFromCacheBySourceIdQueueElementConsumer received message for source ids: {sourceIds}. {id}",
            string.Join(", ", payload.CacheSourceIds), id);

        var deletedCount = await _cacheService.RemoveFromCacheBySourceId(context.Message.CacheSourceIds);

        _logger.LogInformation("Deleted elements by source id: {deletedCount}. {id}", deletedCount, id);
    }
}