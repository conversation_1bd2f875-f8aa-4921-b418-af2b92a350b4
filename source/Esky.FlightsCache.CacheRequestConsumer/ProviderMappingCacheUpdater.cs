using Esky.FlightsCache.Database.Configuration;
using Esky.FlightsCache.ProviderMapping;

namespace Esky.FlightsCache.CacheRequestConsumer;

public class ProviderMappingCacheUpdater(
    ILogger<ProviderMappingCacheUpdater> logger,
    IFlightsCacheProviderConverter fpcConverter,
    IConfigurationDatabase providerMappingDatabase,
    IMarginConfigurationService marginConfigurationService,
    ITechnicalMarginService technicalMarginService)
    : IHostedService
{
    private Timer? _timer;

    public Task StartAsync(CancellationToken cancellationToken)
    {
        logger.LogInformation("ProviderCodeMappingCacheUpdater Hosted Service running");

        _timer = new Timer(UpdateCache, null, TimeSpan.FromMinutes(10), TimeSpan.FromMinutes(10));

        return UpdateCache();
    }

    public Task StopAsync(CancellationToken cancellationToken)
    {
        logger.LogInformation("ProviderCodeMappingCacheUpdater is stopping");
        _timer?.Change(Timeout.Infinite, 0);
        return Task.CompletedTask;
    }

    private async void UpdateCache(object? state)
    {
        await UpdateCache();
    }

    private Task UpdateCache()
    {
        return Task.WhenAll(UpdateProviderMapping(), UpdateMargins());

        async Task UpdateProviderMapping()
        {
            try
            {
                fpcConverter.UpdateInternalMapping(await providerMappingDatabase.GetList());
                logger.LogInformation("ProviderCodeMapping cache updated");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Unable to update ProviderCodeMapping cache: {Message}", ex.Message);
            }
        }

        async Task UpdateMargins()
        {
            try
            {
                marginConfigurationService.UpdateMargins(await providerMappingDatabase.GetMargins());
                logger.LogInformation("ProviderMargins cache updated");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Unable to update ProviderMargins cache: {Message}", ex.Message);
            }
        }

        async Task UpdateTechnicalMargins()
        {
            try
            {
                technicalMarginService.UpdateMargins(await providerMappingDatabase.GetTechnicalMargins());
                logger.LogInformation("ProviderMargins cache updated");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Unable to update ProviderMargins cache: {Message}", ex.Message);
            }
        }
    }
}