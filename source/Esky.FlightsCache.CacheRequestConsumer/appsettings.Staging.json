{"BigQueryOptions": {"ProjectId": "esky-ets-logs-ci", "CredentialsPath": "/var/secrets/google/key.json"}, "CacheRequestProcessor": {"TargetDbConnectionString": "secret"}, "FlightOffersConfiguration": {"ConnectionString": "secret", "AdditionalMongoConnectionStrings": []}, "FlightsCacheConfiguration": {"ConnectionUrl": "secret"}, "FlightsCache": {"ServiceBus": {"Url": "secret", "Login": "secret", "Password": "secret"}}, "RedisCaching": {"ConnectionString": "secret"}, "MongoObjectDirectory": {"ConnectionString": "secret"}, "ServiceBus_Clustermembers": "rabbitmq-logs.service.consul.", "ServiceBus_Login": "ets", "ServiceBus_VHost": "ets", "ServiceBus_Password": "secret", "NLog": {"rules": [{"logger": "Microsoft.AspNetCore.*", "finalMinLevel": "<PERSON><PERSON>"}, {"logger": "Esky.FlightsCache.Processing.CacheRequestProcessor", "finalMinLevel": "<PERSON><PERSON>"}, {"logger": "Esky.CurrencyService.ApiClient.Logger.DefaultCommunicationLogger", "finalMinLevel": "<PERSON><PERSON>"}, {"logger": "*", "minLevel": "Info", "writeTo": "rabbit", "final": true}]}}