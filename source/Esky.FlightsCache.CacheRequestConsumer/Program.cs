using Esky.FlightsCache.CacheRequestConsumer;
using Esky.FlightsCache.Database;
using Esky.FlightsCache.OpenTelemetry;
using NLog.Extensions.Logging;

DatabaseContext.RegisterSerializersAndConventions();

var builder = WebApplication.CreateBuilder(args);
var configuration = builder.Configuration;

builder
    .Services
    .RegisterServices(configuration);

builder.Logging
    .ClearProviders()
    .AddNLog();

var app = builder.Build();

if (builder.Environment.IsDevelopment())
{
    app.UseDeveloperExceptionPage();
}

app
    .UseHealthChecks()
    .UseOpenTelemetryDefaults();

app.Run(async context => await context.Response.WriteAsync("Service responsible for storing data in flight cache database"));

app.Run();