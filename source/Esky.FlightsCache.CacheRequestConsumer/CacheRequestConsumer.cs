using Esky.FlightsCache.MessageContract;
using Esky.FlightsCache.Processing;
using MassTransit;

namespace Esky.FlightsCache.CacheRequestConsumer;

public class CacheRequestConsumer : IConsumer<CacheRequest>
{
    private readonly ICacheRequestProcessor _cacheRequestProcessor;
    private readonly ILogger _logger;

    public CacheRequestConsumer(ICacheRequestProcessor cacheRequestProcessor, ILogger<CacheRequestConsumer> logger)
    {
        _cacheRequestProcessor = cacheRequestProcessor;
        _logger = logger;
    }

    public Task Consume(ConsumeContext<CacheRequest> context)
    {
        if (_logger.IsEnabled(LogLevel.Debug))
        {
            _logger.LogDebug("CacheRequestConsumer received message from '{machineName}'. Id: '{groupName}'",
                context.Message.SourceDescription?.MachineName, context.Message.CommandOptions?.GroupName);
        }

        return _cacheRequestProcessor.ProcessMessageAsync(context.Message);
    }
}