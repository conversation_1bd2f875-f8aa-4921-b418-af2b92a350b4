{"BigQueryOptions": {"ProjectId": "esky-ets-logs-pro", "CredentialsPath": "/var/secrets/google/key.json"}, "CacheRequestProcessor": {"TargetDbConnectionString": "secret"}, "FlightsCacheConfiguration": {"ConnectionUrl": "secret"}, "FlightOffersConfiguration": {"ConnectionString": "secret", "AdditionalMongoConnectionStrings": []}, "FlightsCache": {"ServiceBus": {"Url": "secret", "Password": "secret"}}, "RedisCaching": {"ConnectionString": "secret"}, "MongoObjectDirectory": {"ConnectionString": "secret"}, "ServiceBus_Clustermembers": "rabbitmq-logs.service.consul.", "ServiceBus_Login": "ets", "ServiceBus_VHost": "ets", "ServiceBus_Password": "secret", "NLog": {"rules": [{"logger": "Microsoft.AspNetCore.*", "finalMinLevel": "<PERSON><PERSON>"}, {"logger": "Esky.FlightsCache.Processing.CacheRequestProcessor", "finalMinLevel": "<PERSON><PERSON>"}, {"logger": "Esky.CurrencyService.ApiClient.Logger.DefaultCommunicationLogger", "finalMinLevel": "<PERSON><PERSON>"}, {"logger": "*", "minLevel": "Info", "writeTo": "rabbit", "final": true}]}}