using Esky.FlightsCache.Database;
using Esky.FlightsCache.Database.FlightOffers;
using Esky.FlightsCache.Database.Sql;
using Esky.FlightsCache.MessageContract;
using Esky.FlightsCache.Processing;
using Esky.FlightsCache.Processing.CacheSourceStorage;
using MassTransit;

namespace Esky.FlightsCache.CacheRequestConsumer;

public class UpdateSingleFlightRequestConsumer(
    IEnumerable<ICacheRequestFlightsDecorator> cacheRequestDecorators,
    IFlightsCacheDatabase flightsCacheDatabase,
    ICacheSourceStorage cacheSourceStorage,
    IFlightOffersRepository flightOffersRepository)
    : IConsumer<UpdateSingleFlightRequest>
{
    public async Task Consume(ConsumeContext<UpdateSingleFlightRequest> context)
    {
        using var activity = Activities.Source.StartActivity();
        var cacheRequest = new CacheRequest
        {
            SourceDescription = context.Message.SourceDescription,
            Flights = [context.Message.Flight],
            CommandOptions = new CommandDataOptions
            {
                GroupName = context.Message.SourceDescription.SessionId,
                DeleteOptions = new DeleteOptions { IsEnabled = false, AirlineCodes = [], ProviderCodes = [] },
                SkipDataFiltering = true,
                SkipSQLSave = true
            }
        };

        foreach (var decorator in cacheRequestDecorators)
        {
            await decorator.Decorate(cacheRequest.Flights);
        }

        var sourceId = await flightsCacheDatabase.GenerateSourceIdAsync(cacheRequest);
        _ = cacheSourceStorage.AddSource(cacheRequest, sourceId);

        var flights = cacheRequest.Flights.SplitFlights(sourceId);

        await flightOffersRepository.UpdateFlightPrices(flights, sourceId, cacheRequest);
    }
}