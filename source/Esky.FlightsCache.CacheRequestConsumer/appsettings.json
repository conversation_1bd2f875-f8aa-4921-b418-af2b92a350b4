{"Basic": {"Environment": "PRO"}, "PartnerSettings": {"System": "ETS", "ApiKey": "secret", "Url": "http://esky-partnersettings-api-green.query.consul./"}, "ExternalServices": {"CurrencyService": {"Url": "http://esky-currencyservice-api-green.query.consul./"}}, "RoundTripMergeOptions": {"58": ["wizzairxml", "ezy"], "19": ["wizzair"]}, "SourceSettings": {"SourceDiscard": {"flightsearch-api-additional": false}}, "OpenTelemetry": {"ServiceName": "FlightsCache.CacheRequestConsumer", "Metrics": "Masstransit,FlightsCache.Processing,FlightsCache.Database", "Traces": "Masstransit,FlightsCache.Database", "PerSecond": 0.01}, "CacheRequestProcessor": {"TargetDbConnectionString": "secret"}, "Aggregation": {"IsGlobalMultiticketAggregationDisabled": false, "IsProviderMultiticketAggregationDisabled": false, "IsOneWayAggregationDisabled": false, "IsRoundTripAggregationDisabled": false, "DegreeOfParallelism": 8, "BulkAggregation": {"IsEnabledForOneWay": true, "IntervalInMinutes": 3, "ProcessingTimeoutInMinutes": 45, "MaxElementsToProcess": 100000, "MaxBufferLength": 10000000, "GroupingMode": "RouteAndConsecutiveMonths"}}, "RedisCaching": {"InstanceName": "FlightsCache"}, "FlightsCacheConfiguration": {"ConnectionUrl": "secret", "CollectionName": "providerConfiguration", "RyanairRouteFeeCollectionName": "ryanair<PERSON><PERSON>e<PERSON><PERSON>", "WizzairFeeCollectionName": "wizzairFees", "AirlineFeesCollectionName": "airlineFees"}, "FlightsCache": {"ServiceBus": {"Url": "secret", "Login": "ets", "Password": "secret", "UseCluster": false, "SerializationMode": "JsonGzip", "SerializationProvider": "SystemTextJson", "PrefetchCount": 40, "ConcurrencyLimit": 10}}, "FlightsFilter": {"FlightsPerChunk": "1", "TimeSlotRanges": "09:00;14:00;18:00", "ProvidersToFilterGlobally": {"ProviderCodes": [], "Include": true}, "DomesticOnlyFilter": {"Countries": ["US", "IT", "ES"]}, "IsRtSaveToSqlEnabled": "false", "ProvidersWithAllowedSingleSeatsStoring": [78], "AirlinesWithDisallowedUnknownSeatsStoring": ["W6", "W4", "W9", "5W"]}, "MongoObjectDirectory": {"DatabaseName": "ObjectDirectory"}, "BigQueryOptions": {"ProjectId": "project-id", "CredentialsPath": "/var/secrets/google/key.json"}, "FlightOffersConfiguration": {"DaysForward": 10000}, "UpdateSingleFlightRequest": {"IsEnabled": true, "PrefetchCount": 10, "ConcurrentMessageLimit": 2}, "NLog": {"throwConfigExceptions": true, "internalLogFile": "~\\nlog\\internal-nlog.txt", "internalLogLevel": "<PERSON><PERSON>", "internalLogToConsole": true, "autoReload": true, "extensions": [{"assembly": "Esky.NLog.RabbitMQ.Target"}], "targets": {"async": true, "rabbit": {"type": "RabbitMQ", "username": "${configsetting:item=ServiceBus_Login}", "password": "${configsetting:item=ServiceBus_Password}", "clustermembers": "${configsetting:item=ServiceBus_Clustermembers}", "vhost": "${configsetting:item=ServiceBus_VHost}", "compression": "GZip", "fields": [{"key": "HostName", "name": "HostName", "layout": "${machinename}"}, {"key": "Date", "name": "Date", "layout": "${date:universalTime=False:format=s}"}, {"key": "Application", "name": "Application", "layout": "esky-flightscache-cacherequestconsumer"}, {"key": "Environment", "name": "Environment", "layout": "${configsetting:item=ASPNETCORE_ENVIRONMENT}"}, {"key": "Exception", "name": "Exception", "layout": "${exception}"}, {"key": "AdditionalMessage", "name": "AdditionalMessage", "layout": "${exception:format=toString,Data}"}, {"key": "StackTrace", "name": "StackTrace", "layout": "${exception:format=StackTrace}"}, {"key": "Context", "name": "Provider", "layout": "${event-properties:Context}"}]}}}}