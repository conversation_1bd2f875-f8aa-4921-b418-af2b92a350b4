FROM europe-docker.pkg.dev/esky-common/esky-docker-virtual/dotnet/aspnet:8.0 AS base
ENV ASPNETCORE_HTTP_PORTS=80
WORKDIR /app

FROM europe-docker.pkg.dev/esky-common/esky-docker-virtual/dotnet/sdk:8.0 AS build
WORKDIR /src
COPY ["nuget.config", "."]
COPY ["source/Esky.FlightsCache.CacheRequestConsumer/Esky.FlightsCache.CacheRequestConsumer.csproj", "source/Esky.FlightsCache.CacheRequestConsumer/"]
COPY ["source/Esky.FlightsCache.CurrencyProvider/Esky.FlightsCache.CurrencyProvider.csproj", "source/Esky.FlightsCache.CurrencyProvider/"]
COPY ["source/Esky.FlightsCache.MessageContract/Esky.FlightsCache.MessageContract.csproj", "source/Esky.FlightsCache.MessageContract/"]
COPY ["source/Esky.FlightsCache.OpenTelemetry/Esky.FlightsCache.OpenTelemetry.csproj", "source/Esky.FlightsCache.OpenTelemetry/"]
COPY ["source/Esky.FlightsCache.Processing/Esky.FlightsCache.Processing.csproj", "source/Esky.FlightsCache.Processing/"]
COPY ["source/Esky.FlightsCache.Common/Esky.FlightsCache.Common.csproj", "source/Esky.FlightsCache.Common/"]
COPY ["source/Esky.FlightsCache.Database/Esky.FlightsCache.Database.csproj", "source/Esky.FlightsCache.Database/"]
COPY ["source/Esky.FlightsCache.ProviderMapping/Esky.FlightsCache.ProviderMapping.csproj", "source/Esky.FlightsCache.ProviderMapping/"]
COPY ["source/Esky.FlightsCache.ResultFilters/Esky.FlightsCache.ResultFilters.csproj", "source/Esky.FlightsCache.ResultFilters/"]
COPY ["source/Esky.FlightsCache.ObjectDirectory/Esky.FlightsCache.ObjectDirectory.csproj", "source/Esky.FlightsCache.ObjectDirectory/"]
COPY ["source/Esky.FlightsCache.PartnerSettings/Esky.FlightsCache.PartnerSettings.csproj", "source/Esky.FlightsCache.PartnerSettings/"]
COPY ["source/Esky.FlightsCache.RuleEngine/Esky.FlightsCache.RuleEngine.csproj", "source/Esky.FlightsCache.RuleEngine/"]
COPY ["source/Esky.FlightsCache.ServiceBus/Esky.FlightsCache.ServiceBus.csproj", "source/Esky.FlightsCache.ServiceBus/"]
RUN dotnet restore "source/Esky.FlightsCache.CacheRequestConsumer/Esky.FlightsCache.CacheRequestConsumer.csproj"
COPY . .
WORKDIR "/src/source/Esky.FlightsCache.CacheRequestConsumer"
RUN dotnet build "Esky.FlightsCache.CacheRequestConsumer.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "Esky.FlightsCache.CacheRequestConsumer.csproj" -c Release -o /app/publish /p:UseAppHost=false

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "Esky.FlightsCache.CacheRequestConsumer.dll"]
