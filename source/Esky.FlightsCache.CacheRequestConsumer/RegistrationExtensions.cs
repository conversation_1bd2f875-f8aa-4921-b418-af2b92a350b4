using Esky.FlightsCache.Common;
using Esky.FlightsCache.CurrencyProvider;
using Esky.FlightsCache.Database;
using Esky.FlightsCache.Database.Aggregations;
using Esky.FlightsCache.Database.Airports;
using Esky.FlightsCache.Database.Configuration;
using Esky.FlightsCache.Database.FlightOffers;
using Esky.FlightsCache.Database.Sql;
using Esky.FlightsCache.Database.Timetables;
using Esky.FlightsCache.ObjectDirectory;
using Esky.FlightsCache.OpenTelemetry;
using Esky.FlightsCache.PartnerSettings;
using Esky.FlightsCache.Processing;
using Esky.FlightsCache.Processing.CacheSourceStorage;
using Esky.FlightsCache.Processing.Multipliers;
using Esky.FlightsCache.Processing.PriceDecorators;
using Esky.FlightsCache.Processing.PriceDecorators.AirlineFees;
using Esky.FlightsCache.Processing.PriceDecorators.EasyJet;
using Esky.FlightsCache.Processing.PriceDecorators.Lufthansa;
using Esky.FlightsCache.Processing.PriceDecorators.Ryanair;
using Esky.FlightsCache.Processing.PriceDecorators.Wizzair;
using Esky.FlightsCache.Processing.PriceDecorators.Jet2;
using Esky.FlightsCache.Processing.PriceDecorators.Latam;
using Esky.FlightsCache.Processing.TechnicalMargin;
using Esky.FlightsCache.ProviderMapping;
using Esky.FlightsCache.ResultFilters.AirportCode;
using Esky.FlightsCache.ResultFilters.FlightListFilters;
using Esky.FlightsCache.ResultFilters.ProviderAirlineExclude;
using Esky.FlightsCache.ServiceBus;
using MassTransit;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;
using OpenTelemetry.Trace;

namespace Esky.FlightsCache.CacheRequestConsumer;

public static class RegistrationExtensions
{
    public static IServiceCollection RegisterServices(this IServiceCollection services, IConfiguration configuration)
    {
        // provider mapping
        services.AddSingleton<IConfigurationDatabase, ConfigurationDatabase>();
        services.AddSingleton(_ => configuration.GetSection("FlightsCacheConfiguration").Get<ConfigurationDatabase.Settings>()!);
        services.AddSingleton<IFlightsCacheProviderConverter>(_ => new FlightsCacheProviderConverter([]));
        services.AddSingleton<IMarginConfigurationService, MarginConfigurationService>();
        services.AddHostedService<ProviderMappingCacheUpdater>();

        services.Configure<HostOptions>(opts => opts.ShutdownTimeout = TimeSpan.FromSeconds(60));

        services
            .AddLogging()
            .ConfigureBus(configuration.GetSection("FlightsCache:ServiceBus").Get<BusSettings>()!)
            .ConfigureOpenTelemetry(
                configuration.GetSection("OpenTelemetry").Get<OpenTelemetryOptions>()!,
                tracesConfigurator: builder => builder.SetSampler(new ParentBasedSampler(new RateLimitingSampler(configuration.GetSection("OpenTelemetry:PerSecond").Get<double>())))
            )
            .RegisterOpenTracingShim()
            .AddMemoryCache();

        services.AddCustomHealthChecks();

        services.Configure<CacheRequestProcessorConfiguration>(configuration.GetSection("CacheRequestProcessor"));
        services.AddSingleton(sp => new DatabaseContext(sp.GetRequiredService<IOptions<FlightOffersConfiguration>>().Value.ConnectionString));
        services.AddSingleton<ICacheRequestProcessor, CacheRequestProcessor>();
        services.Configure<SourceSettings>(configuration.GetSection("SourceSettings"));
        services.AddRyanairFee(configuration);
        services.AddWizzairFee();
        services.AddEasyJetFee();
        services.AddLufthansaFee();
        services.AddJet2Fee();
        services.AddLatamFee();
        services.AddAirlineFees(configuration);
        services.AddSingleton<ITechnicalMarginService, TechnicalMarginService>();
        services.AddSingleton<ITechnicalMarginCalculator, TechnicalMarginCalculator>();
        
        services.AddSingleton<ICacheRequestFlightsDecorator, OfficeIdAsSupplierFlightsDecorator>();
        services.AddSingleton<ICacheRequestFlightsDecorator, ProviderCodeCacheRequestFlightsDecorator>();
        services.AddSingleton<ICacheRequestFlightsDecorator, SearchCodesCacheRequestFlightsDecorator>();
        services.AddSingleton<ICacheRequestFlightsDecorator, CustomFeeDecorator>();
        services.AddSingleton<ICacheRequestFlightsDecorator, TechnicalMarginDecorator>(); // must be after CustomFeeDecorator
        services.AddSingleton<ICacheRequestFlightsDecorator, FlightTimeDecorator>();

        services.AddSingleton<IAirportsRepository, AirportsRepository>();
        services.Decorate<IAirportsRepository, CacheAirportsRepository>();

        services.AddSingleton<IFlightIdProvider, FlightIdProvider>();
        services.AddSingleton<IFlightsCacheDatabase, FlightsCacheDatabase>();
        services.AddSingleton<IFlightsCacheService, HybridFlightsCacheService>();
        services.AddSingleton<IPriceHashCalculator, PriceHashCalculator>();
        
        services.AddSingleton<ISourceIdGenerator, SourceIdGenerator>();
        services.AddSingleton<IFlightOffersRepository, FlightOffersRepository>();
        services.AddSingleton<IFlightOffersConverter, FlightOffersConverter>();
        services.AddSingleton<ISupplierStorage, SupplierStorage>();
        services.AddSingleton<IFlightOffersToDeleteGenerator, FlightOffersToDeleteGenerator>();
        services.Configure<FlightOffersConfiguration>(configuration.GetSection("FlightOffersConfiguration"));
        services.AddSingleton(sp => sp.GetRequiredService<IOptions<FlightOffersConfiguration>>().Value);

        services.AddMongoStores(configuration);
        
        // unfortunately monthly aggregation is triggered after processing cache request :(
        services.AddSingleton<IRouteAggregationBuffer, RedisRouteAggregationBuffer>();
        services.AddSingleton<IRedisConnection, RedisConnection>();
        services.Configure<RedisCachingConfiguration>(configuration.GetSection("RedisCaching"));
        services.Configure<AggregationConfiguration>(configuration.GetSection("Aggregation"));
        services.Configure<BulkAggregationConfiguration>(configuration.GetSection("Aggregation:BulkAggregation"));
        services.AddSingleton(sp => sp.GetRequiredService<IOptions<BulkAggregationConfiguration>>().Value);
        services.AddConfigurationRoundTripMergeOptions(configuration.GetSection("RoundTripMergeOptions"));

        services.AddSingleton<IAirportsRepository>(sp => new CacheAirportsRepository(new AirportsRepository(sp.GetRequiredService<DatabaseContext>()), sp.GetRequiredService<IMemoryCache>()));

        services.ConfigureCacheSourceStorage(configuration);
        services.ConfigureObjectDirectory(configuration);
        services.ConfigurePartnerSettings(configuration);

        services.AddSingleton<TravelFusionTimetableRepository>();
        services.AddSingleton(sp => new TravelFusionTimetableService(sp.GetRequiredService<TravelFusionTimetableRepository>()));
        services.AddSingleton<DisabledTimetableService>();
        services.AddSingleton<ITimetableServiceFactory, TimetableServiceFactory>();
        
        services.AddSingleton<ICacheRequestMultiplierFactory, CacheRequestMultiplierFactory>();
        services.AddSingleton<DisabledCacheRequestMultiplier>();
        services.AddSingleton<SSCEasyJetCacheRequestMultiplier>();

        services.ConfigureCurrencyProvider(configuration);
        services.ConfigureFlightsFilter(configuration);
        services.AddSingleton<IProviderAirlineExcludeSettingsDatabase, ProviderAirlineExcludeSettingsDatabase>();

        return services;
    }
    
    private static void AddMongoStores(this IServiceCollection services, IConfiguration configuration)
    {
        var config = configuration.GetSection("FlightOffersConfiguration").Get<FlightOffersConfiguration>() ?? throw new ApplicationException("FlightOffersConfiguration missing");

        foreach (var connectionString in config.GetConnectionStrings())
        {
            services.AddKeyedSingleton<DatabaseContext>(
                connectionString,
                (sp, cs) => sp.IsMainDatabase((string)cs)
                    ? sp.GetRequiredService<DatabaseContext>()
                    : new DatabaseContext((string)cs)
            );

            services.AddKeyedSingleton<IMonthlyAggregationsService>(
                connectionString,
                (sp, cs) =>
                {
                    var aggregationService = new MonthlyAggregationsService(
                        sp.GetRequiredService<IOptions<AggregationConfiguration>>(),
                        sp.GetRequiredService<ILogger<MonthlyAggregationsService>>(),
                        sp.GetRequiredKeyedService<DatabaseContext>((string)cs)
                    );

                    // processing the same input data for each database, to prevent duplicates we use buffer for main database only
                    return sp.IsMainDatabase((string)cs)
                        ? new BufferedMonthlyAggregationsService(
                            aggregationService,
                            sp.GetRequiredService<IRouteAggregationBuffer>(),
                            sp.GetRequiredService<BulkAggregationConfiguration>()
                        )
                        : new OneWayNoopMonthlyAggregationsService(aggregationService);
                }
            );

            services.AddKeyedSingleton<IAirportsRepository>(
                connectionString,
                (sp, cs) => sp.IsMainDatabase((string)cs)
                    ? sp.GetRequiredService<IAirportsRepository>()
                    : new CacheAirportsRepository(
                        new AirportsRepository(sp.GetRequiredKeyedService<DatabaseContext>((string)cs)),
                        sp.GetRequiredService<IMemoryCache>()
                    )
            );
        }

        services.AddSingleton<IFlightOffersStorage>(
            sp =>
            {
                var connectionStrings = sp.GetRequiredService<IOptions<FlightOffersConfiguration>>().Value.GetConnectionStrings();

                var stores = connectionStrings
                    .Select(
                        connectionString =>
                        {
                            var databaseContext = sp.GetRequiredKeyedService<DatabaseContext>(connectionString);
                            var monthlyAggregationsService = sp.GetRequiredKeyedService<IMonthlyAggregationsService>(connectionString);
                            var airportsRepository = sp.GetRequiredKeyedService<IAirportsRepository>(connectionString);
                            return new FlightOffersStorage(databaseContext, airportsRepository, monthlyAggregationsService);
                        }
                    );

                return new MultiFlightOfferStore(stores);
            }
        );
    }

    private static bool IsMainDatabase(this IServiceProvider serviceProvider, string connectionString)
    {
        return serviceProvider
            .GetRequiredService<IOptions<FlightOffersConfiguration>>().Value
            .IsMainDatabase(connectionString);
    }

    private static IServiceCollection ConfigureBus(this IServiceCollection services, BusSettings busSettings)
    {
        return services
            .AddLogging()
            .AddBusWithInstrumentation(
                busSettings,
                bus =>
                {
                    bus.AddConsumers(typeof(Program).Assembly);
                    bus.Configure<MassTransitHostOptions>(opt => opt.WaitUntilStarted = true);
                    bus.ConfigureHealthCheckOptions(opt =>
                    {
                        opt.Tags.Add(HealthChecks.Tags.Readiness);
                        opt.Tags.Add(HealthChecks.Tags.Liveness);
                    });
                },
                (context, configurator) => RegisterEndpoints(context, configurator, busSettings)
            );
    }

    private static void RegisterEndpoints(IBusRegistrationContext context, IRabbitMqBusFactoryConfigurator configurator, BusSettings busSettings)
    {
        var env = context.GetRequiredService<IWebHostEnvironment>();
        var shouldBind = env.IsDevelopment() && !busSettings.Url.Contains("consul");

        configurator.ReceiveEndpoint("Esky.FlightsCache.CacheRequest", endpoint =>
        {
            endpoint.ConfigureConsumeTopology = shouldBind;
            endpoint.ConfigureConsumer<CacheRequestConsumer>(context);
        });

        var configuration = context.GetRequiredService<IConfiguration>();
        var isEnabled = configuration.GetValue<bool>("UpdateSingleFlightRequest:IsEnabled");
        if (isEnabled)
        {
            configurator.ReceiveEndpoint("Esky.FlightsCache.UpdateSingleFlightRequest", endpoint =>
            {
                endpoint.ConfigureConsumeTopology = shouldBind;
                endpoint.ConfigureConsumer<UpdateSingleFlightRequestConsumer>(context);
                endpoint.PrefetchCount = configuration.GetValue<int?>("UpdateSingleFlightRequest:PrefetchCount") ?? 10;
                endpoint.ConcurrentMessageLimit = configuration.GetValue<int?>("UpdateSingleFlightRequest:ConcurrentMessageLimit");
                endpoint.UseRetry(e => e.None());
            });
        }

        configurator.ReceiveEndpoint("Esky.FlightsCache.RemoveFromCacheBySourceIdRequest", endpoint =>
        {
            endpoint.ConfigureConsumeTopology = shouldBind;
            endpoint.ConfigureConsumer<RemoveFromCacheBySourceIdQueueElementConsumer>(context);
        });
    }
}