{
  "CacheRequestProcessor": {
    "TargetDbConnectionString": "Server=host.docker.internal,1433;Database=IBE_FlightsPromoCache;uid=sa;pwd=************;Connection Timeout=30",
  },
  "FlightOffersConfiguration": {
    "ConnectionString": "mongodb://host.docker.internal:27018/FlightsPromoCache?appname=esky-flightscache-cacherequestconsumer",
    "AdditionalMongoConnectionStrings": []
  },
  "BigQueryOptions": {
    "ProjectId": "esky-ets-logs-ci",
    "CredentialsPath": "google.json"
  },
  "FlightsCacheConfiguration": {
    "ConnectionUrl": "mongodb://host.docker.internal:27018/FlightsCacheConfiguration?appname=esky-flightscache-cacherequestconsumer"
  },
  "FlightsCache": {
    "ServiceBus": {
      "Url": "rabbitmq://host.docker.internal",
      "Login": "cacheuser",
      "Password": "cachepassword",
      "ConcurrencyLimit": 1
    }
  },
  "RedisCaching": {
    "ConnectionString": "host.docker.internal:6379,password=k8rBZeXosd,asyncTimeout=120000"
  },
  "MongoObjectDirectory": {
    "ConnectionString": "mongodb://host.docker.internal:27018/ObjectDirectory?appname=esky-flightscache-cacherequestconsumer"
  },
  "ServiceBus_Clustermembers": "host.docker.internal",
  "ServiceBus_Login": "cacheuser",
  "ServiceBus_VHost": "/",
  "ServiceBus_Password": "cachepassword",
  "NLog": {
    "targets": {
      "console": { "type": "Console" }
    },
    "rules": [
      { "logger": "Microsoft.AspNetCore.*", "finalMinLevel": "Warn" },
      { "logger": "*", "minLevel": "Info", "writeTo": "console", "final": true },
      { "logger": "*", "minLevel": "Info", "writeTo": "rabbit" } // to avoid Warn: Unused target detected
    ]
  }
}
