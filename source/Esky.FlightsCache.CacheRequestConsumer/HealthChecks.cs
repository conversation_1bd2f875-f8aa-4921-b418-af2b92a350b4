using HealthChecks.UI.Client;
using Microsoft.AspNetCore.Diagnostics.HealthChecks;
using Microsoft.Extensions.Diagnostics.HealthChecks;

namespace Esky.FlightsCache.CacheRequestConsumer;

public static class HealthChecks
{
    public static IHealthChecksBuilder AddCustomHealthChecks(this IServiceCollection self)
    {
        return self
            .AddHealthChecks()
            .AddCheck("self", () => HealthCheckResult.Healthy(), new[] { Tags.Liveness });
    }

    public static IApplicationBuilder UseHealthChecks(this IApplicationBuilder self)
    {
        return self
            .UseHealthChecks("/health", new HealthCheckOptions { Predicate = _ => true, ResponseWriter = UIResponseWriter.WriteHealthCheckUIResponse })
            .UseHealthChecks("/health/ready", new HealthCheckOptions { Predicate = r => r.Tags.Contains(Tags.Readiness) })
            .UseHealthChecks("/health/live", new HealthCheckOptions { Predicate = r => r.Tags.Contains(Tags.Liveness) });
    }

    internal static class Tags
    {
        internal const string Readiness = "readiness";
        internal const string Liveness = "liveness";
    }
}