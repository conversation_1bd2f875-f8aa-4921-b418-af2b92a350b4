using Esky.FlightsStaticData.ObjectDirectory.Abstraction;
using Esky.FlightsStaticData.ObjectDirectory.Contract;
using Microsoft.Extensions.Caching.Memory;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Esky.FlightsCache.ObjectDirectory
{
    public class ObjectDirectoryService : IObjectDirectoryService
    {
        private readonly IMemoryCache _cache;
        private readonly IObjectDirectoryRepository _objectDirectoryRepository;

        public ObjectDirectoryService(IObjectDirectoryRepository objectDirectoryRepository, IMemoryCache cache)
        {
            _objectDirectoryRepository = objectDirectoryRepository;
            _cache = cache;
        }

        public bool IsAirport(string airportCode)
        {
            return GetAllAirportCodes().Contains(airportCode);
        }

        public bool IsAirline(string airlineCode)
        {
            var airline = GetFromCache($"GetAirlineByCode_{airlineCode}",
                () => _objectDirectoryRepository.GetAirlineByCode(airlineCode));

            return airline is not null;
        }

        public string GetAirportTimeZone(string code)
        {
            return GetDestinationAttribute(code, airport => airport.TimeZoneId);
        }

        public string GetMultiportCodeByDestination(string code)
        {
            return GetDestinationAttribute(code,
                airport => airport.MultiportCodes?.FirstOrDefault()?.Code ?? code, multiport => multiport.Code);
        }

        public string GetCountryCodeByDestination(string code)
        {
            return GetDestinationAttribute(code,
                airport => airport.CountryCode, multiport => multiport.CountryCode);
        }

        public string GetContinentCodeByDestination(string code)
        {
            return GetDestinationAttribute(code,
                airport => airport.ContinentCode, multiport => multiport.ContinentCode);
        }

        private string GetDestinationAttribute(string destinationCode,
            Func<Airport, string> airportFunc,
            Func<Multiport, string> multiportFunc = null)
        {
            if (multiportFunc != null)
            {
                var multiport = GetMultiportByCode(destinationCode);

                if (multiport != null)
                {
                    return multiportFunc(multiport);
                }
            }

            var airport = GetAirportByCode(destinationCode);

            if (airport == null)
            {
                throw new Exception($"Destination code does not exists: {destinationCode}");
            }

            return airportFunc(airport);
        }

        private HashSet<string> GetAllAirportCodes()
        {
            return GetFromCache("GetAllAirportCodes", () => _objectDirectoryRepository.GetAllAirports().Select(x => x.Code).ToHashSet());
        }

        private Airport GetAirportByCode(string destinationCode)
        {
            return GetFromCache($"GetAirportByCode_{destinationCode}",
                () => _objectDirectoryRepository.GetAirportByCode(destinationCode));
        }

        private Multiport GetMultiportByCode(string destinationCode)
        {
            return GetFromCache($"GetMultiportByCode_{destinationCode}",
                () => _objectDirectoryRepository.GetMultiportByCode(destinationCode));
        }

        private T GetFromCache<T>(string key, Func<T> getIfNotExists)
        {
            var cacheKey = key;
            if (!_cache.TryGetValue(cacheKey, out T value))
            {
                lock(key)
                {
                    if (!_cache.TryGetValue(cacheKey, out value))
                    {
                        var cacheEntryOptions = new MemoryCacheEntryOptions()
                            .SetAbsoluteExpiration(TimeSpan.FromMinutes(120));

                        value = getIfNotExists();

                        _cache.Set(cacheKey, value, cacheEntryOptions);
                    }
                }
            }

            return value;
        }
    }
}