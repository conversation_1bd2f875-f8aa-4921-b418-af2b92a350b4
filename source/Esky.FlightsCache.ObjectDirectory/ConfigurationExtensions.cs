using Esky.FlightsStaticData.ObjectDirectory.Abstraction;
using Esky.FlightsStaticData.ObjectDirectory.Mongo.Repositories;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace Esky.FlightsCache.ObjectDirectory
{
    public static class ConfigurationExtensions
    {
        public static IServiceCollection ConfigureObjectDirectory(this IServiceCollection services,
            IConfiguration configuration)
        {
            //Object directory
            var odConfig = configuration.GetSection("MongoObjectDirectory").Get<MongoObjectDirectoryConfiguration>();

            return services
                .AddSingleton<IObjectDirectoryService, ObjectDirectoryService>()
                .AddSingleton<IObjectDirectoryRepository>(
                    new MongoObjectDirectoryRepository(odConfig.ConnectionString, odConfig.DatabaseName));
        }
    }
}