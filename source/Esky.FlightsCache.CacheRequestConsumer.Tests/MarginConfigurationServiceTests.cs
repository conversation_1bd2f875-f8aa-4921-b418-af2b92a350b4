using Esky.FlightsCache.ProviderMapping;
using FluentAssertions;

namespace Esky.FlightsCache.CacheRequestConsumer.Tests;

public class MarginConfigurationServiceTests
{
    private readonly MarginConfigurationService _sut = new();

    public MarginConfigurationServiceTests()
    {
        var config = new List<ProviderMarginConfiguration>
        {
            new()
            {
                ProviderCode = 1,
                Margins =
                [
                    new ProviderMarginConfiguration.ContextMargin
                    {
                        Supplier = "*",
                        AirlineCodes = ["A1"],
                        Margin = new MarginConfiguration { MarginType = MarginType.Relative, Amount = 1 }
                    },
                    new ProviderMarginConfiguration.ContextMargin
                    {
                        Supplier = "s1",
                        AirlineCodes = ["A1"],
                        Margin = new MarginConfiguration { MarginType = MarginType.Relative, Amount = 2 }
                    },
                    new ProviderMarginConfiguration.ContextMargin
                    {
                        Supplier = "s2",
                        AirlineCodes = ["A2", "B2", "C2"],
                        Margin = new MarginConfiguration { MarginType = MarginType.Relative, Amount = 3 }
                    },
                    new ProviderMarginConfiguration.ContextMargin
                    {
                        Supplier = "s3",
                        AirlineCodes = ["A2", "B2"],
                        Margin = new MarginConfiguration { MarginType = MarginType.Absolute, Amount = 5, Currency = "EUR"}
                    },
                ]
            }
        };
        _sut.UpdateMargins(config);
    }

    [Fact]
    public void WhenNoMarginConfigured_ThenReturnZeroMargin()
    {
        _sut.UpdateMargins([]);
        var result = _sut.GetMarginConfiguration(1, null, ["FR"]);
        result.Should().BeEquivalentTo(MarginConfiguration.ZeroMargin);
    }

    [Theory]
    [InlineData(1, "s1", new []{"A1"}, 2)] // s1.A1 precise match
    [InlineData(1, "s2", new []{"A1"}, 1)] // *.A1
    [InlineData(1, "other", new []{"A1"}, 1)] // *.A1
    [InlineData(1, null, new []{"A1"}, 1)] // *.A1
    
    [InlineData(100, "s1", new []{"A1"}, 0)] // provider not matched - no margin
    [InlineData(100, "other", new []{"A1"}, 0)] // provider not matched - no margin
    [InlineData(100, null, new []{"A1"}, 0)] // provider not matched - no margin
    
    [InlineData(1, "s1", new []{"A1", "A2"}, 0)] // airlines partially matched - no margin
    [InlineData(1, "s2", new []{"D1"}, 0)] // airlines not matched - no margin
    [InlineData(1, "s2", new []{"A2"}, 3)] // s2.A2 precise match
    [InlineData(1, "s2", new []{"A2", "B2"}, 3)] // s2.A2,B2 precise match
    [InlineData(1, "s2", new []{"B2", "A2"}, 3)] // s2.A2,B2 precise match
    
    [InlineData(1, "s3", new []{"A2"}, 5)] // precise match, absolute margin type
    public void GetMarginConfiguration_AsExpected(int providerCode, string? supplier, string[] airlines, decimal expectedAmount)
    {
        var margin = _sut.GetMarginConfiguration(providerCode, supplier, airlines);
        
        margin.Amount.Should().Be(expectedAmount);
        
        if (margin.Amount != 0 && margin.MarginType == MarginType.Absolute)
        {
            margin.Currency.Should().NotBeNull();
        }
        else
        {
            margin.Currency.Should().BeNull();
        }
    }
}