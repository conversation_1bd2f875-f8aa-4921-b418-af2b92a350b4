<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>

        <IsPackable>false</IsPackable>
        <IsTestProject>true</IsTestProject>
        <RootNamespace>Esky.FlightsCache.CacheRequestConsumer.Tests</RootNamespace>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="FluentAssertions" Version="7.0.0" />
        <PackageReference Include="Microsoft.AspNetCore.Mvc.Testing" Version="8.0.11" />
        <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.6.0"/>
        <PackageReference Include="NSubstitute" Version="5.3.0" />
        <PackageReference Include="xunit" Version="2.9.3" />
        <PackageReference Include="xunit.runner.visualstudio" Version="3.0.1">
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
            <PrivateAssets>all</PrivateAssets>
        </PackageReference>
        <PackageReference Include="coverlet.collector" Version="6.0.3">
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
            <PrivateAssets>all</PrivateAssets>
        </PackageReference>
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\Esky.FlightsCache.CacheRequestConsumer\Esky.FlightsCache.CacheRequestConsumer.csproj" />
    </ItemGroup>

</Project>
