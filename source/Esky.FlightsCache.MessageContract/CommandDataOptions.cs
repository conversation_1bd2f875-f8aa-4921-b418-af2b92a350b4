namespace Esky.FlightsCache.MessageContract
{
    public class CommandDataOptions
    {
        /// <summary>
        ///     Unikalna nazwa grupy paczki
        /// </summary>
        public string GroupName { get; set; }

        /// <summary>
        ///     Numer paczki
        /// </summary>
        public int PortionId { get; set; }

        /// <summary>
        ///     Opcje kasowania dla paczki lotów
        /// </summary>
        public DeleteOptions DeleteOptions { get; set; }

        /// <summary>
        ///     Pomiń filtrowanie lotów przy zapisie do cache
        /// </summary>
        public bool SkipDataFiltering { get; set; }

        /// <summary>
        ///     Pomiń zapis do bazy SQL
        /// </summary>
        public bool SkipSQLSave { get; set; }
    }
}