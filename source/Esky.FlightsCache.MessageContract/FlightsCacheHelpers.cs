namespace Esky.FlightsCache.Processing.Helpers
{
    public static class FlightCacheHelpers
    {
        public static int GetDestinationHashCode(string airportCode1, string airportCode2)
        {
            var hashCode = 0;
            var charArray = (airportCode1 + airportCode2).ToUpper().ToCharArray();
            for (var i = 0; i < 6; i++)
            {
                hashCode *= 26;
                hashCode += charArray[i] - 'A';
            }

            return hashCode;
        }

        public static short GetAirportHashCode(this string airportCode)
        {
            short hashCode = 0;
            var charArray = airportCode.ToUpper().ToCharArray();
            for (var i = 0; i < 3; i++)
            {
                hashCode *= 26;
                hashCode += (short)(charArray[i] - 'A');
            }

            return hashCode;
        }

        public static string GetAirportCode(this short hashCode)
        {
            return new string(new[]
            {
                (char)((hashCode / 26 / 26 % 26) + 'A'), (char)((hashCode / 26 % 26) + 'A'),
                (char)((hashCode % 26) + 'A')
            });
        }
    }
}