using System;
using System.Collections.Generic;

namespace Esky.FlightsCache.MessageContract
{
    public class FlightCacheSegment
    {
        public DateTime DepartureDate { get; set; }

        public string DepartureCode { get; set; }

        public string ArrivalCode { get; set; }

        public DateTime ArrivalDate { get; set; }

        public string FlightNumber { get; set; }

        public string AirlineCode { get; set; }

        public string OperatingAirlineCode { get; set; }

        public string OperatingFlightNumber { get; set; }

        public FareDetails FareDetails { get; set; }

        public string BookingClass { get; set; }

        public bool IsBaggageIncludedInPrice { get; set; }

        public string AircraftCode { get; set; }

        public List<FlightCacheSegmentStopover> Stopovers { get; set; }

        public string GetOfficeId() => FareDetails?.OfficeId;
    }
}