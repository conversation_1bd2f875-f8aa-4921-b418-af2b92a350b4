namespace Esky.FlightsCache.MessageContract
{
    public class PriceCacheEntry
    {
        public PriceCacheEntry() { }

        public PriceCacheEntry(decimal basePrice, decimal taxPrice, int minimumNumberOPaxes = 1, decimal? marginIncluded = null, decimal? packagesAdditionalMargin = null)
        {
            BasePrice = basePrice;
            TaxPrice = taxPrice;
            MinimumNumberOfPaxes = minimumNumberOPaxes;
            MarginIncluded = marginIncluded;
            PackagesAdditionalMargin = packagesAdditionalMargin;
        }

        public decimal BasePrice { get; set; }
        public decimal TaxPrice { get; set; }
        public int MinimumNumberOfPaxes { get; set; }
        internal bool IsGenerated { get; set; } = false;

        public decimal? MarginIncluded { get; set; }
        public decimal? PackagesAdditionalMargin { get; set; }

        /// <summary>
        /// Adds two PriceCacheEntry instances, accumulating all price components.
        /// The MinimumNumberOfPaxes from the left operand is preserved.
        /// </summary>
        public static PriceCacheEntry operator +(PriceCacheEntry left, PriceCacheEntry right)
        {
            if (left == null && right == null) return null;
            if (left == null) return right;
            if (right == null) return left;

            return new PriceCacheEntry(
                basePrice: left.BasePrice + right.BasePrice,
                taxPrice: left.TaxPrice + right.TaxPrice,
                minimumNumberOPaxes: left.MinimumNumberOfPaxes,
                marginIncluded: (left.MarginIncluded ?? 0) + (right.MarginIncluded ?? 0),
                packagesAdditionalMargin: (left.PackagesAdditionalMargin ?? 0) + (right.PackagesAdditionalMargin ?? 0))
            {
                IsGenerated = left.IsGenerated || right.IsGenerated
            };
        }

        /// <summary>
        /// Creates a new PriceCacheEntry with all values set to zero for the specified minimum number of passengers.
        /// </summary>
        public static PriceCacheEntry Zero()
        {
            return new PriceCacheEntry(0, 0, 0, 0, 0);
        }
    }
}