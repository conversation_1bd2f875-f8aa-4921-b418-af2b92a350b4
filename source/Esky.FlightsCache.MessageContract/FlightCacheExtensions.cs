using Esky.FlightsCache.MessageContract;
using System;
using System.Linq;
using System.Text;

namespace Esky.FlightsCache.Processing.Helpers
{
    public static class FlightCacheExtensions
    {
        public static int GetDestinationHashCode(this FlightCacheLeg leg)
        {
            return FlightCacheHelpers.GetDestinationHashCode(leg.DepartureCode, leg.ArrivalCode);
        }

        public static int GetRevertDestinationHashCode(this FlightCacheLeg leg)
        {
            return FlightCacheHelpers.GetDestinationHashCode(leg.ArrivalCode, leg.DepartureCode);
        }

        public static short GetDepartureHashCode(this FlightCacheLeg leg)
        {
            return leg.DepartureCode.GetAirportHashCode();
        }

        public static short GetArrivalHashCode(this FlightCacheLeg leg)
        {
            return leg.ArrivalCode.GetAirportHashCode();
        }

        public static string GetSegmentsHash(this FlightCacheLeg leg)
        {
            var sr = new StringBuilder();
            var firstDepartureDate = leg.Segments.First().DepartureDate;
            var index = 0;

            foreach (var s in leg.Segments)
            {
                var depDiff = index == 0 ? 0 : (s.DepartureDate.Date - firstDepartureDate.Date).Days;
                var arrDiff = (s.ArrivalDate.Date - firstDepartureDate.Date).Days;

                var coder = new SegmentHashCoder
                {
                    DepartureCode = s.DepartureCode,
                    DepartureTime = s.DepartureDate.ToString("HH:mm"),
                    DepartureDiff = depDiff,
                    ArrivalCode = s.ArrivalCode,
                    ArrivalTime = s.ArrivalDate.ToString("HH:mm"),
                    ArrivalDiff = arrDiff,
                    AirlineCode = s.AirlineCode,
                    OperatingAirlineCode = s.OperatingAirlineCode,
                    FlightNumber = s.FlightNumber,
                    OperatingFlightNumber = s.OperatingFlightNumber,
                    BookingClass = s.BookingClass,
                    FareDetails = new FareDetails
                    {
                        FareCode = s.FareDetails?.FareCode,
                        OfficeId = s.FareDetails?.OfficeId,
                        OfferId = s.FareDetails?.OfferId
                    }
                };
                sr.Append(coder);
                sr.Append("|");

                index++;
            }

            return sr.ToString();
        }

        public static bool IsRoundTrip(this FlightCache f)
        {
            return f.Legs.Count == 2 && !(f.LegsCanBeUseSeparately && f.Legs.TrueForAll(l => l.AdultPrices?.Any() == true));
        }

        public static bool IsOpenJaw(this FlightCache f)
        {
            return f.IsRoundTrip() &&
                   (!AreAirportCodesEqual(f.Legs[0].DepartureCode, f.Legs[1].ArrivalCode) ||
                    !AreAirportCodesEqual(f.Legs[0].ArrivalCode, f.Legs[1].DepartureCode));
        }

        private static bool AreAirportCodesEqual(string first, string second)
        {
            return string.Equals(first, second, StringComparison.OrdinalIgnoreCase);
        }
    }
}