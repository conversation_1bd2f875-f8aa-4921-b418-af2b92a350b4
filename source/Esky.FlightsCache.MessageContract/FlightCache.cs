using System;
using System.Collections.Generic;

namespace Esky.FlightsCache.MessageContract
{
    public class FlightCache
    {
        public int Id { get; set; }

        public int ProviderCode { get; set; }
 
        internal int ReadProviderCode { get; set; }
        
        internal int OriginalProviderCode { get; set; }

        public string ValidatingCarrier { get; set; }

        public string SessionId { get; set; }

        public DateTime SearchDate { get; set; }

        public string SearchId { get; set; }

        public bool LegsCanBeUseSeparately { get; set; }

        public DateTime ExpirationDate { get; set; }

        public string Supplier { get; set; }

        public List<FlightCacheLeg> Legs { get; set; }
    }
}