using Esky.FlightsCache.MessageContract;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Esky.FlightsCache.Processing.Helpers
{
    public class SegmentHashCoder
    {
        public SegmentHashCoder(string hash)
        {
            ParseHash(hash);
        }

        public SegmentHashCoder()
        {
        }

        public string DepartureCode { get; set; }

        public int DepartureDiff { get; set; }

        public string DepartureTime { get; set; }

        public string ArrivalCode { get; set; }

        public int ArrivalDiff { get; set; }

        public string ArrivalTime { get; set; }

        public string AirlineCode { get; set; }

        public string OperatingAirlineCode { get; set; }

        public FareDetails FareDetails { get; set; }

        public string BookingClass { get; set; }

        public string FlightNumber { get; set; }

        public string OperatingFlightNumber { get; set; }

        private void ParseHash(string hash)
        {
            var options = hash.Split(new string[1] { "," }, StringSplitOptions.None);

            if (options.Length != 8)
            {
                throw new Exception("Segment hash format error: " + hash);
            }

            DepartureCode = Split(options[0])[0];
            DepartureDiff = Convert.ToInt32(Split(options[0])[1]);
            DepartureTime = Split(options[0])[2];

            ArrivalCode = Split(options[1])[0];
            ArrivalDiff = Convert.ToInt32(Split(options[1])[1]);
            ArrivalTime = Split(options[1])[2];

            AirlineCode = options[2];
            OperatingAirlineCode = options[3];
            FareDetails = SplitFareCode(options[4]);
            BookingClass = options[5];
            FlightNumber = options[6];
            OperatingFlightNumber = options[7];
        }

        private List<string> Split(string s)
        {
            return s.Split(new string[1] { "^" }, StringSplitOptions.None).ToList();
        }

        private string GetFareCode()
        {
            return $"{FareDetails?.FareCode}@{FareDetails?.OfficeId}#{FareDetails?.OfferId}";
        }

        private static FareDetails SplitFareCode(string fare)
        {
            var elements = fare.Split('@', '#');
            return new FareDetails
            {
                FareCode = elements.Any() ? elements[0] : null,
                OfficeId = elements.Length > 1 ? elements[1] : null,
                OfferId = elements.Length > 2 ? elements[2] : null,
            };
        }

        public override string ToString()
        {
            var sr = new StringBuilder();

            sr.Append(string.Format("{0}^{1}^{2},{3}^{4}^{5},{6},{7},{8},{9},{10},{11}",
                DepartureCode,
                DepartureDiff,
                DepartureTime,
                ArrivalCode,
                ArrivalDiff,
                ArrivalTime,
                AirlineCode,
                OperatingAirlineCode,
                GetFareCode(),
                BookingClass,
                FlightNumber,
                OperatingFlightNumber
            ));

            return sr.ToString();
        }
    }
}