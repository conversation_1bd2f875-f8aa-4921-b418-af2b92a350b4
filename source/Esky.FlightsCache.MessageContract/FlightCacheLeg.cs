using System;
using System.Collections.Generic;

namespace Esky.FlightsCache.MessageContract
{
    public class FlightCacheLeg
    {
        public string DepartureCode { get; set; }

        public string ArrivalCode { get; set; }

        public DateTime DepartureDate { get; set; }

        public DateTime ArrivalDate { get; set; }

        public string AirlineCode { get; set; }

        public List<PriceCacheEntry> AdultPrices { get; set; }
        public List<PriceCacheEntry> YouthPrices { get; set; }
        public List<PriceCacheEntry> ChildPrices { get; set; }
        public List<PriceCacheEntry> InfantPrices { get; set; }
        public Dictionary<string, PriceCacheEntry> RawPriceComponents { get; set; }

        public string CurrencyCode { get; set; }

        public decimal ConversionRatioToReferenceCurrency { get; set; } = decimal.One;

        public List<FlightCacheSegment> Segments { get; set; }

        public SeparationOptions SeparationOptions { get; set; }

        public string[] DepartureSearchCodes { get; set; }
        public string[] ArrivalSearchCodes { get; set; }

        public AirportDetails DepartureAirportDetails { get; set; }
        public AirportDetails ArrivalAirportDetails { get; set; }

        public int? AvailableSeatsCount { get; set; }
        
        public int? TotalFaresLeft { get; set; }

        public DateTime? DataTimestamp { get; set; }
        
        public TimeSpan FlightTime { get; set; }
    }
}