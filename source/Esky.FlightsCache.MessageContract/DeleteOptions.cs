using System;
using System.Collections.Generic;

namespace Esky.FlightsCache.MessageContract
{
    public class DeleteOptions
    {
        /// <summary>
        ///     <PERSON><PERSON> opcja kasowania jest włączona
        /// </summary>
        public bool IsEnabled { get; set; }

        /// <summary>
        ///     Rod<PERSON>j kasowania jaki ma być zastosowany
        /// </summary>
        public TypeOfDisposalEnum Type { get; set; }

        /// <summary>
        ///     Lista dostawców dla których odbędzie się kasowanie
        ///     Jeżeli null, kody dostawców do usunięcia brane z lotów
        /// </summary>
        public List<int> ProviderCodes { get; set; }
        
        /// <summary>
        ///     Lista linii lotniczych dla których odbędzie się kasowanie
        /// </summary>
        public List<string> AirlineCodes { get; set; }

        /// <summary>
        ///     Min. Data wylotu dla których odbędzie się kasowanie
        /// </summary>
        public DateTime DeleteDepartureDayFrom { get; set; }

        public DateTime DeleteDepartureDayTo { get; set; }

        public DateTime? DeleteReturnDepartureDayFrom { get; set; }

        public DateTime? DeleteReturnDepartureDayTo { get; set; }
    }
}