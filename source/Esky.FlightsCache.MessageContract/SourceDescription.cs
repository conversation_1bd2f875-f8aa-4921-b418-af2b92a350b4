using System;

namespace Esky.FlightsCache.MessageContract
{
    public class SourceDescription
    {
        public string Name { get; set; }

        public string MachineName { get; set; }

        public string SearchDepartureCode { get; set; }

        public string SearchArrivalCode { get; set; }

        public DateTime SearchDepartureDate { get; set; }

        public DateTime? SearchReturnDepartureDate { get; set; }

        public DateTime? SendDate { get; set; }

        public int Flex { get; set; }

        public string Provider { get; set; }
        
        public int? ProviderCode { get; set; }
        
        public string Supplier { get; set; }

        public string PartnerCode { get; set; }

        public string PaxConfiguration { get; set; }
        
        public int[] PaxAges { get; set; }

        public string SessionId { get; set; }

        public string AdditionalInformations { get; set; }

        public int? MaxStay { get; set; }
    }
}