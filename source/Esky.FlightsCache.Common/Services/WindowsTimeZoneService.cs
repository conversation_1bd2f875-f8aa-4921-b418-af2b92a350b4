using System;

namespace Esky.FlightsCache.Common.Services
{
    public class WindowsTimeZoneService : ITimeZoneService
    {
        private const string UtcTimeZoneName = "UTC";

        public DateTime GetLocalTime(DateTime utcDateTime, string timezoneId)
        {
            return TimeZoneInfo.ConvertTimeFromUtc(utcDateTime, TimeZoneInfo.FindSystemTimeZoneById(timezoneId));
        }

        public TimeSpan GetTimeDifference(DateTime dateTimeA, string timeZoneA, DateTime dateTimeB, string timeZoneB)
        {
            var arrivalDateInUtc = TimeZoneInfo.ConvertTimeBySystemTimeZoneId(dateTimeA, timeZoneA, UtcTimeZoneName);
            var departureDateInUtc = TimeZoneInfo.ConvertTimeBySystemTimeZoneId(dateTimeB, timeZoneB, UtcTimeZoneName);

            return arrivalDateInUtc - departureDateInUtc;
        }
    }
}