using System;

namespace Esky.FlightsCache.Common.Services;

public class TimeZoneService : ITimeZoneService
{
    public DateTime GetLocalTime(DateTime utcDateTime, string timezoneId)
    {
        var local = TimeZoneInfo.ConvertTimeFromUtc(utcDateTime, TimeZoneInfo.FindSystemTimeZoneById(timezoneId));
        return local;
    }

    public TimeSpan GetTimeDifference(DateTime dateTimeA, string timeZoneA, DateTime dateTimeB, string timeZoneB)
    {
        var arrivalDateInUtc = TimeZoneInfo.ConvertTimeToUtc(dateTimeA, TimeZoneInfo.FindSystemTimeZoneById(timeZoneA));
        var departureDateInUtc = TimeZoneInfo.ConvertTimeToUtc(dateTimeB, TimeZoneInfo.FindSystemTimeZoneById(timeZoneB));

        return arrivalDateInUtc - departureDateInUtc;
    }
}