using System;
using TimeZoneConverter;

namespace Esky.FlightsCache.Common.Services
{
    public class LinuxTimeZoneService : ITimeZoneService
    {
        private const string UtcTimeZoneName = "UTC";

        public DateTime GetLocalTime(DateTime utcDateTime, string timezoneId)
        {
            return TimeZoneInfo.ConvertTimeFromUtc(utcDateTime,
                TimeZoneInfo.FindSystemTimeZoneById(TZConvert.WindowsToIana(timezoneId)));
        }

        public TimeSpan GetTimeDifference(DateTime dateTimeA, string timeZoneA, DateTime dateTimeB, string timeZoneB)
        {
            var arrivalDateInUtc =
                TimeZoneInfo.ConvertTimeBySystemTimeZoneId(dateTimeA, TZConvert.WindowsToIana(timeZoneA),
                    UtcTimeZoneName);
            var departureDateInUtc =
                TimeZoneInfo.ConvertTimeBySystemTimeZoneId(dateTimeB, TZConvert.WindowsToIana(timeZoneB),
                    UtcTimeZoneName);

            return arrivalDateInUtc - departureDateInUtc;
        }
    }
}