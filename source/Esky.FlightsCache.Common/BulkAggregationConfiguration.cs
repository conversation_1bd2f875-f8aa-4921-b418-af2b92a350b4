using Esky.FlightsCache.Common.Model;

namespace Esky.FlightsCache.Common
{
    public class BulkAggregationConfiguration
    {
        public BulkAggregationConfiguration()
        {
            IntervalInMinutes = 5;
            MaxElementsToProcess = 100_000;
            MaxBufferLength = 10_000_000;
            ProcessingTimeoutInMinutes = 45;
            IsEnabledForOneWay = false;
            GroupingMode = BulkAggregationGroupingMode.RouteAndConsecutiveMonths;
        }

        public bool IsEnabledForOneWay { get; set; }

        public int IntervalInMinutes { get; set; }

        public int ProcessingTimeoutInMinutes { get; set; }

        public int MaxElementsToProcess { get; set; }

        public int MaxBufferLength { get; set; }

        public BulkAggregationGroupingMode GroupingMode { get; set; }
    }
}