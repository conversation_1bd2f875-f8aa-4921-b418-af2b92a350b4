namespace Esky.FlightsCache.Common
{
    public class RedisCachingConfiguration
    {
        public RedisCachingConfiguration()
        {
            ConnectionString =
                "redis-general-master.default.svc.cluster.local.:6379,password=k8rBZeXosd,asyncTimeout=120000";
            InstanceName = "FlightsCache";
        }

        public string ConnectionString { get; set; }

        public string InstanceName { get; set; }
    }
}