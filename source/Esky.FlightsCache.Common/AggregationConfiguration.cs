namespace Esky.FlightsCache.Common
{
    public class AggregationConfiguration
    {
        public bool IsGlobalMultiticketAggregationDisabled { get; set; }
        public bool IsProviderMultiticketAggregationDisabled { get; set; }
        public bool IsOneWayAggregationDisabled { get; set; }

        public bool IsMultiticketAggregationDisabled =>
            IsGlobalMultiticketAggregationDisabled && IsProviderMultiticketAggregationDisabled;

        public bool IsAggregationFromOwDisabled => IsMultiticketAggregationDisabled && IsOneWayAggregationDisabled;

        public bool IsRoundTripAggregationDisabled { get; set; }

        public int DegreeOfParallelism { get; set; }

        public BulkAggregationConfiguration BulkAggregation { get; set; } = new();
    }
}