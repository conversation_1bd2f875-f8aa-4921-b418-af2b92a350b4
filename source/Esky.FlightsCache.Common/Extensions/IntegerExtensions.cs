using System.Collections.Generic;

namespace Esky.FlightsCache.Common.Extensions
{
    public static class IntegerExtensions
    {
        public static string ToRangeString(this IEnumerable<int> values)
        {
            return string.Concat(GetIntRangeParts(values));
        }

        private static IEnumerable<string> GetIntRangeParts(IEnumerable<int> values)
        {
            (int Start, int End)? currentRange = null;
            foreach (var i in values)
            {
                if (currentRange.HasValue)
                {
                    if (i == currentRange.Value.End + 1)
                    {
                        if (currentRange.Value.Start == currentRange.Value.End)
                        {
                            yield return "-";
                        }

                        currentRange = (currentRange.Value.End, i);
                        continue;
                    }

                    if (i >= currentRange.Value.Start && i <= currentRange.Value.End)
                    {
                        continue;
                    }

                    if (currentRange.Value.Start < currentRange.Value.End)
                    {
                        yield return $"{currentRange.Value.End}";
                    }

                    yield return ", ";
                }

                yield return $"{i}";
                currentRange = (i, i);
            }

            if (currentRange.HasValue && currentRange.Value.Start < currentRange.Value.End)
            {
                yield return $"{currentRange.Value.End}";
            }
        }
    }
}