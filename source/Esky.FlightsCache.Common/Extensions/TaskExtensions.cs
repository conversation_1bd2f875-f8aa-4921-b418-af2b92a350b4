using System.Collections.Generic;
using System.Threading.Tasks;

namespace Esky.FlightsCache.Common.Extensions
{
    public static class TaskExtensions
    {
        public static Task<TResult[]> WhenAll<TResult>(this IEnumerable<Task<TResult>> tasks)
        {
            return Task.WhenAll(tasks);
        }

        public static Task WhenAll(this IEnumerable<Task> tasks)
        {
            return Task.WhenAll(tasks);
        }
    }
}