using System;
using System.Collections.Generic;
using System.Linq;

namespace Esky.FlightsCache.Common.Extensions
{
    public static class DateTimeExtensions
    {
        public static DateTime AsSqlSmallDateTime(this DateTime dateTime)
        {
            return dateTime.AddTicks(-(dateTime.Ticks % TimeSpan.TicksPerSecond));
        }
        
        public static DateTime AsDateTimeWithoutSeconds(this DateTime dateTime)
        {
            return dateTime.AddTicks(-(dateTime.Ticks % TimeSpan.TicksPerMinute));
        }

        public static DateTime AsIsoDate(this DateTime dateTime)
        {
            return new DateTime(dateTime.Year, dateTime.Month, dateTime.Day);
        }

        public static DateTime Min(DateTime date1, DateTime? date2)
        {
            return !date2.HasValue || date1 < date2 ? date1 : date2.Value;
        }

        public static DateTime Max(DateTime date1, DateTime? date2)
        {
            return !date2.HasValue || date1 > date2 ? date1 : date2.Value;
        }

        public static DateTime BeginningOfTheMonth(this DateTime dateTime)
        {
            return new DateTime(dateTime.Year, dateTime.Month, 1);
        }

        public static DateTime EndOfTheMonth(this DateTime dateTime)
        {
            return new DateTime(dateTime.Year, dateTime.Month, DateTime.DaysInMonth(dateTime.Year, dateTime.Month));
        }

        public static IEnumerable<DateTime> Range(this DateTime startDate, DateTime endDate)
        {
            return Enumerable.Range(0, endDate.Subtract(startDate).Days + 1).Select(o => startDate.AddDays(o));
        }
    }
}