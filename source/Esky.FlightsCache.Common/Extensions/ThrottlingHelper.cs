using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace Esky.FlightsCache.Common.Extensions
{
    public static class ThrottlingHelper
    {
        public static async Task ForEachThrottledAsync<T>(
            this IEnumerable<T> source,
            int degreeOfParallelism,
            Func<T, Task> action)
        {
            var tasks = new List<Task>();
            using (var throttler = new SemaphoreSlim(Math.Max(degreeOfParallelism, 1)))
            {
                foreach (var element in source)
                {
                    await throttler.WaitAsync();
                    tasks.Add(Task.Run(async () =>
                    {
                        try
                        {
                            await action(element);
                        }
                        finally
                        {
                            throttler.Release();
                        }
                    }));
                }

                await Task.WhenAll(tasks);
            }
        }
    }
}