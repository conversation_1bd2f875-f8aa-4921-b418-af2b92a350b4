using System;
using System.Collections.Generic;
using System.Linq;

namespace Esky.FlightsCache.Common.Extensions
{
    public static class EnumerableExtensions
    {
        public static IEnumerable<T> Concat<T>(params IEnumerable<T>[] sequences)
        {
            return sequences.SelectMany(x => x);
        }

        public static IEnumerable<TSource> GetMinElementByGroup<TSource, TKey>(this IEnumerable<TSource> source,
            Func<TSource, TKey> partitionSelector,
            params Func<TSource, IComparable>[] selectorsToComparableFields)
        {
            TSource Accumulate(TSource cheapest, TSource next)
            {
                if (cheapest == null)
                {
                    return next;
                }

                foreach (var selector in selectorsToComparableFields)
                {
                    var compareResult = selector(next).CompareTo(selector(cheapest));
                    if (compareResult < 0)
                    {
                        return next;
                    }

                    if (compareResult > 0)
                    {
                        return cheapest;
                    }
                    //if (compareResult == 0) continue;
                }

                return cheapest;
            }

            return source
                .GroupBy(partitionSelector)
                .Select(g => g.Aggregate(Accumulate));
        }
    }
}