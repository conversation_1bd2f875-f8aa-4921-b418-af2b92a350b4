using System.Collections.Generic;

namespace Esky.FlightsCache.PartnerSettings
{
    public class ProviderRobotsSettings : BaseProviderRobotsSettings
    {
        public bool ForceSSCRobotSearchIfAirportCurrenciesAreDifferent { get; set; }

        public int ConcurrentRequestsPerThread { get; set; }

        public int RegularProxiesPercentage { get; set; }

        public int NumberOfPaxToGet { get; set; }
        public int MaxNumberOfSearchesInParallel { get; set; }
        public bool UseMobileApiForSearching { get; set; }
        public bool UseWebApiForSearching { get; set; }
        public bool UseWebApiForGettingTimetable { get; set; }
        public bool UseTimetableService { get; set; }

        public bool UseAngularApi { get; set; }
        public int NumberOfMonthsToGet { get; set; }
        public bool StoreToSSCCache { get; set; }
        public bool UseExtendedSearchLogger { get; set; }

        public bool FilterFlightsByCalendar { get; set; }
        public bool ValidateFlights { get; set; }
        public bool ValidateWithReturnFlights { get; set; }
        public decimal PriceValidationThreshold { get; set; }

        public int RobotProviderCode { get; set; }

        /// <summary>
        ///     Flaga informująca czy robot ma korzystać z całej siatki połaczeń czy tylko z tras zdefinowanych jako okazje
        /// </summary>
        public bool ShouldScanEntireConnectionNetwork { get; set; }

        /// <summary>
        ///     Flaga włączająca predefiniowane połączenia
        /// </summary>
        public bool ShouldScanDefinedRoutes { get; set; }

        /// <summary>
        ///     Flaga oznaczająca wyszukiwanie po wszystkich typach w rozkładach lotów
        /// </summary>
        public bool UseAllTimelineRouteTypes { get; set; }

        public IList<string> AirlineCodes { get; set; }
        public IList<string> HPAirlineCodes { get; set; }

        public bool AllowCalendarFlowForSSCRobot { get; set; }

        public string FareCodePostfix { get; set; }

        public bool BrowserRobotsEnabled { get; set; }

        public int BrowserRobotsDaysToSearch { get; set; }
        public int BrowserRobotsStartDayOffset { get; set; }
    }
}