using System.Collections.Generic;
using System.Threading.Tasks;

namespace Esky.FlightsCache.PartnerSettings
{
    public class PartnerSettingsService : IPartnerSettingsService
    {
        private readonly Framework.PartnerSettings.IPartnerSettingsService _service;

        public PartnerSettingsService(Framework.PartnerSettings.IPartnerSettingsService service)
        {
            _service = service;
        }

        public async Task<PartnerSettingsModel> GetPartnerSettingsAsync(string partnerCode)
        {
            return await _service.GetSettingAsync("PartnerSettingsModel",
                x => x.GetPartnerSettings<PartnerSettingsModel>(partnerCode), partnerCode);
        }

        public async Task<IEnumerable<int>> GetActiveProvidersAsync(string partnerCode)
        {
            var result = await _service.GetSettingAsync("ActiveProviders", x => x.GetActiveProviders(partnerCode),
                partnerCode);
            return result.List;
        }
    }
}