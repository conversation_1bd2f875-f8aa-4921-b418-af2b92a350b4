using System.Collections.Generic;

namespace Esky.FlightsCache.PartnerSettings
{
    public class RobotsConfiguration
    {
        /// <summary>
        ///     Konfiguracja per provider
        /// </summary>

        public IDictionary<string, ProviderRobotsSettings> ProviderConfigurations
        {
            get;
            set;
        }

        /// <summary>
        ///     Liczba dni do przeszukania
        /// </summary>
        public int NumberOfDaysForward
        {
            get;
            set;
        }

        /// <summary>
        ///     Liczba dni do przeszukania, częściej niż zwykle
        /// </summary>
        public int NumberOfDaysForwardWithHigherPriority
        {
            get;
            set;
        }

        /// <summary>
        ///     Liczba dni do przeszukania dla odcinka powrotnego
        ///     Liczba powrotow dla kazdego dnia wylotu
        /// </summary>
        public int NumberOfReturnDaysForEachDepartureDay
        {
            get;
            set;
        }

        /// <summary>
        ///     Flaga informująca czy parsowanie dla tego partnera jest włączone
        /// </summary>
        public bool IsEnable
        {
            get;
            set;
        }

        public bool LogResponseFromProvider { get; set; }

        public List<string> DefaultProcessingNodeNames { get; set; }

        public int DefaultNumberOfProcessingThreads { get; set; }

        /// <summary>
        ///     Procent najtańszych ofert do sprawdzenia na wybranej trasie
        /// </summary>
        public int VolatileOffersToCheckPercent { get; set; }

        /// <summary>
        ///     Minimalna liczba najtańszych ofert do sprawdzenia na wybranej trasie
        /// </summary>
        public int VolatileOffersToCheckMinCount { get; set; }
    }
}