namespace Esky.FlightsCache.PartnerSettings
{
    public class FlightsMixingSettings
    {
        public int[] ProvidersToGetDirectlyFromPromoCache { get; set; }

        public int[] ProvidersToUseWithMixing { get; set; }

        public bool UseNearbyAirports { get; set; }

        public int NumberOfCheapestRecordsToReturn { get; set; }

        public int NumberOf3LegFlightsToReturn { get; set; }

        public int MaxNumberOfTickets { get; set; }

        public string PriceCalculationParameters { get; set; }
        public int ViProviderCode { get; set; }
    }
}