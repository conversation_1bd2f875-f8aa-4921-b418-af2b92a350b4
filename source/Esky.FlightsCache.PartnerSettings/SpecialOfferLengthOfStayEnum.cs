using System.Runtime.Serialization;

namespace Esky.FlightsCache.PartnerSettings
{
    public enum SpecialOfferLengthOfStayEnum
    {
        /// <summary>
        ///     Any day 30 days forward
        /// </summary>
        [EnumMember] Any = 0,

        /// <summary>
        ///     From friday do sunday
        /// </summary>
        [EnumMember] Weekends = 1,

        /// <summary>
        ///     0 to 3 days
        /// </summary>
        [EnumMember] Range0To3Days = 2,

        /// <summary>
        ///     4 to 7 days
        /// </summary>
        [EnumMember] Range4To7Days = 3,

        /// <summary>
        ///     8 to 14 days
        /// </summary>
        [EnumMember] Range8To14Days = 4,

        /// <summary>
        ///     15 to 21 days
        /// </summary>
        [EnumMember] Range15To21Days = 5,

        /// <summary>
        ///     Continental - 1 to 14 days, Intercontinental - 7 to 30 days
        /// </summary>
        [EnumMember] Custom = 6
    }
}