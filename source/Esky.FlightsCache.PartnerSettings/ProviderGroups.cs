using Esky.Framework.PartnerSettings.Enums;
using System.Collections.Generic;

namespace Esky.FlightsCache.PartnerSettings
{
    public static class ProviderGroups
    {
        /// <summary>
        ///     Provider code to provider group mapping. Provider is assumed to be low-cost if not mapped.
        /// </summary>
        private static readonly Dictionary<ProviderCodeEnum, ProviderGroupEnum> ProviderGroupDictionary = new()
        {
            { ProviderCodeEnum.Amadeus, ProviderGroupEnum.GDS },
            // TODO: consult mappings below with Gracjan before deploying it to PRO
            // { ProviderCodeEnum.AmadeusNdc, ProviderGroupEnum.GDS },
            // { ProviderCodeEnum.LufthansaNdc, ProviderGroupEnum.GDS },
            // { ProviderCodeEnum.LatamNdc, ProviderGroupEnum.GDS },
            { ProviderCodeEnum.NMWorldSpan, ProviderGroupEnum.GDS },
            { ProviderCodeEnum.WorldSpan, ProviderGroupEnum.GDS },
            { ProviderCodeEnum.TPWorldSpan, ProviderGroupEnum.GDS },
            { ProviderCodeEnum.Sabre, ProviderGroupEnum.GDS },
            { ProviderCodeEnum.AviankaCions, ProviderGroupEnum.LocalCarier },
            { ProviderCodeEnum.GOL, ProviderGroupEnum.LocalCarier },
            { ProviderCodeEnum.MySky, ProviderGroupEnum.LocalCarier },
            { ProviderCodeEnum.PassaredoCions, ProviderGroupEnum.LocalCarier },
            { ProviderCodeEnum.TAM, ProviderGroupEnum.LocalCarier },
            { ProviderCodeEnum.Trip, ProviderGroupEnum.LocalCarier },
            { ProviderCodeEnum.WebJetCions, ProviderGroupEnum.LocalCarier },
            { ProviderCodeEnum.Azul, ProviderGroupEnum.LocalCarier },
            { ProviderCodeEnum.Avianka, ProviderGroupEnum.LocalCarier },
            { ProviderCodeEnum.SeteCions, ProviderGroupEnum.LocalCarier },
            { ProviderCodeEnum.MapCions, ProviderGroupEnum.LocalCarier },
            { ProviderCodeEnum.SunExpressCrane, ProviderGroupEnum.LocalCarier },
            { ProviderCodeEnum.FlyPgsCrane, ProviderGroupEnum.LocalCarier },
            { ProviderCodeEnum.BoraJetCrane, ProviderGroupEnum.LocalCarier },
            { ProviderCodeEnum.OnurAirCrane, ProviderGroupEnum.LocalCarier },
            { ProviderCodeEnum.OnurAirCraneNew, ProviderGroupEnum.LocalCarier },
            { ProviderCodeEnum.AtlasJet, ProviderGroupEnum.LocalCarier },
            { ProviderCodeEnum.Kiu, ProviderGroupEnum.LocalCarier },
            { ProviderCodeEnum.BlueAirNavitare, ProviderGroupEnum.LocalCarier },
            { ProviderCodeEnum.MDSCharters, ProviderGroupEnum.Charter },
            { ProviderCodeEnum.TuiCharters, ProviderGroupEnum.Charter },
            { ProviderCodeEnum.Mixed, ProviderGroupEnum.Other }
        };

        /// <summary>
        ///     Gets the group of the provider specified by the <paramref name="providerCode" />.
        /// </summary>
        /// <param name="providerCode">The provider code.</param>
        /// <returns></returns>
        public static ProviderGroupEnum GetProviderGroup(ProviderCodeEnum providerCode)
        {
            ProviderGroupEnum providerGroup;

            return ProviderGroupDictionary.TryGetValue(providerCode, out providerGroup)
                ? providerGroup
                : ProviderGroupEnum.LowCost;
        }

        /// <summary>
        ///     Determines whether the provider specified by the <paramref name="providerCode" /> is a GDS provider.
        /// </summary>
        /// <param name="providerCode">The provider code.</param>
        /// <returns>
        ///     <c>true</c> if the provider specified by the <paramref name="providerCode" /> is a GDS provider; otherwise,
        ///     <c>false</c>.
        /// </returns>
        public static bool IsGdsProvider(ProviderCodeEnum providerCode)
        {
            ProviderGroupEnum providerGroup;

            return ProviderGroupDictionary.TryGetValue(providerCode, out providerGroup) &&
                   providerGroup == ProviderGroupEnum.GDS;
        }

        /// <summary>
        ///     Determines whether the provider specified by the <paramref name="providerCode" /> is a local carrier provider.
        /// </summary>
        /// <param name="providerCode">The provider code.</param>
        /// <returns>
        ///     <c>true</c> if the provider specified by the <paramref name="providerCode" /> is a local carrier provider;
        ///     otherwise, <c>false</c>.
        /// </returns>
        public static bool IsLccProvider(ProviderCodeEnum providerCode)
        {
            ProviderGroupEnum providerGroup;

            return ProviderGroupDictionary.TryGetValue(providerCode, out providerGroup) &&
                   providerGroup == ProviderGroupEnum.LocalCarier;
        }

        /// <summary>
        ///     Determines whether the provider specified by the <paramref name="providerCode" /> is a low cost provider.
        /// </summary>
        /// <param name="providerCode">The provider code.</param>
        /// <returns>
        ///     <c>true</c> if the provider specified by the <paramref name="providerCode" /> is a low cost provider; otherwise,
        ///     <c>false</c>.
        /// </returns>
        public static bool IsLowCostProvider(ProviderCodeEnum providerCode)
        {
            return !ProviderGroupDictionary.ContainsKey(providerCode);
        }
    }
}