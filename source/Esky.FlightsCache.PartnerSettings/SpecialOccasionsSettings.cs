using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Xml.Serialization;

namespace Esky.FlightsCache.PartnerSettings
{
    [Serializable]
    public class CacheTimeOutProviderItem
    {
        public CacheTimeOutProviderItem()
        {
            TimeOutConfigurations = [];
            ProviderCodes = [];
        }

        public List<int> ProviderCodes { get; set; }

        public List<TimeOutFlightConfiguration> TimeOutConfigurations { get; set; }

        public int GetTimeOutForDepartureDate(DateTime departureDate)
        {
            if (TimeOutConfigurations == null || !TimeOutConfigurations.Any())
            {
                return CacheTimeOutConfiguration.DefaultTimeOutInHours;
            }

            if (departureDate < DateTime.Now)
            {
                return 0;
            }

            var daysToDeparture = (departureDate - DateTime.Now).TotalDays;

            foreach (var t in TimeOutConfigurations)
            {
                var min = t.MinDaysToDeparture ?? int.MinValue;
                var max = t.MaxDaysToDeparture ?? int.MaxValue;

                if (min < daysToDeparture && daysToDeparture <= max)
                {
                    return t.TimeOutInHours;
                }
            }

            return
                CacheTimeOutConfiguration
                    .DefaultTimeOutInHours; //Jeden dzień, domyślny tiemout jeżeli niezdefinowany dla providera
        }
    }

    [Serializable]
    public class TimeOutFlightConfiguration
    {
        public int? MinDaysToDeparture { get; set; }

        public int? MaxDaysToDeparture { get; set; }

        public int TimeOutInHours { get; set; }
    }

    public class CacheTimeOutConfiguration
    {
        public const int DefaultTimeOutInHours = 24;

        public CacheTimeOutConfiguration()
        {
            ProvidersConfiguration = [];
        }

        public List<CacheTimeOutProviderItem> ProvidersConfiguration { get; set; }

        public int GetTimeOutInHoursForProvider(int providerCode, DateTime departureDate)
        {
            if (ProvidersConfiguration == null || !ProvidersConfiguration.Any() ||
                !ProvidersConfiguration.Any(s => s.ProviderCodes.Any(s1 => s1 == providerCode)))
            {
                return DefaultTimeOutInHours;
            }

            return ProvidersConfiguration.First(s => s.ProviderCodes.Any(s1 => s1 == providerCode))
                .GetTimeOutForDepartureDate(departureDate);
        }
    }

    public class SpecialOccasionsSettings
    {
        private CacheTimeOutConfiguration _cacheTimeOutConfiguration;

        public SpecialOccasionsSettings()
        {
            SpecialOccasionsFromCacheSettings = new SpecialOccasionsFromCacheSettings();
        }

        public bool IsTFForSpecialOffersEnabled { get; set; }
        public SpecialOccasionsFromCacheSettings SpecialOccasionsFromCacheSettings { get; set; }
        public RobotsConfiguration RobotsConfiguration { get; set; }
        public string SpecialOccasionsCalendarCurrencyCode { get; set; }
        public string CacheTimeOutConfiguration { get; set; }
        public List<string> PartnerCodes { get; set; }
        public string DisallowedOfficeIdRegexPattern { get; set; }

        public CacheTimeOutConfiguration GetCacheTimeOutConfiguration()
        {
            if (string.IsNullOrWhiteSpace(CacheTimeOutConfiguration))
            {
                return new CacheTimeOutConfiguration();
            }

            if (_cacheTimeOutConfiguration == null)
            {
                var clearedConfigurationXml = CacheTimeOutConfiguration.Replace("ProviderCodeEnum", "int");
                _cacheTimeOutConfiguration = Deserialize<CacheTimeOutConfiguration>(clearedConfigurationXml);
            }

            return _cacheTimeOutConfiguration;
        }

        private T Deserialize<T>(string value)
        {
            var serializer = new XmlSerializer(typeof(T));
            using (var sr = new StringReader(value))
            {
                return (T)serializer.Deserialize(sr);
            }
        }
    }
}