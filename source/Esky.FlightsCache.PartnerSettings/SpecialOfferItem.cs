using System;
using System.Diagnostics;

namespace Esky.FlightsCache.PartnerSettings
{
    [DebuggerDisplay("{DebuggerDisplay,nq}")]
    public class SpecialOfferItem
    {
        public string DepartureCode { get; set; }
        public string ArrivalCode { get; set; }
        public SpecialOfferType OfferType { get; set; }
        public string AirlineCode { get; set; }
        public DateTime DepartureDateFrom { get; set; }
        public DateTime? DepartureDateTo { get; set; }
        public bool IsActive { get; set; }
        public int Id { get; set; }

        private string DebuggerDisplay => $"{Id}) {DepartureCode}-{ArrivalCode} @{AirlineCode}";
    }
}