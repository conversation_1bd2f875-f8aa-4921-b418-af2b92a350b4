namespace Esky.FlightsCache.PartnerSettings
{
    public class SpecialOccasionsFromCacheSettings
    {
        /// <summary>
        ///     Liczba minut po jakim cache dla requestu o podanych parametrach zostanie usunięty
        /// </summary>
        public int CacheTimeOutInMinutes { get; set; }

        /// <summary>
        ///     Liczba najtańszych lotów danego dnia, zgrupowane per liczba dni do powrotu
        /// </summary>
        public int NumberOfCheapestFlightsPerGroup { get; set; }

        /// <summary>
        ///     Liczba najlepszych lotów VI danego dnia
        /// </summary>
        public int NumberOfVIFlightsPerRoute { get; set; }

        /// <summary>
        ///     Maksymalna data do jakiej mają być pobierane okazje. Domyślnie 6 miesiący.
        /// </summary>
        public int MaxDepartureDateInMonths { get; set; }

        /// <summary>
        ///     Data początkowa od jakiej mają być pobierane okazje. Domyślnie 1, czyli od dnia jutrzejszego.
        /// </summary>
        public int MinDepartureDateInDays { get; set; }

        /// <summary>
        ///     Maksymalna data do jakiej mają być pobierane okazje dla lotów powrotnych. Domyślnie 6 miesiący.
        /// </summary>
        public int MaxReturnDepartureDateInMonths { get; set; }

        /// <summary>
        ///     Dla niektorych rynkow do ceny w kalendarzu będzie brana pod uwagę tylko cena bazowa. np. Edestinos
        /// </summary>
        public bool TakeOnlyBasePrice { get; set; }

        /// <summary>
        ///     Maksymalna liczba dni na powrót w przypadku lotów Round-Trip
        /// </summary>
        public int MaxDaysForReturn { get; set; }

        /// <summary>
        ///     Maksymalna data do jakiej beda generowane kalendarze. Domyślnie 12 miesiący.
        /// </summary>
        public int MaxDepartureDateInMonthsForPriceAlert { get; set; }

        public bool UseMixedFlightsInCalendars { get; set; }

        /// <summary>
        ///     Zakres dla jakiego bedą generowane oferty VI. Przykład, Generuj do MaxDepartureDateInMonths=7 miesięcy do przodu
        ///     ale po RangeInDaysForViOffersGeneratedByWorkerService=7 dni
        /// </summary>
        public int RangeInDaysForViOffersGeneratedByWorkerService { get; set; }
    }
}