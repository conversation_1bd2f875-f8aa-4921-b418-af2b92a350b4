using Esky.Framework.PartnerSettings.Enums;
using System.Collections.Generic;
using System.Linq;

namespace Esky.FlightsCache.PartnerSettings
{
    public sealed class PartnerSettingsModel
    {
        public PartnerSettingsModel()
        {
            SpecialOccasionsSettings = new SpecialOccasionsSettings();
        }

        public string Code { get; set; }
        public IDictionary<string, ProviderModel> Providers { get; set; }

        public string RuleEngineBaseApiUrl { get; set; }
        public bool RulesEngineUsageLoggerEnabled { get; set; }
        public IList<int> RulesEngineUsageLoggerRequestTypeBlackList { get; set; }
        public IList<string> RulesEngineUsageLoggerBlackList { get; set; }
        public FlightsMixingSettings FlightsMixingSettings { get; set; }
        public string ParentPartnerCode { get; set; }
        public string CompanyBranch { get; set; }

        public bool IsPriceTracingEnabled { get; set; }

        public bool ShouldGenerateSearchLegLocators { get; set; }
        public int LegLocatorProviderId { get; set; }

        public SpecialOccasionsSettings SpecialOccasionsSettings { get; set; }

        public string CurrencySymbolUSD { get; set; }
        public string CurrencySymbolBRL { get; set; }

        public string DefaultCurrency { get; set; }

        public static PartnerSettingsModel Empty => new();

        public FlightSearchSettings FlightSearchSettings { get; set; }

        public PartnerRoleEnum Role { get; set; }

        public ProviderModel GetProvider(int providerCode)
        {
            return Providers.Values.FirstOrDefault(x => x.ProviderCode == providerCode);
        }

        public bool IsDpk()
        {
            var dpkRoles = new[] { PartnerRoleEnum.Agent, PartnerRoleEnum.Company, PartnerRoleEnum.Fullfillment };

            return dpkRoles.Contains(Role);
        }
    }
}