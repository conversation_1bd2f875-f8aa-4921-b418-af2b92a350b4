using Esky.Framework.Cache;
using Esky.Framework.Cache.Abstractions;
using Esky.Framework.Contexts;
using Esky.Framework.Diagnostics.Tracing;
using Esky.Framework.Logging.Loggers;
using Esky.Framework.Middleware.IoC;
using Esky.Framework.PartnerSettings;
using Esky.Metrics.Abstractions;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using System;

namespace Esky.FlightsCache.PartnerSettings
{
    public static class ConfigurationExtensions
    {
        public static IServiceCollection ConfigurePartnerSettings(this IServiceCollection services,
            IConfiguration configuration)
        {
            var psConfig = configuration.GetSection("PartnerSettings").Get<PartnerSettingsConfiguration>();

            services.UseApplicationContext();

            services
                .ConfigurePartnerSettingsAsSingleton(
                    (psConfig.Url, psConfig.ApiKey, psConfig.System),
                    x => x.Register<RuntimeModeContext>(),
                    x =>
                        x.Register(configuration.GetSection("Basic").Get<BasicConfiguration>()),
                    x => x.Register<NoDisposablePartnerContext>())
                .AddSingleton<IPartnerSettingsService, PartnerSettingsService>();

            services.TryAddSingleton<ITraceManager, TraceManager>();
            services.TryAddSingleton<IMetricsManager, NoopMetricsManager>();
            services.TryAddSingleton(typeof(IGeneralLogger<>), typeof(NlogGeneralLogger<>));
            services.TryAddSingleton<ICacheService, MemoryCacheService>();

            return services;
        }

        private class PartnerSettingsConfiguration
        {
            public string System { get; set; }
            public string ApiKey { get; set; }
            public string Url { get; set; }
        }

        private class RuntimeModeContext : IRuntimeModeContext
        {
            public bool IsLive { get => true; set => throw new NotImplementedException(); }
        }
    }
}