using Esky.CurrencyService.API.Contract;
using Esky.CurrencyService.ApiClient;
using Esky.CurrencyService.ApiClient.Logger;
using Esky.CurrencyService.ApiClient.Polly;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace Esky.FlightsCache.CurrencyProvider
{
    public static class ConfigurationExtensions
    {
        public static IServiceCollection ConfigureCurrencyProvider(this IServiceCollection services, IConfiguration configuration)
        {
            services
                .AddSingleton<ICommunicationLogger, DefaultCommunicationLogger>()
                .AddSingleton<IExecutionPolicyRegister<CurrencyRateDTO>, GetExchangeRateExecutionPolicyRegister>()
                .AddSingleton<IRestClientProvider, RestClientProvider>()
                .AddSingleton<ICurrencyServiceApiClient, CurrencyServiceApiClient>()
                .AddSingleton<ICurrencyExchangeRateService, CurrencyExchangeRatesService>()
                .AddSingleton<ICurrencyExchangeRatesUpdater, CurrencyExchangeRatesUpdater>()
                .AddSingleton<IDateProvider, DateProvider>()
                .AddSingleton<ICurrencyRatioProvider, CurrencyRatioProvider>()
                .AddSingleton<ICurrencyRatioDatabaseClient, CurrencyRatioDatabaseClient>();

            services.Configure<CurrencyServiceApiClientOptions>(configuration.GetSection("ExternalServices:CurrencyService"));

            return services;
        }
    }
}