namespace Esky.FlightsCache.CurrencyProvider
{
    public class CurrencyExchangeService : ICurrencyExchangeService
    {
        private readonly ICurrencyRatioProvider _currencyRatioProvider;

        public CurrencyExchangeService(ICurrencyRatioProvider currencyRatioProvider)
        {
            _currencyRatioProvider = currencyRatioProvider;
        }

        public decimal ConvertCurrency(decimal amount, string sourceCurrency, string targetCurrency)
        {
            if (sourceCurrency == targetCurrency)
            {
                return amount;
            }

            if (amount == 0m)
            {
                return 0m;
            }

            var ratio = _currencyRatioProvider.GetRatio(sourceCurrency, targetCurrency);

            return amount * ratio;
        }
    }
}