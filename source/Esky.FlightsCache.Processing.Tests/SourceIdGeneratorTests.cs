using System;

namespace Esky.FlightsCache.Processing.Tests;

public class SourceIdGeneratorTests
{
    [Fact]
    public void GenerateSourceId_ShouldReturnUniqueId()
    {
        var generator = new SourceIdGenerator();

        var id1 = generator.GenerateSourceId();
        var id2 = generator.GenerateSourceId();

        Assert.NotEqual(id1, id2);
    }
    
    [Fact]
    public void GenerateSourceId_ShouldReturnIdWithinExpectedRange()
    {
        var generator = new SourceIdGenerator();

        var id = generator.GenerateSourceId();

        Assert.InRange(id, 0, long.MaxValue);
    }
    
    [Fact]
    public void GenerateSourceId_ShouldBeAlwaysHigherThanPrevious()
    {
        var generator = new SourceIdGenerator();

        var previousId = generator.GenerateSourceId();
        for (var i = 0; i < 100; i++)
        {
            var currentId = generator.GenerateSourceId();
            Assert.True(currentId > previousId, "Generated ID should be greater than the previous one.");
            previousId = currentId;
        }
    }
}