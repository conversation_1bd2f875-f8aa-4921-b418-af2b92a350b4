using System.Collections.Generic;

namespace Esky.FlightsCache.Processing.Tests.TestUtils
{
    public static class TheoryDataExtensions
    {
        public static TheoryData<T> ToTheoryData<T>(this IEnumerable<T> collection)
        {
            var result = new TheoryData<T>();
            foreach (var item in collection)
            {
                result.Add(item);
            }

            return result;
        }
    }
}