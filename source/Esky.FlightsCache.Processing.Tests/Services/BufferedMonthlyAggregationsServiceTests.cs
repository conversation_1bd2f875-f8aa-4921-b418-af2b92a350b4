using Esky.FlightsCache.Common;
using Esky.FlightsCache.Database.Aggregations;
using Esky.FlightsCache.Database.FlightOffers.Model;
using NSubstitute;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Esky.FlightsCache.Processing.Tests.Services
{
    public class BufferedMonthlyAggregationsServiceTests
    {
        private readonly BulkAggregationConfiguration _bulkAggregationConfiguration;
        private readonly IMonthlyAggregationsService _monthlyAggregationsService;
        private readonly IRouteAggregationBuffer _routeAggregationBuffer;

        public BufferedMonthlyAggregationsServiceTests()
        {
            _monthlyAggregationsService = Substitute.For<IMonthlyAggregationsService>();
            _routeAggregationBuffer = Substitute.For<IRouteAggregationBuffer>();
            _bulkAggregationConfiguration = new BulkAggregationConfiguration();
        }

        private BufferedMonthlyAggregationsService CreateTestObject()
        {
            return new BufferedMonthlyAggregationsService(
                _monthlyAggregationsService,
                _routeAggregationBuffer,
                _bulkAggregationConfiguration);
        }

        [Fact]
        public async Task One_way_aggregation_passes_call_to_decorated_service_when_disabled_in_configuration()
        {
            // Arrange
            var testObject = CreateTestObject();
            var owCells = new List<FlightOfferOneWay>();
            _bulkAggregationConfiguration.IsEnabledForOneWay = false;

            // Act
            await testObject.RecalculateMonthlyAggregates(owCells);

            // Assert
            await _monthlyAggregationsService.Received().RecalculateMonthlyAggregates(owCells);
            await _routeAggregationBuffer.DidNotReceive().Insert(Arg.Any<string>(), Arg.Any<RouteUpdateEvent[]>());
        }

        [Fact]
        public async Task One_way_aggregation_stores_event_in_buffer_when_enabled_in_configuration()
        {
            // Arrange
            var testObject = CreateTestObject();
            var owCells = new List<FlightOfferOneWay>();
            _bulkAggregationConfiguration.IsEnabledForOneWay = true;

            // Act
            await testObject.RecalculateMonthlyAggregates(owCells);

            // Assert
            await _routeAggregationBuffer.Received().Insert("OW", Arg.Any<RouteUpdateEvent[]>());
            await _monthlyAggregationsService.DidNotReceive().RecalculateMonthlyAggregates(owCells);
        }

        [Fact]
        public async Task One_way_aggregation_sends_events_grouped_by_route_and_year_and_month()
        {
            // Arrange
            var owCells = new List<FlightOfferOneWay>
            {
                CreateOneWayCell("KTW-LON", "KTW-LTN", 2020, 05, 35),
                CreateOneWayCell("KTW-LON", "KTW-LTN", 2020, 05, 64)
            };
            _bulkAggregationConfiguration.IsEnabledForOneWay = true;

            RouteUpdateEvent[] sentEvents = null;
            await _routeAggregationBuffer.Insert(Arg.Any<string>(), Arg.Do<RouteUpdateEvent[]>(x => sentEvents = x));

            var testObject = CreateTestObject();

            // Act
            await testObject.RecalculateMonthlyAggregates(owCells);

            // Assert
            sentEvents.Should().HaveCount(1);
            var sentEvent = sentEvents.Single();
            sentEvent.Route.MultiportRouteString.Should().Be("KTW-LON");
            sentEvent.Route.AirportRouteString.Should().Be("KTW-LTN");
            sentEvent.YearMonthDate.Should().Be(new DateTime(2020, 05, 01));
            sentEvent.Providers.Should().BeEquivalentTo(new[] { 35, 64 });
        }

        [Fact]
        public async Task One_way_aggregation_sends_events_with_distinct_provider_codes()
        {
            // Arrange
            var owCells = new List<FlightOfferOneWay>
            {
                CreateOneWayCell("KTW-LON", "KTW-LTN", 2020, 05, 35),
                CreateOneWayCell("KTW-LON", "KTW-LTN", 2020, 05, 35)
            };
            _bulkAggregationConfiguration.IsEnabledForOneWay = true;

            RouteUpdateEvent[] sentEvents = null;
            await _routeAggregationBuffer.Insert(Arg.Any<string>(), Arg.Do<RouteUpdateEvent[]>(x => sentEvents = x));

            var testObject = CreateTestObject();

            // Act
            await testObject.RecalculateMonthlyAggregates(owCells);

            // Assert
            sentEvents.Should().HaveCount(1);
            var sentEvent = sentEvents.Single();
            sentEvent.Providers.Should().BeEquivalentTo(new[] { 35 });
        }

        [Fact]
        public async Task One_way_aggregation_sends_events_with_standardized_routes()
        {
            // Arrange
            var owCells = new List<FlightOfferOneWay>
            {
                CreateOneWayCell("LON-KTW", "LTN-KTW", 2020, 05, 35)
            };
            _bulkAggregationConfiguration.IsEnabledForOneWay = true;

            RouteUpdateEvent[] sentEvents = null;
            await _routeAggregationBuffer.Insert(Arg.Any<string>(), Arg.Do<RouteUpdateEvent[]>(x => sentEvents = x));

            var testObject = CreateTestObject();

            // Act
            await testObject.RecalculateMonthlyAggregates(owCells);

            // Assert
            sentEvents.Should().HaveCount(1);
            var sentEvent = sentEvents.Single();
            sentEvent.Route.MultiportRouteString.Should().Be("LON-KTW");
            sentEvent.Route.AirportRouteString.Should().Be("LTN-KTW");
        }

        [Fact]
        public async Task Round_trip_aggregation_passes_call_to_decorated_service()
        {
            // Arrange
            var testObject = CreateTestObject();
            var rtCells = new List<FlightOfferOneWay>();

            // Act
            await testObject.RecalculateMonthlyAggregates(rtCells);

            // Assert
            await _monthlyAggregationsService.Received().RecalculateMonthlyAggregates(rtCells);
        }

        private static FlightOfferOneWay CreateOneWayCell(string topRoute, string route, int year, int month, int provider)
        {
            return FlightOfferOneWay.Empty with {Route = GetRoute(topRoute, route), Provider = provider, DepartureDate = new DateTime(year, month, 1)};
        }

        private static Route GetRoute(string topRoute, string route)
        {
            var topCodes = topRoute.Split('-');
            var airportCodes = route.Split('-');
            return new Route { Departure = airportCodes[0], Arrival = airportCodes[1], MultiportDeparture = topCodes[0], MultiportArrival = topCodes[1] };
        }
    }
}