namespace Esky.FlightsCache.Processing.Tests.Services.MonthlyAggregations
{
    public class RouteDefinitionParserTests
    {
        [Theory]
        [InlineData("KTW-LTN(LON)", new[] { "KTW", "LTN" })]
        [InlineData("STN(LON)-KTW", new[] { "KTW", "STN" })]
        [InlineData("LTN(LON)-TXL(BERL)", new[] { "TXL", "LTN" })]
        [InlineData("KRK-GDN", new[] { "KRK", "GDN" })]
        public void Parse_ShouldParseAirportCodes(string routeString, string[] expectedAirportCodes)
        {
            // Act
            var result = RouteDefinitionParser.Parse(routeString);

            // Assert
            expectedAirportCodes.Should().BeEquivalentTo(result.First.AirportCode, result.Second.AirportCode);
        }

        [Theory]
        [InlineData("KTW-LTN(LON)", new[] { "KTW", "LON" })]
        [InlineData("STN(LON)-KTW", new[] { "KTW", "LON" })]
        [InlineData("LTN(LON)-TXL(BERL)", new[] { "BERL", "LON" })]
        [InlineData("KRK-GDN", new[] { "KRK", "GDN" })]
        public void Parse_ShouldParseMultiportCodes(string routeString, string[] expectedMultiportCodes)
        {
            // Act
            var result = RouteDefinitionParser.Parse(routeString);

            // Assert
            expectedMultiportCodes.Should().BeEquivalentTo(result.First.MultiportCode, result.Second.MultiportCode);
        }

        [Theory]
        [InlineData("KTW-LTN(LON)", "KTW")]
        [InlineData("STN(LON)-KTW", "STN")]
        [InlineData("LTN(LON)-TXL(BERL)", "LTN")]
        [InlineData("KRK-GDN", "KRK")]
        [InlineData("GDN-KRK", "GDN")]
        public void Parse_ShouldNotBeOrderedByMultiportCodes(string routeString, string expectedFirstAirportCode)
        {
            // Act
            var result = RouteDefinitionParser.Parse(routeString);

            // Assert
            result.First.AirportCode.Should().BeEquivalentTo(expectedFirstAirportCode);
        }
    }
}