using Esky.FlightsCache.Database.Aggregations;
using System;
using System.Text.RegularExpressions;

namespace Esky.FlightsCache.Processing.Tests.Services.MonthlyAggregations
{
    internal static partial class RouteDefinitionParser
    {
        public static GeneralRouteDefinition Parse(string routeString)
        {
            var match = ParseRegex().Match(routeString);
            try
            {
                var departureAirport = match.Groups["depAirport"].Value;
                var departureMultiport = match.Groups["depMultiport"].Success
                    ? match.Groups["depMultiport"].Value
                    : departureAirport;
                var departureTuple = (MultiportCode: departureMultiport, AirportCode: departureAirport);

                var arrivalAirport = match.Groups["arrAirport"].Value;
                var arrivalMultiport = match.Groups["arrMultiport"].Success
                    ? match.Groups["arrMultiport"].Value
                    : arrivalAirport;
                var arrivalTuple = (MultiportCode: arrivalMultiport, AirportCode: arrivalAirport);

                return new GeneralRouteDefinition(departureTuple, arrivalTuple);
            }
            catch (Exception e)
            {
                throw new ApplicationException($"Cannot parse {nameof(GeneralRouteDefinition)}:{routeString}", e);
            }
        }

        [GeneratedRegex(@"^(?<depAirport>[A-Z]{3})(\((?<depMultiport>[A-Z]{3,4})\))?-(?<arrAirport>[A-Z]{3})(\((?<arrMultiport>[A-Z]{3,4})\))?$")]
        private static partial Regex ParseRegex();
    }
}