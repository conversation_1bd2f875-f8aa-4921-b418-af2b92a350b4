using Esky.FlightsCache.Database.Aggregations;
using System;

namespace Esky.FlightsCache.Processing.Tests.Services.MonthlyAggregations
{
    public class RouteUpdateEventTests
    {
        [Fact]
        public void New_event_has_all_values_set()
        {
            // Act
            var routeUpdateEvent = new RouteUpdateEvent(RouteDefinitionParser.Parse("KTW-LTN(LON)"), 2020, 02,
                new[] { 11, 35, 64 });

            // Assert
            routeUpdateEvent.Route.First.MultiportCode.Should().Be("KTW");
            routeUpdateEvent.Route.First.AirportCode.Should().Be("KTW");
            routeUpdateEvent.Route.Second.MultiportCode.Should().Be("LON");
            routeUpdateEvent.Route.Second.AirportCode.Should().Be("LTN");
            routeUpdateEvent.YearMonthDate.Should().Be(new DateTime(2020, 02, 01));
            routeUpdateEvent.Providers.Should().BeEquivalentTo(new[] { 11, 35, 64 });
        }

        [Fact]
        public void String_representation_contains_all_event_data()
        {
            // Arrange
            var routeUpdateEvent = new RouteUpdateEvent(RouteDefinitionParser.Parse("KTW-LTN(LON)"), 2020, 11,
                new[] { 11, 35, 64 });

            // Act
            var actual = routeUpdateEvent.ToString();

            // Assert
            actual.Should().Be("KTW-LONKTWLTN201111,35,64");
        }

        [Fact]
        public void Parsed_event_has_all_values_set()
        {
            // Act
            var routeUpdateEvent = RouteUpdateEvent.Parse("KTW-LONKTWLTN191128,58");

            // Assert
            routeUpdateEvent.Route.First.MultiportCode.Should().Be("KTW");
            routeUpdateEvent.Route.First.AirportCode.Should().Be("KTW");
            routeUpdateEvent.Route.Second.MultiportCode.Should().Be("LON");
            routeUpdateEvent.Route.Second.AirportCode.Should().Be("LTN");
            routeUpdateEvent.YearMonthDate.Should().Be(new DateTime(2019, 11, 01));
            routeUpdateEvent.Providers.Should().BeEquivalentTo(new[] { 28, 58 });
        }

        [Theory]
        [InlineData("01", 2001)]
        [InlineData("69", 2069)]
        [InlineData("99", 2099)]
        public void Event_parsing_handles_two_digit_years(string twoDigitYear, int expectedYear)
        {
            // Act
            var routeUpdateEvent = RouteUpdateEvent.Parse($"KTW-LONKTWLTN{twoDigitYear}116");

            // Assert
            routeUpdateEvent.YearMonthDate.Year.Should().Be(expectedYear);
        }

        [Theory]
        [InlineData("GDN-KRKGDNKRK200558,64,32", "GDN-KRK", "GDN-KRK")] // airport-airport
        [InlineData("KRK-NYCKRKJFK200558,64,32", "KRK-NYC", "KRK-JFK")] // airport-multiport(3)
        [InlineData("BERL-KRKTXLKRK200558,64,32", "BERL-KRK", "TXL-KRK")] // airport-multiport(3)
        [InlineData("LON-GDNLTNGDN200558,64,32", "LON-GDN", "LTN-GDN")] // multiport(3)-airport
        [InlineData("LON-NYCLHRJFK200558,64,32", "LON-NYC", "LHR-JFK")] // multiport(3)-multiport(3)
        [InlineData("LON-BERLLTNTXL200558,64,32", "LON-BERL", "LTN-TXL")] // multiport(3)-multiport(4)
        [InlineData("GDN-WAWAGDNWAW200558,64,32", "GDN-WAWA", "GDN-WAW")] // multiport(4)-airport
        [InlineData("NYC-WAWAJFKWAW200558,64,32", "NYC-WAWA", "JFK-WAW")] // multiport(4)-multiport(3)
        [InlineData("BERL-WAWATXLWAW200558,64,32", "BERL-WAWA", "TXL-WAW")] // multiport(4)-multiport(4)
        public void Event_parsing_handles_multiport_decoded_route(string eventToParse, string expectedMultiportRoute,
            string expectedAirportRoute)
        {
            // Act
            var routeUpdateEvent = RouteUpdateEvent.Parse(eventToParse);

            // Assert
            routeUpdateEvent.Route.MultiportRouteString.Should().Be(expectedMultiportRoute);
            routeUpdateEvent.Route.AirportRouteString.Should().Be(expectedAirportRoute);
        }

        [Theory]
        [InlineData("GDN-KRKGDNKRK200558")]
        [InlineData("KRK-NYCKRKJFK200558,64,32")]
        [InlineData("BERL-KRKTXLKRK200558,64,32")]
        [InlineData("LON-GDNLTNGDN200558,64,32")]
        [InlineData("LON-NYCLHRJFK200558,64,32")]
        [InlineData("BERL-LONTXLLTN200558,64,32")]
        [InlineData("GDN-WAWAGDNWAW200558,64,32")]
        [InlineData("NYC-WAWAJFKWAW200558,64,32")]
        [InlineData("BERL-WAWATXLWAW200558,64")]
        public void Parsed_event_should_generate_the_same_event_key(string eventToParse)
        {
            // Act
            var routeUpdateEvent = RouteUpdateEvent.Parse(eventToParse);

            // Assert
            routeUpdateEvent.ToString().Should().Be(eventToParse);
        }
    }
}