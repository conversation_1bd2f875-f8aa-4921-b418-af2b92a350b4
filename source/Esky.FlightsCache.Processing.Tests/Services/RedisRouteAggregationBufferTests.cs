using Esky.FlightsCache.Common;
using Esky.FlightsCache.Database.Aggregations;
using Esky.FlightsCache.Processing.Tests.Services.MonthlyAggregations;
using Microsoft.Extensions.Logging;
using NSubstitute;
using StackExchange.Redis;
using System.Linq;
using System.Threading.Tasks;

namespace Esky.FlightsCache.Processing.Tests.Services
{
    public class RedisRouteAggregationBufferTests
    {
        private readonly BulkAggregationConfiguration _bulkAggregationConfiguration;
        private readonly IRedisConnection _redisConnection;
        private readonly IDatabase _redisDatabase;
        private readonly ILogger<RedisRouteAggregationBuffer> _logger;

        public RedisRouteAggregationBufferTests()
        {
            _redisConnection = Substitute.For<IRedisConnection>();
            _redisDatabase = Substitute.For<IDatabase>();
            _bulkAggregationConfiguration = new BulkAggregationConfiguration();
            _logger = Substitute.For<ILogger<RedisRouteAggregationBuffer>>();

            _redisConnection.Database.Returns(_redisDatabase);
        }

        private RedisRouteAggregationBuffer CreateTestObject()
        {
            return new RedisRouteAggregationBuffer(
                _redisConnection,
                _bulkAggregationConfiguration,
                _logger);
        }

        [Fact]
        public async Task Inserting_empty_array_does_not_call_redis_database()
        {
            // Arrange
            var testObject = CreateTestObject();

            // Act
            await testObject.Insert("OW", new RouteUpdateEvent[0]);

            // Assert
            _redisDatabase.ReceivedCalls().Should().BeEmpty();
        }

        [Fact]
        public async Task Inserting_non_empty_array_causes_insert_to_redis_database()
        {
            // Arrange
            _redisConnection.InstanceName.Returns("instance");
            _bulkAggregationConfiguration.MaxBufferLength = 123;

            var testObject = CreateTestObject();

            // Act
            await testObject.Insert("OW",
                new[]
                {
                    new RouteUpdateEvent(RouteDefinitionParser.Parse("KTW-LTN(LON)"), 2020, 05, new[] { 11, 35 })
                });

            // Assert
            await _redisDatabase
                .Received()
                .StreamAddAsync(
                    "instance:aggregations:streams:OW",
                    "_",
                    Arg.Is<RedisValue>(x => x == "KTW-LONKTWLTN200511,35"),
                    maxLength: 123,
                    useApproximateMaxLength: true,
                    flags: CommandFlags.FireAndForget);
        }

        [Fact]
        public async Task Removing_empty_list_of_values_does_not_cause_acknowledge_on_stream()
        {
            // Arrange
            _bulkAggregationConfiguration.MaxElementsToProcess = 66;
            _redisConnection.InstanceName.Returns("instance");

            var testObject = CreateTestObject();

            // Act
            await testObject.RemoveValues("OW", new string[0]);

            // Assert
            _redisConnection.Database.ReceivedCalls().Should().HaveCount(0);
        }

        [Fact]
        public async Task Removing_values_causes_acknowledge_for_passed_keys()
        {
            // Arrange
            _bulkAggregationConfiguration.MaxElementsToProcess = 66;
            _redisConnection.InstanceName.Returns("instance");
            var keys = new[] { "key1", "key2" };

            var testObject = CreateTestObject();

            // Act
            await testObject.RemoveValues("OW", keys);

            // Assert
            await _redisConnection.Database
                .Received()
                .StreamAcknowledgeAsync(
                    "instance:aggregations:streams:OW",
                    "ConsumerGroup",
                    Arg.Is<RedisValue[]>(p => keys.SequenceEqual(p.Select(val => val.ToString()))));
        }
    }
}