using Esky.FlightsCache.ProviderMapping;
using MoreLinq;

namespace Esky.FlightsCache.Processing.Tests
{
    public class FlightsCacheProviderConverterTests
    {
        public enum CacheProviderCodes : byte
        {
            RyanAir = 129,
            WizzAir = 130,
            EasyJet = 131,
            VivaAirPe = 141,
            VivaAirCo = 142
        }

        public enum ProviderCodes
        {
            SSC = 6,
            Unmapped = 1000
        }

        private readonly FlightsCacheProviderConverter _converter;

        public FlightsCacheProviderConverterTests()
        {
            _converter = new FlightsCacheProviderConverter(
            [
                Mapping(CacheProviderCodes.RyanAir, ProviderCodes.SSC, "FR"),
                Mapping(CacheProviderCodes.WizzAir, ProviderCodes.SSC, "W6", "WU"),
                Mapping(CacheProviderCodes.EasyJet, ProviderCodes.SSC, "U2"),
                Mapping(CacheProviderCodes.VivaAirPe, ProviderCodes.SSC, "VV"),
                Mapping(CacheProviderCodes.VivaAirCo, ProviderCodes.SSC, "FC"),
                Mapping((CacheProviderCodes)143, (ProviderCodes)40, "0B"),
                Mapping((CacheProviderCodes)144, (ProviderCodes)58, "D8"),
                Mapping((CacheProviderCodes)145, (ProviderCodes)58, "VY")
            ]);
        }

        private CacheProviderConfiguration Mapping(CacheProviderCodes cacheProvider, ProviderCodes searchProvider,
            params string[] airlines)
        {
            return new CacheProviderConfiguration
            {
                CacheProviderCode = (int)cacheProvider,
                ReadProviderCode = (int)searchProvider,
                WriteConfigurations = new[]
                {
                    new CacheProviderConfiguration.WriteConfiguration
                    {
                        ProviderCode = (int)searchProvider, AirlineCodes = airlines
                    }
                }
            };
        }

        [Theory]
        [InlineData("FR", CacheProviderCodes.RyanAir)]
        [InlineData("W6", CacheProviderCodes.WizzAir)]
        [InlineData("WU", CacheProviderCodes.WizzAir)]
        [InlineData("U2", CacheProviderCodes.EasyJet)]
        [InlineData("VV", CacheProviderCodes.VivaAirPe)]
        [InlineData("FC", CacheProviderCodes.VivaAirCo)]
        public void GetWriteConfiguration_WhenSscProviderAndKnowAirlineCode_ConvertToSSCProviderCodeEnum(
            string airlineCode, CacheProviderCodes result)
        {
            Assert.Equal((int)result, _converter.GetWriteConfiguration(6, [airlineCode]).CacheProviderCode);
        }

        [Fact]
        public void ToFpcProviderCodes_WhenNotMappedProvider_ShouldReturnUnchanged()
        {
            var otherProviderCode = 35;
            var result = _converter.ToFCProviderCodes([otherProviderCode], []);

            Assert.Contains(otherProviderCode, result);
        }

        [Fact]
        public void ToFpcProviderCodes_WhenMappedAndNotMappedProviders_ShouldReturnBoth()
        {
            var result = _converter.ToFCProviderCodes(
                [(int)ProviderCodes.SSC, (int)ProviderCodes.Unmapped],
                [AirlineCodes.WizzAir]);

            Assert.Contains((int)ProviderCodes.Unmapped, result);
            Assert.Contains((int)CacheProviderCodes.WizzAir, result);
        }

        [Fact]
        public void ToFpcProviderCodes_WhenMappedAndNotMappedAirline_ShouldReturnBothProviders()
        {
            var result = _converter.ToFCProviderCodes(
                [(int)ProviderCodes.SSC],
                [AirlineCodes.WizzAir, AirlineCodes.Unmapped]);

            Assert.Contains((int)ProviderCodes.SSC, result);
            Assert.Contains((int)CacheProviderCodes.WizzAir, result);
        }

        [Fact]
        public void ToFpcProviderCodes_WhenNotMappedAirline_ShouldReturnOriginalProvider()
        {
            var result = _converter.ToFCProviderCodes(
                [(int)ProviderCodes.SSC],
                [AirlineCodes.Unmapped]);

            Assert.Contains((int)ProviderCodes.SSC, result);
        }

        [Fact]
        public void ToFpcProviderCodes_WhenMappedAirline_ShouldNotReturnOriginalProvider()
        {
            var result = _converter.ToFCProviderCodes(
                [(int)ProviderCodes.SSC],
                [AirlineCodes.WizzAir]);

            Assert.Contains((int)CacheProviderCodes.WizzAir, result);
            Assert.DoesNotContain((int)ProviderCodes.SSC, result);
        }

        [Theory]
        [InlineData(ProviderCodes.SSC, 129, 130, 131, 141, 142)]
        [InlineData((ProviderCodes)40, 143)]
        [InlineData((ProviderCodes)58, 144, 145)]
        [InlineData((ProviderCodes)35)]
        public void ToFpcProviderCodes_WhenNoAirline_ShouldReturAllMappedCacheProvidersAndOriginalCode(
            ProviderCodes searchProvider, params int[] expectedCacheProviders)
        {
            var result = _converter.ToFCProviderCodes(
                [(int)searchProvider],
                []);

            result.Should().Equal(expectedCacheProviders.Append((int)searchProvider));
        }

        private static class AirlineCodes
        {
            internal const string WizzAir = "W6";
            internal const string Unmapped = "XX";
        }
    }
}