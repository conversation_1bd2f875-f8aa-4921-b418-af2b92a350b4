using Esky.FlightsCache.Common.Extensions;

namespace Esky.FlightsCache.Processing.Tests.Common.Extensions
{
    public class IntegerExtensionsTests
    {
        [Theory]
        [InlineData("1-3, 10, 20-22", 1, 2, 3, 10, 20, 21, 22)]
        [InlineData("")]
        [InlineData("1, 3, 10-11, 22", 1, 3, 10, 11, 22)]
        [InlineData("1, 4", 1, 1, 1, 4, 4)]
        public void ToRangeString(string expectedResult, params int[] input)
        {
            // Act
            var rangeString = input.ToRangeString();

            // Assert
            rangeString.Should().Be(expectedResult);
        }
    }
}