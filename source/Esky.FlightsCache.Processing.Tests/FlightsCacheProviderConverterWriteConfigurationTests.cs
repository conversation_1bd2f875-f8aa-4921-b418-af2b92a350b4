using Esky.FlightsCache.ProviderMapping;

namespace Esky.FlightsCache.Processing.Tests;

public class FlightsCacheProviderConverterWriteConfigurationTests
{
    private readonly FlightsCacheProviderConverter _converter = new(
        [
            Mapping(19, 129, ["FR", "RK"]),
            Mapping(19, 130, ["W4", "5W", "W6", "W9"]),
            Mapping(58, 131, ["W4", "5W", "W6", "W9"]),
        ]
    );

    [Theory]
    [InlineData(19, new[] { "FR" }, 129)]
    [InlineData(19, new[] { "RK" }, 129)]
    [InlineData(19, new[] { "FR", "W6" }, null)] // no airline match
    [InlineData(19, new[] { "FR", "RK" }, 129)]
    [InlineData(19, new[] { "W4", "W9" }, 130)]
    [InlineData(19, new[] { "W6" }, 130)]
    [InlineData(19, new[] { "W6", "LH" }, null)] // no airline match
    [InlineData(19, new[] { "W4", "5W" }, 130)]
    [InlineData(58, new[] { "W4", "5W" }, 131)]
    [InlineData(58, new[] { "W4", "LH" }, null)] // no airline match
    [InlineData(58, new[] { "FR" }, null)] // no airline match
    public void GetWriteConfiguration(int searchProvider, string[] airlines, int? expectedCode)
    {
        var configuration = _converter.GetWriteConfiguration(searchProvider, airlines);

        if (expectedCode is null)
            configuration.Should().BeNull();
        else
            configuration.CacheProviderCode.Should().Be(expectedCode);
    }

    private static CacheProviderConfiguration Mapping(int searchProvider, int cacheProvider, params string[] airlines)
    {
        return new CacheProviderConfiguration
        {
            CacheProviderCode = cacheProvider,
            ReadProviderCode = searchProvider,
            WriteConfigurations = [new CacheProviderConfiguration.WriteConfiguration { ProviderCode = searchProvider, AirlineCodes = airlines }]
        };
    }
}