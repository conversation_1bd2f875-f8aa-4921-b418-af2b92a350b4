using Esky.FlightsCache.MessageContract;
using Esky.FlightsCache.ResultFilters.AirportCode;
using Esky.FlightsCache.ResultFilters.Contract;
using Esky.FlightsCache.ResultFilters.FlightListFilters.Global;
using NSubstitute;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Esky.FlightsCache.Processing.Tests.ResultFilters.FlightListFilters.Global
{
    public class AirportCodeResultFilterTests
    {
        private readonly IFlightResultContext _context;
        private readonly AirportCodeFlightListFilter _subject;

        public AirportCodeResultFilterTests()
        {
            var db = Substitute.For<IAirportsRepository>();

            AirportCollection.Airport[] airports =
            {
                new()
                {
                    Code = "WAW",
                    Multiport = "WAWA",
                    NearbyAirports = Array.Empty<string>(),
                    CityCode = "WAW",
                    CountryCode = "PL",
                    ContinentCode = "EU"
                },
                new()
                {
                    Code = "WMI",
                    Multiport = "WAWA",
                    NearbyAirports = Array.Empty<string>(),
                    CityCode = "WAW",
                    CountryCode = "PL",
                    ContinentCode = "EU"
                },
                new()
                {
                    Code = "LTN",
                    Multiport = "LON",
                    NearbyAirports = Array.Empty<string>(),
                    CityCode = "LON",
                    CountryCode = "UK",
                    ContinentCode = "EU"
                },
                new()
                {
                    Code = "STN",
                    Multiport = "LON",
                    NearbyAirports = Array.Empty<string>(),
                    CityCode = "LON",
                    CountryCode = "UK",
                    ContinentCode = "EU"
                },
                new()
                {
                    Code = "LHR",
                    Multiport = "LON",
                    NearbyAirports = Array.Empty<string>(),
                    CityCode = "LON",
                    CountryCode = "UK",
                    ContinentCode = "EU"
                }
            };

            db.GetAirportsAsync().Returns(Task.FromResult(new AirportCollection(airports)));

            var airportCodesIgnoreList = Substitute.For<IAirportCodesIgnoreList>();
            airportCodesIgnoreList.CanIgnoreLoggingForNotSupportedCode("LON", "QQS")
                .Returns(true); // London St. Pancras Railway Station

            _subject = new AirportCodeFlightListFilter(airportCodesIgnoreList, db);

            _context = Substitute.For<IFlightResultContext>();
            _context.DepartureCode.Returns("WAWA");
            _context.ArrivalCode.Returns("LON");
        }

        [Fact]
        public async Task Filter_WhenOnlySupportedAirportCodes_ShallNotFilterOutAnyFlights()
        {
            // Arrange
            var inputFlights = CreateFlightsList(
                (DepartureCode: "WAW", ArrivalCode: "LHR"),
                (DepartureCode: "WAW", ArrivalCode: "LTN"),
                (DepartureCode: "WMI", ArrivalCode: "STN")
            );

            // Act
            var filterResult = await _subject.Filter(inputFlights, _context);

            // Assert
            Assert.Equal(inputFlights.Count, filterResult.Flights.Count);
            Assert.Equal(string.Empty, filterResult.StatisticsMessage + filterResult.ErrorMessage);
        }

        [Fact]
        public async Task Filter_WhenUnsupportedByDepartureMultiportAirportCodes_ShallFilterOutNotMappedCodes()
        {
            // Arrange
            var inputFlights = CreateFlightsList(
                (DepartureCode: "WAW", ArrivalCode: "LHR"),
                (DepartureCode: "WAW", ArrivalCode: "LTN"),
                (DepartureCode: "WMI", ArrivalCode: "STN"),
                (DepartureCode: "RDO", ArrivalCode: "STN") // wrong departure airport
            );

            // Act
            var filterResult = await _subject.Filter(inputFlights, _context);

            // Assert
            Assert.Equal(inputFlights.Count - 1, filterResult.Flights.Count);
            Assert.NotEqual(string.Empty, filterResult.StatisticsMessage + filterResult.ErrorMessage);
        }

        [Fact]
        public async Task Filter_WhenUnsupportedByArrivalMultiportAirportCodes_ShallFilterOutNotMappedCodes()
        {
            // Arrange
            var inputFlights = CreateFlightsList(
                (DepartureCode: "WAW", ArrivalCode: "LHR"),
                (DepartureCode: "WAW", ArrivalCode: "LTN"),
                (DepartureCode: "WMI", ArrivalCode: "STN"),
                (DepartureCode: "WAW", ArrivalCode: "KRK"), // wrong arrival airport
                (DepartureCode: "WAW", ArrivalCode: "KTW") // wrong arrival airport
            );

            // Act
            var filterResult = await _subject.Filter(inputFlights, _context);

            // Assert
            Assert.Equal(inputFlights.Count - 2, filterResult.Flights.Count);
            Assert.NotEqual(string.Empty, filterResult.StatisticsMessage + filterResult.ErrorMessage);
        }

        [Fact]
        public async Task
            Filter_WhenUnsupportedByArrivalMultiportAirportCodes_ButKnownUnsupportedMapping_ShallFilterOutNotMappedCodes_ButNoErrorMessage()
        {
            // Arrange
            var inputFlights = CreateFlightsList(
                (DepartureCode: "WAW", ArrivalCode: "LHR"),
                (DepartureCode: "WAW", ArrivalCode: "LTN"),
                (DepartureCode: "WMI", ArrivalCode: "STN"),
                (DepartureCode: "WAW", ArrivalCode: "QQS") // London St. Pancras Railway Station
            );

            // Act
            var filterResult = await _subject.Filter(inputFlights, _context);

            // Assert
            Assert.Equal(inputFlights.Count - 1, filterResult.Flights.Count);
            Assert.Empty(filterResult.StatisticsMessage + filterResult.ErrorMessage);
        }

        [Fact]
        public async Task Filter_WhenUnknownDepartureMultiportCode_ShallFilterOutAllFlights()
        {
            // Arrange
            var inputFlights = CreateFlightsList(
                (DepartureCode: "WAW", ArrivalCode: "LHR"),
                (DepartureCode: "WAW", ArrivalCode: "LTN"),
                (DepartureCode: "WMI", ArrivalCode: "STN")
            );
            _context.DepartureCode.Returns("WARSZAWA" /*Unknown Multiport Code*/);

            // Act
            var filterResult = await _subject.Filter(inputFlights, _context);

            // Assert
            Assert.NotEmpty(inputFlights);
            Assert.Empty(filterResult.Flights);
            Assert.NotEqual(string.Empty, filterResult.StatisticsMessage + filterResult.ErrorMessage);
        }

        [Fact]
        public async Task Filter_WhenUnknownArrivalMultiportCode_ShallFilterOutAllFlights()
        {
            // Arrange
            var inputFlights = CreateFlightsList(
                (DepartureCode: "WAW", ArrivalCode: "LHR"),
                (DepartureCode: "WAW", ArrivalCode: "LTN"),
                (DepartureCode: "WMI", ArrivalCode: "STN")
            );
            _context.ArrivalCode.Returns("LLL" /*Unknown Multiport Code*/);

            // Act
            var filterResult = await _subject.Filter(inputFlights, _context);

            // Assert
            Assert.NotEmpty(inputFlights);
            Assert.Empty(filterResult.Flights);
            Assert.NotEqual(string.Empty, filterResult.StatisticsMessage + filterResult.ErrorMessage);
        }

        [Fact]
        public async Task Filter_WhenSeparationOptionsForInbound_ShallNotFilterOutReturnFlights()
        {
            // Arrange
            var inputFlights = CreateFlightsList(
                (DepartureCode: "WAW", ArrivalCode: "LHR", SeparationOptionEnum.RoundTripOutbound),
                (DepartureCode: "LHR", ArrivalCode: "WAW", SeparationOptionEnum.RoundTripInbound)
            );

            // Act
            var filterResult = await _subject.Filter(inputFlights, _context);

            // Assert
            Assert.Equal(inputFlights.Count, filterResult.Flights.Count);
            Assert.Equal(string.Empty, filterResult.StatisticsMessage + filterResult.ErrorMessage);
        }

        [Fact]
        public async Task Filter_WhenNoSeparationOptionsForInbound_ShallFilterOutReturnFlights()
        {
            // Arrange
            var inputFlights = CreateFlightsList(
                (DepartureCode: "WAW", ArrivalCode: "LHR", SeparationOptionEnum.OneWay),
                (DepartureCode: "LHR", ArrivalCode: "WAW", SeparationOptionEnum.OneWay)
            );

            // Act

            var filterResult = await _subject.Filter(inputFlights, _context);

            // Assert
            Assert.Equal(inputFlights.Count - 1, filterResult.Flights.Count);
            Assert.NotEqual(string.Empty, filterResult.StatisticsMessage + filterResult.ErrorMessage);
        }

        private static List<FlightCache> CreateFlightsList(
            params (string DepartureCode, string ArrivalCode)[] destinationTuples)
        {
            return CreateFlightsList(destinationTuples
                .Select(x => (
                    x.DepartureCode,
                    x.ArrivalCode,
                    SeparationOptions: SeparationOptionEnum.None))
                .ToArray());
        }

        private static List<FlightCache> CreateFlightsList(
            params (string DepartureCode, string ArrivalCode, SeparationOptionEnum SeparationOptions)[]
                destinationTuples)
        {
            return destinationTuples
                .Select(destination =>
                    new FlightCache
                    {
                        Legs =
                        [
                            new()
                            {
                                DepartureCode = destination.DepartureCode,
                                ArrivalCode = destination.ArrivalCode,
                                SeparationOptions = new SeparationOptions
                                {
                                    Options = destination.SeparationOptions
                                }
                            }
                        ]
                    })
                .ToList();
        }
    }
}