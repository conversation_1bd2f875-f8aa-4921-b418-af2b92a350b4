using Esky.FlightsCache.MessageContract;
using Esky.FlightsCache.ResultFilters.FlightListFilters;
using Esky.FlightsCache.ResultFilters.FlightListFilters.Global;
using Esky.FlightsCache.ResultFilters.FlightListFilters.Utilities;
using Microsoft.Extensions.Options;
using NSubstitute;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Esky.FlightsCache.Processing.Tests.ResultFilters.FlightListFilters.Global
{
    public class CheapestFlightsByTimeSlotFlightListFilterTests
    {
        private readonly ITimeOfDayBucketizer _timeOfDayBucketizer;

        public CheapestFlightsByTimeSlotFlightListFilterTests()
        {
            _timeOfDayBucketizer = Substitute.For<ITimeOfDayBucketizer>();
            _timeOfDayBucketizer.GetSlotNumber(Arg.Any<DateTime>()).Returns(1);
        }


        private List<FlightCache> GetFlightsListPossibleToFilter(int providerCode)
        {
            var departureDate = DateTime.Today.AddDays(14);
            return
            [
                GetNonDirectFlight(providerCode, departureDate), GetNonDirectFlight(providerCode, departureDate)
            ];
        }

        private FlightCache GetNonDirectFlight(int providerCode, in DateTime departureDate)
        {
            return new FlightCache
            {
                ProviderCode = providerCode,
                Legs =
                [
                    new()
                    {
                        DepartureDate = departureDate,
                        Segments =
                        [
                            new(), new() // non-direct flight => at least 2 segments
                        ]
                    }
                ]
            };
        }

        private static IOptions<FlightsFilterConfiguration> GetConfiguration(bool include, params int[] providerCodes)
        {
            var config = Substitute.For<IOptions<FlightsFilterConfiguration>>();
            config.Value.Returns(new FlightsFilterConfiguration
            {
                FlightsPerChunk = 1,
                ProvidersToFilterGlobally = new FlightsFilterConfiguration.ProvidersToFilter
                {
                    ProviderCodes = providerCodes,
                    Include = include
                }
            });
            return config;
        }

        [Fact]
        public async Task Filter_WhenProviderInIncludedProviderCodesList_ShallFilterFlights()
        {
            // Arrange
            var config = GetConfiguration(providerCodes: new[] { 1, 2, 3, 6 }, include: true);
            var inputFlights = GetFlightsListPossibleToFilter(6);

            IFlightListFilter subject = new CheapestFlightsByTimeSlotFlightListFilter(_timeOfDayBucketizer, config);

            // Act
            var filterResult = await subject.Filter(inputFlights, null);

            // Assert
            Assert.True(filterResult.Flights.Count() < inputFlights.Count);
        }

        [Fact]
        public async Task Filter_WhenProviderNotInIncludedProviderCodesList_ShallNotFilterFlights()
        {
            // Arrange
            var config = GetConfiguration(providerCodes: new[] { 1, 2, 3 }, include: true);
            var inputFlights = GetFlightsListPossibleToFilter(6);

            IFlightListFilter subject = new CheapestFlightsByTimeSlotFlightListFilter(_timeOfDayBucketizer, config);

            // Act
            var filterResult = await subject.Filter(inputFlights, null);

            // Assert
            Assert.True(filterResult.Flights.Count() == inputFlights.Count);
        }

        [Fact]
        public async Task Filter_WhenProviderInExcludedProviderCodesList_ShallNotFilterFlights()
        {
            // Arrange
            var config = GetConfiguration(providerCodes: new[] { 1, 2, 3, 6 }, include: false);
            var inputFlights = GetFlightsListPossibleToFilter(6);

            IFlightListFilter subject = new CheapestFlightsByTimeSlotFlightListFilter(_timeOfDayBucketizer, config);

            // Act
            var filterResult = await subject.Filter(inputFlights, null);

            // Assert
            Assert.True(filterResult.Flights.Count() == inputFlights.Count);
        }

        [Fact]
        public async Task Filter_WhenProviderNotInExcludedProviderCodesList_ShallFilterFlights()
        {
            // Arrange
            var config = GetConfiguration(providerCodes: new int[0], include: false);
            var inputFlights = GetFlightsListPossibleToFilter(6);

            IFlightListFilter subject = new CheapestFlightsByTimeSlotFlightListFilter(_timeOfDayBucketizer, config);

            // Act
            var filterResult = await subject.Filter(inputFlights, null);

            // Assert
            Assert.True(filterResult.Flights.Count() < inputFlights.Count);
        }
    }
}