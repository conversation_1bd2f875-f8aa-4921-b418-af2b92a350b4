using Esky.FlightsCache.MessageContract;
using Esky.FlightsCache.ResultFilters.Contract;
using Esky.FlightsCache.ResultFilters.FlightListFilters.Global;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Esky.FlightsCache.Processing.Tests.ResultFilters.FlightListFilters.Global
{
    public class NoAdultPricesFilterTests
    {
        private readonly IFlightResultContext _context = null;
        private readonly IFlightListFilter _flightListFilter = new NoAdultPriceFilter();

        [Fact]
        public async Task WhenNoPricesInFlight_ThenFilterFlight()
        {
            var flights = new List<FlightCache>{ CreateFlight(adultPrices: null) };
            var result = await _flightListFilter.Filter(flights, _context);
            result.Flights.Should().HaveCount(0);
        }

        [Fact]
        public async Task WhenEmptyPrices_ThenFilterFlight()
        {
            var flights = new List<FlightCache>{ CreateFlight(adultPrices: []) };
            var result = await _flightListFilter.Filter(flights, _context);
            result.Flights.Should().HaveCount(0);
        }
        
        [Fact]
        public async Task WhenEmptyAdultPrices_OtherPricesNonEmpty_ThenFilterFlight()
        {
            var flights = new List<FlightCache>{ CreateFlight(adultPrices: [], youthPrices: [ new PriceCacheEntry(100,10) ]) };
            var result = await _flightListFilter.Filter(flights, _context);
            result.Flights.Should().HaveCount(0);
        }
        
        [Fact]
        public async Task WhenAdultPrices_OtherPricesNonEmpty_ThenAllowFlight()
        {
            var flights = new List<FlightCache>{ CreateFlight(adultPrices: [ new PriceCacheEntry(100,10) ], youthPrices: [ new PriceCacheEntry(100,10) ]) };
            var result = await _flightListFilter.Filter(flights, _context);
            result.Flights.Should().HaveCount(1);
        }
        
        [Fact]
        public async Task WhenZeroAdultPrices_OtherPricesNonEmpty_ThenFilterFlight()
        {
            var flights = new List<FlightCache>{ CreateFlight(adultPrices: [ new PriceCacheEntry(0,0) ], youthPrices: [ new PriceCacheEntry(100,10) ]) };
            var result = await _flightListFilter.Filter(flights, _context);
            result.Flights.Should().HaveCount(0);
        }
        
        [Fact]
        public async Task WhenZeroAndNonZeroAdultPrices_ThenAllowFlight()
        {
            var flights = new List<FlightCache>{ CreateFlight(adultPrices: [ new PriceCacheEntry(0,0, minimumNumberOPaxes: 1), new PriceCacheEntry(100,0, minimumNumberOPaxes: 2) ]) };
            var result = await _flightListFilter.Filter(flights, _context);
            result.Flights.Should().HaveCount(1);
        }
        
        [Fact]
        public async Task WhenMultipleFlightsNonZeroAdultPrices_ThenAllowFlights()
        {
            var flights = new List<FlightCache>{ CreateFlight(adultPrices: [ new PriceCacheEntry(100,10) ]), CreateFlight(adultPrices: [ new PriceCacheEntry(200,20) ]) };
            var result = await _flightListFilter.Filter(flights, _context);
            result.Flights.Should().HaveCount(2);
        }
        
        
        private static FlightCache CreateFlight(List<PriceCacheEntry> adultPrices = null, List<PriceCacheEntry> youthPrices = null)
        {
            return new FlightCache
            {
                ProviderCode = 6,
                Legs =
                [
                    new()
                    {
                        AvailableSeatsCount = 2,
                        DepartureCode = "KTW",
                        ArrivalCode = "LTN",
                        AirlineCode = "FR",
                        AdultPrices = adultPrices,
                        YouthPrices = youthPrices,
                        DepartureDate = DateTime.UtcNow.AddDays(7),
                        Segments = [new() { DepartureCode = "KTW", ArrivalCode = "LTN", AirlineCode = "FR" }]
                    }
                ]
            };
        }
    }
}