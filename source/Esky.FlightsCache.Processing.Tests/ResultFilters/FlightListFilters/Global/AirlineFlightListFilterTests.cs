using Esky.FlightsCache.MessageContract;
using Esky.FlightsCache.ObjectDirectory;
using Esky.FlightsCache.ResultFilters.Contract;
using Esky.FlightsCache.ResultFilters.FlightListFilters.Global;
using NSubstitute;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Esky.FlightsCache.Processing.Tests.ResultFilters.FlightListFilters.Global
{
    public class AirlineFlightListFilterTests
    {
        private readonly IFlightResultContext _context;
        private readonly IObjectDirectoryService _objectDirectoryService;
        private readonly IFlightListFilter _subject;

        public AirlineFlightListFilterTests()
        {
            _objectDirectoryService = Substitute.For<IObjectDirectoryService>();
            _subject = new AirlineFlightListFilter(_objectDirectoryService);
            _context = Substitute.For<IFlightResultContext>();
        }

        private static List<FlightCache> CreateFlightsList(
            params (string DepartureCode, string ArrivalCode, string AirlineCode)[] destinationTuples)
        {
            return destinationTuples
                .Select(destination =>
                    new FlightCache
                    {
                        Legs = new List<FlightCacheLeg>
                        {
                            new()
                            {
                                DepartureCode = destination.DepartureCode,
                                ArrivalCode = destination.ArrivalCode,
                                AirlineCode = destination.AirlineCode
                            }
                        }
                    })
                .ToList();
        }

        [Fact]
        public async Task Filter_WhenOnlySupportedAirlineCodes_ShallNotFilterOutAnyFlights()
        {
            // Arrange 
            _objectDirectoryService.IsAirline(Arg.Any<string>()).Returns(true);

            var flights = CreateFlightsList((DepartureCode: "WAW", ArrivalCode: "LHR", AirlineCode: "AA"),
                (DepartureCode: "WAW", ArrivalCode: "LTN", AirlineCode: "DL"),
                (DepartureCode: "WMI", ArrivalCode: "STN", AirlineCode: "AF"));

            // Act
            var result = await _subject.Filter(flights, _context);

            // Assert 
            Assert.Equal(flights.Count, result.Flights.Count);
            Assert.Empty(result.ErrorMessage);
        }

        [Fact]
        public async Task Filter_WhenUnknownAirlineCode_ShallFilterOutAllFlights()
        {
            // Arrange
            var unknownAirlineCode = "XX";
            _objectDirectoryService.IsAirline(Arg.Any<string>()).Returns(true);
            _objectDirectoryService.IsAirline(unknownAirlineCode).Returns(false);

            var flights = CreateFlightsList((DepartureCode: "WAW", ArrivalCode: "LHR", AirlineCode: "AA"),
                (DepartureCode: "WAW", ArrivalCode: "LTN", AirlineCode: "DL"),
                (DepartureCode: "WMI", ArrivalCode: "STN", AirlineCode: unknownAirlineCode));

            // Act
            var result = await _subject.Filter(flights, _context);

            // Assert 
            Assert.Equal(flights.Count - 1, result.Flights.Count);
            Assert.Equal($"Not supported airline {unknownAirlineCode} filtered out from search results",
                result.ErrorMessage);
        }

        [Fact]
        public async Task Filter_WhenAirlineCodeIsNull_ShallFilterOutAllFlights()
        {
            // Arrange
            _objectDirectoryService.IsAirline(Arg.Any<string>()).Returns(true);

            var flights = CreateFlightsList((DepartureCode: "WAW", ArrivalCode: "LHR", AirlineCode: "AA"),
                (DepartureCode: "WAW", ArrivalCode: "LTN", AirlineCode: "DL"),
                (DepartureCode: "WMI", ArrivalCode: "STN", AirlineCode: null));

            // Act
            var result = await _subject.Filter(flights, _context);

            // Assert 
            Assert.Equal(flights.Count - 1, result.Flights.Count);
            Assert.Equal("Null airline filtered out from search results", result.ErrorMessage);
        }
    }
}