using Esky.FlightsCache.MessageContract;
using Esky.FlightsCache.ResultFilters.Contract;
using Esky.FlightsCache.ResultFilters.FlightListFilters;
using Esky.FlightsCache.ResultFilters.FlightListFilters.Global;
using Microsoft.Extensions.Options;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Esky.FlightsCache.Processing.Tests.ResultFilters.FlightListFilters.Global
{
    public class AvailableSeatsFilterTests
    {
        private readonly IFlightResultContext _context = new SourceDescriptionWrapper(new SourceDescription
        {
            PaxConfiguration = "*******",
            Name = "flightsearch-api"
        });

        private IFlightListFilter GetFilter(params int[] singleSeatAllowedProviders) =>
            new AvailableSeatsFilter(Options.Create(new FlightsFilterConfiguration
            {
                ProvidersWithAllowedSingleSeatsStoring = singleSeatAllowedProviders
            })) as IFlightListFilter;

        private static FlightCache CreateFlight(int providerCode, int? availableSeatsCount = null)
        {
            return new FlightCache
            {
                ProviderCode = providerCode,
                Legs = new List<FlightCacheLeg>
                {
                    new()
                    {
                        AvailableSeatsCount = availableSeatsCount,
                        DepartureCode = "KTW",
                        ArrivalCode = "LTN",
                        AirlineCode = "FR",
                        Segments = new List<FlightCacheSegment>
                        {
                            new() { DepartureCode = "KTW", ArrivalCode = "LTN", AirlineCode = "FR" }
                        }
                    }
                }
            };
        }

        [Fact]
        public async Task WhenNoAvailableSeatsCountInformation_ThenDontFilter()
        {
            var flights = new List<FlightCache>
            {
                CreateFlight(129),
                CreateFlight(129),
                CreateFlight(129),
                CreateFlight(130),
                CreateFlight(130)
            };

            var result = await GetFilter().Filter(flights, _context);

            result.Flights.Should().HaveCount(5);
            result.StatisticsMessage.Should().BeNullOrWhiteSpace();
        }

        [Fact]
        public async Task WhenAvailableSeatsCountIsGreaterThanOne_ThenDontFilter()
        {
            var flights = new List<FlightCache>
            {
                CreateFlight(129, 2),
                CreateFlight(129),
                CreateFlight(129, 3),
                CreateFlight(130),
                CreateFlight(130)
            };

            var result = await GetFilter().Filter(flights, _context);

            result.Flights.Should().HaveCount(5);
            result.StatisticsMessage.Should().BeNullOrWhiteSpace();
        }

        [Fact]
        public async Task WhenAvailableSeatsCountIsOne_ThenFilterOutEntireProviderResults()
        {
            var flights = new List<FlightCache>
            {
                CreateFlight(129, 1),
                CreateFlight(129),
                CreateFlight(129, 3),
                CreateFlight(130),
                CreateFlight(130, 2)
            };

            var result = await GetFilter().Filter(flights, _context);

            result.Flights.Should().HaveCount(2);
            result.StatisticsMessage.Should().Contain("129");
        }
        
        [Fact]
        public async Task WhenAvailableSeatsCountIsOneAndSomeProvidersAreAllowed_ThenFilterOutSingleProviderResults()
        {
            var singleSeatAllowedProviders = new [] { 129 };
            var flights = new List<FlightCache>
            {
                CreateFlight(129, 1),
                CreateFlight(129),
                CreateFlight(129, 3),
                CreateFlight(130),
                CreateFlight(130, 1),
                CreateFlight(131),
                CreateFlight(131, 2)
            };

            var result = await GetFilter(singleSeatAllowedProviders).Filter(flights, _context);

            result.Flights.Should().HaveCount(5);
            result.StatisticsMessage.Should().Contain("130");
        }
        
        [Theory]
        [InlineData("*******", "flightsearch-api", true)]
        [InlineData("*******", "flightsearch-api", false)]
        [InlineData("*******", "robots", true)]
        [InlineData("*******", "robots", true)]
        public async Task WhenEarlyExitConditionsMet_ThenReturnAllFlights(string paxConfig, string name, bool allFlights)
        {
            var context = new SourceDescriptionWrapper(new SourceDescription
            {
                PaxConfiguration = paxConfig,
                Name = name
            });
            
            var flights = new List<FlightCache>
            {
                CreateFlight(129, 1),
                CreateFlight(129),
                CreateFlight(129, 3),
                CreateFlight(129),
                CreateFlight(129, 1),
                CreateFlight(129),
                CreateFlight(129, 2)
            };
            
            var result = await GetFilter().Filter(flights, context);
            
            if(allFlights) result.Flights.Should().BeEquivalentTo(flights);
            else result.Flights.Should().HaveCount(0);
        }
    }
}