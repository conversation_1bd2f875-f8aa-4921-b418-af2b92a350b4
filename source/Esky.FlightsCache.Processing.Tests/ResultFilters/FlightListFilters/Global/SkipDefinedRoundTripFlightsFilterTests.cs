using Esky.FlightsCache.MessageContract;
using Esky.FlightsCache.Processing.Helpers;
using Esky.FlightsCache.ResultFilters.Contract;
using Esky.FlightsCache.ResultFilters.FlightListFilters.Global;
using NSubstitute;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Esky.FlightsCache.Processing.Tests.ResultFilters.FlightListFilters.Global;

public class SkipDefinedRoundTripFlightsFilterTests
{
    private readonly IFlightResultContext _context = Substitute.For<IFlightResultContext>();
    private readonly IRoundTripMergeOptions _roundTripMergeOptions = Substitute.For<IRoundTripMergeOptions>();
    private readonly SkipDefinedRoundTripFlightsFilter _sut;

    public SkipDefinedRoundTripFlightsFilterTests()
    {
        _sut = new SkipDefinedRoundTripFlightsFilter(_roundTripMergeOptions);
    }

    [Fact]
    public async Task WhenOneWayFlights_ThenNotFiltered()
    {
        // Arrange
        List<FlightCache> inputFlights =
        [
            new() { ReadProviderCode = 1, Supplier = "wizzair", Legs = [new() { AirlineCode = "W6" }] },
            new() { ReadProviderCode = 2, Supplier = "ryanair", Legs = [new() { AirlineCode = "FR" }] }
        ];

        // Act
        var filterResult = await _sut.Filter(inputFlights, _context);

        // Assert
        inputFlights.Should().HaveSameCount(filterResult.Flights);
        filterResult.StatisticsMessage.Should().BeNullOrEmpty();
        filterResult.ErrorMessage.Should().BeNullOrEmpty();
    }

    [Fact]
    public async Task WhenRoundTripFlightsNotMatchingSettings_ThenNotFiltered()
    {
        // Arrange
        List<FlightCache> inputFlights =
        [
            new() { ReadProviderCode = 1, Supplier = "wizzair", Legs = [new() { AirlineCode = "W6" }, new() { AirlineCode = "W6" }] },
            new() { ReadProviderCode = 2, Supplier = "ryanair", Legs = [new() { AirlineCode = "FR" }, new() { AirlineCode = "FR" }] }
        ];

        // Act
        var filterResult = await _sut.Filter(inputFlights, _context);

        // Assert
        inputFlights.Should().HaveSameCount(filterResult.Flights);
        filterResult.StatisticsMessage.Should().BeNullOrEmpty();
        filterResult.ErrorMessage.Should().BeNullOrEmpty();
    }

    [Fact]
    public async Task WhenRoundTripFlightsMatchingSettings_ThenFiltered()
    {
        // Arrange
        _roundTripMergeOptions.CanMergeIntoRoundTrip(1, "wizzair").Returns(true);
        _roundTripMergeOptions.CanMergeIntoRoundTrip(2, "ryanair").Returns(false);
        _roundTripMergeOptions.CanMergeIntoRoundTrip(3, "easyjet").Returns(true);
        List<FlightCache> inputFlights =
        [
            new() { ReadProviderCode = 1, Supplier = "wizzair", Legs = [new() { AirlineCode = "W6" }, new() { AirlineCode = "W6" }] },
            new() { ReadProviderCode = 2, Supplier = "ryanair", Legs = [new() { AirlineCode = "FR" }, new() { AirlineCode = "FR" }] },
            new() { ReadProviderCode = 3, Supplier = "easyjet", Legs = [new() { AirlineCode = "U2" }, new() { AirlineCode = "U2" }] }
        ];

        // Act
        var filterResult = await _sut.Filter(inputFlights, _context);

        // Assert
        filterResult.Flights.Should().HaveCount(1);
        filterResult.StatisticsMessage.Should()
            .NotBeNullOrEmpty()
            .And.Contain("wizzair")
            .And.Contain("easyjet")
            .And.NotContain("ryanair");
        filterResult.ErrorMessage.Should().BeNullOrEmpty();
    }

    [Fact]
    public async Task WhenOneWayAndRoundTripFlightsMatchingSettings_ThenFilteredRoundTrip()
    {
        // Arrange
        _roundTripMergeOptions.CanMergeIntoRoundTrip(1, "wizzair").Returns(true);
        List<FlightCache> inputFlights =
        [
            new() { ReadProviderCode = 1,Supplier = "wizzair",  Legs = [new() { AirlineCode = "W6" }] },
            new() { ReadProviderCode = 1,Supplier = "wizzair",  Legs = [new() { AirlineCode = "W6" }, new() { AirlineCode = "W6" }] }
        ];

        // Act
        var filterResult = await _sut.Filter(inputFlights, _context);

        // Assert
        filterResult.Flights.Should().HaveCount(1);
        filterResult.Flights.Should().AllSatisfy(f => f.IsRoundTrip().Should().BeFalse());
        filterResult.StatisticsMessage.Should()
            .NotBeNullOrEmpty()
            .And.Contain("wizzair");
        filterResult.ErrorMessage.Should().BeNullOrEmpty();
    }
}