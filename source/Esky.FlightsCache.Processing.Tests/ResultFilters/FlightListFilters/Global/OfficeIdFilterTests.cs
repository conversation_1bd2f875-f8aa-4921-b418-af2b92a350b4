using Esky.FlightsCache.MessageContract;
using Esky.FlightsCache.PartnerSettings;
using Esky.FlightsCache.ResultFilters.Contract;
using Esky.FlightsCache.ResultFilters.FlightListFilters.Global;
using NSubstitute;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Esky.FlightsCache.Processing.Tests.ResultFilters.FlightListFilters.Global
{
    public class OfficeIdFilterTests
    {
        private readonly IFlightResultContext _context = null;

        private readonly IPartnerSettingsService _partnerSettingsServiceSubstitute =
            Substitute.For<IPartnerSettingsService>();

        [Fact]
        public async Task WhenNoOfficeId_ThenDontFilter()
        {
            var flights = CreateFlightsList(
                ("SDU", "CPH", "LA", string.Empty),
                ("GIG", "GRU", "LA", string.Empty),
                ("SDU", "CPH", "LA", string.Empty)
            );
            flights[0].Legs[0].Segments[0].FareDetails.FareCode = null;
            flights[1].Legs[0].Segments[0].FareDetails.FareCode = string.Empty;
            flights[2].Legs[0].Segments[0].FareDetails.FareCode = "SAO";

            const string pattern = "^(SAO|POA).*$";
            ConfigurePartnerSettings(pattern);
            var filter = new OfficeIdFilter(_partnerSettingsServiceSubstitute);
            var result = await filter.Filter(flights, _context);

            Assert.Equal(3, result.Flights.Count);
        }

        [Fact]
        public async Task WhenPatternSet_FiltersOutOfficeIdsMatchingThePattern()
        {
            var flights = CreateFlightsList(
                ("SDU", "CPH", "LA", "SAOEY38AA"),
                ("GIG", "GRU", "LA", "SAOEY38AA"),
                ("SDU", "CPH", "LA", "POAEY38AD"),
                ("GIG", "GRU", "LA", "POAEY38AD"),
                ("SDU", "CPH", "LA", "LONU1254W"),
                ("GIG", "GRU", "LA", "LONU1254W")
            );

            const string pattern = "^(SAO|POA).*$";
            ConfigurePartnerSettings(pattern);
            var filter = new OfficeIdFilter(_partnerSettingsServiceSubstitute);

            var result = await filter.Filter(flights, _context);

            Assert.Equal(2, result.Flights.Count);
            Assert.All(result.Flights.Select(f => f.Legs[0].Segments[0].GetOfficeId()),
                x => Assert.DoesNotMatch("(SAO|POA).*", x));
        }

        [Fact]
        public async Task WhenNoPatternSet_ReturnsAllFlights()
        {
            var flights = CreateFlightsList(
                ("WAW", "LHR", "LO", "WAWEY38AA"),
                ("WAW", "LHR", "BA", "WAWEY38AA"),
                ("WAW", "LHR", "LO", "WAWEY38AD"),
                ("WAW", "LHR", "BA", "WAWEY38AD"),
                ("WAW", "LHR", "LO", "LONU1254W"),
                ("WAW", "LHR", "BA", "LONU1254W")
            );
            ConfigurePartnerSettings("");
            var filter = new OfficeIdFilter(_partnerSettingsServiceSubstitute);

            var result = await filter.Filter(flights, _context);

            Assert.Equal(flights.Count, result.Flights.Count);
        }

        [Fact]
        public async Task WhenPatternSet_InformationAboutFilteredOfficeIdsIsReturned()
        {
            var flights = CreateFlightsList(
                ("SDU", "CPH", "LA", "SAOEY38AA"),
                ("GIG", "GRU", "LA", "SAOEY38AA"),
                ("SDU", "CPH", "LA", "POAEY38AD"),
                ("GIG", "GRU", "LA", "POAEY38AD"),
                ("SDU", "CPH", "LA", "LONU1254W"),
                ("GIG", "GRU", "LA", "LONU1254W")
            );

            const string pattern = "^(SAO|POA).*$";
            ConfigurePartnerSettings(pattern);
            var filter = new OfficeIdFilter(_partnerSettingsServiceSubstitute);

            var result = await filter.Filter(flights, _context);

            Assert.Contains("SAOEY38AA", result.StatisticsMessage);
            Assert.Contains("POAEY38AD", result.StatisticsMessage);
            Assert.DoesNotContain("LONU1254W", result.StatisticsMessage);
        }

        private static List<FlightCache> CreateFlightsList(
            params (string DepartureCode, string ArrivalCode, string AirlineCode, string OfficeId)[] destinationTuples)
        {
            return destinationTuples
                .Select(destination =>
                    new FlightCache
                    {
                        Legs = new List<FlightCacheLeg>
                        {
                            new()
                            {
                                DepartureCode = destination.DepartureCode,
                                ArrivalCode = destination.ArrivalCode,
                                AirlineCode = destination.AirlineCode,
                                Segments = new List<FlightCacheSegment>
                                {
                                    new()
                                    {
                                        DepartureCode = destination.DepartureCode,
                                        ArrivalCode = destination.ArrivalCode,
                                        AirlineCode = destination.AirlineCode,
                                        FareDetails = new FareDetails
                                        {
                                            FareCode = "ABC",
                                            OfficeId = destination.OfficeId
                                        }
                                    }
                                }
                            }
                        }
                    })
                .ToList();
        }

        private void ConfigurePartnerSettings(string pattern)
        {
            _partnerSettingsServiceSubstitute.GetPartnerSettingsAsync(Arg.Any<string>()).ReturnsForAnyArgs(
                Task.FromResult(new PartnerSettingsModel
                {
                    SpecialOccasionsSettings =
                        new SpecialOccasionsSettings { DisallowedOfficeIdRegexPattern = pattern }
                }));
        }
    }
}