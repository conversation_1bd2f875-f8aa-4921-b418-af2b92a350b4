using Esky.FlightsCache.MessageContract;
using Esky.FlightsCache.ResultFilters.Contract;
using Esky.FlightsCache.ResultFilters.FlightListFilters;
using Esky.FlightsCache.ResultFilters.FlightListFilters.Global;
using Microsoft.Extensions.Options;
using NSubstitute;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq;

namespace Esky.FlightsCache.Processing.Tests.ResultFilters.FlightListFilters.Global
{
    public class UnknownSeatsFilterTests
    {
        private readonly IFlightResultContext _singlePaxSearchContext = Substitute.For<IFlightResultContext>();
        private readonly IFlightResultContext _singlePaxRobotsContext = Substitute.For<IFlightResultContext>();
        private readonly IFlightResultContext _multiplePaxContext = Substitute.For<IFlightResultContext>();

        public UnknownSeatsFilterTests()
        {
            _singlePaxSearchContext.IsFromRobots.Returns(false);
            _singlePaxSearchContext.PaxConfiguration.Returns("*******");
            
            _singlePaxRobotsContext.IsFromRobots.Returns(true);
            _singlePaxRobotsContext.PaxConfiguration.Returns("*******");
            
            _multiplePaxContext.IsFromRobots.Returns(false);
            _multiplePaxContext.PaxConfiguration.Returns("*******");
        }

        private IFlightListFilter GetFilter(params string[] airlinesWithDisallowedUnknownSeatsStoring) =>
            new UnknownSeatsFilter(Options.Create(new FlightsFilterConfiguration
            {
                AirlinesWithDisallowedUnknownSeatsStoring = airlinesWithDisallowedUnknownSeatsStoring
            })) as IFlightListFilter;

        private static FlightCache CreateFlight(int providerCode, string supplier, string airline, int? availableSeatsCount = null)
        {
            return new FlightCache
            {
                ProviderCode = providerCode,
                Supplier = supplier,
                Legs = new List<FlightCacheLeg>
                {
                    new()
                    {
                        AvailableSeatsCount = availableSeatsCount,
                        DepartureCode = "KTW",
                        ArrivalCode = "LTN",
                        AirlineCode = airline,
                        Segments = new List<FlightCacheSegment>
                        {
                            new() { DepartureCode = "KTW", ArrivalCode = "LTN", AirlineCode = airline }
                        }
                    }
                }
            };
        }

        [Fact]
        public async Task WhenFlightsFromRobots_ThenDontFilter()
        {
            var flights = new List<FlightCache>
            {
                CreateFlight(129, "ryanair", "FR", 3),
                CreateFlight(130, "wizzair", "W6", 1),
                CreateFlight(19, "wizzair", "W6"),
                CreateFlight(19, "spirit", "NK"),
                CreateFlight(19, "easyjet", "U2")
            };

            var result = await GetFilter("W6").Filter(flights, _singlePaxRobotsContext);

            result.Flights.Should().HaveCount(5);
            result.StatisticsMessage.Should().BeNullOrWhiteSpace();
        }

        [Fact]
        public async Task WhenFlightsFromMultiplePaxSearch_ThenDontFilter()
        {
            var flights = new List<FlightCache>
            {
                CreateFlight(129, "ryanair", "FR", 3),
                CreateFlight(130, "wizzair", "W6", 1),
                CreateFlight(19, "wizzair", "W6"),
                CreateFlight(19, "spirit", "NK"),
                CreateFlight(19, "easyjet", "U2")
            };

            var result = await GetFilter("W6").Filter(flights, _multiplePaxContext);

            result.Flights.Should().HaveCount(5);
            result.StatisticsMessage.Should().BeNullOrWhiteSpace();
        }
        
        [Fact]
        public async Task WhenFlightsFromSinglePaxSearch_ThenFilterFlightsWithoutSeatInformation()
        {
            var flights = new List<FlightCache>
            {
                CreateFlight(129, "ryanair", "FR", 3),
                CreateFlight(130, "wizzair", "W6", 1),
                CreateFlight(19, "wizzair", "W6"),
                CreateFlight(19, "spirit", "NK"),
                CreateFlight(19, "easyjet", "U2")
            };

            var result = await GetFilter("W6").Filter(flights, _singlePaxSearchContext);

            result.Flights.Should().HaveCount(4);
            result.Flights
                .Select(f => (f.Legs.Select(l => l.AirlineCode).Single(), f.Legs.Select(l => l.AvailableSeatsCount).Single()))
                .Should().NotContain(("W6", null));
            result.StatisticsMessage.Should().Contain("19, wizzair");
        }
    }
}