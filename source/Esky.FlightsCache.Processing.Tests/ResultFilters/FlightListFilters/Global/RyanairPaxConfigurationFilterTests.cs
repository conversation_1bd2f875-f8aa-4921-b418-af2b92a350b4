using Esky.FlightsCache.MessageContract;
using Esky.FlightsCache.ProviderMapping;
using Esky.FlightsCache.ResultFilters.Contract;
using Esky.FlightsCache.ResultFilters.FlightListFilters.Global;
using NSubstitute;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Esky.FlightsCache.Processing.Tests.ResultFilters.FlightListFilters.Global
{
    public class RyanairPaxConfigurationFilterTests
    {
        private readonly IFlightListFilter _filter = new RyanairPaxConfigurationFilter();

        private static FlightCache CreateFlight(int providerCode)
        {
            return new FlightCache
            {
                ProviderCode = providerCode,
                Legs =
                [
                    new()
                    {
                        AvailableSeatsCount = null,
                        DepartureCode = "KTW",
                        ArrivalCode = "LTN",
                        AirlineCode = "FR",
                        Segments =
                        [
                            new() { DepartureCode = "KTW", ArrivalCode = "LTN", AirlineCode = "FR" }
                        ]
                    }
                ]
            };
        }

        private static IFlightResultContext CreateContext(string paxConfiguration)
        {
            var result = Substitute.For<IFlightResultContext>();
            result.PaxConfiguration.Returns(paxConfiguration);
            return result;
        }

        [Theory]
        [InlineData("1.0.0.0")]
        [InlineData("2.0.0.0")]
        [InlineData("1.1.0.0")]
        [InlineData("1.0.0.1")]
        public async Task WhenPaxConfigurationWithoutChild_PassAllFlights(string paxConfiguration)
        {
            var flights = new List<FlightCache>
            {
                CreateFlight(KnownCacheProviderCodes.Ryanair),
                CreateFlight(KnownCacheProviderCodes.Ryanair),
                CreateFlight(KnownCacheProviderCodes.Ryanair),
                CreateFlight(KnownCacheProviderCodes.Amadeus),
                CreateFlight(KnownCacheProviderCodes.Amadeus)
            };
            var context = CreateContext(paxConfiguration);

            var result = await _filter.Filter(flights, context);

            result.Flights.Should().HaveCount(5);
        }

        [Theory]
        [InlineData("1.0.1.0")]
        [InlineData("1.0.2.0")]
        [InlineData("5.0.3.0")]
        [InlineData("0.1.1.0")]
        [InlineData("1.1.1.1")]
        public async Task WhenPaxConfigurationWithChild_FlilterOutRyanairFlights(string paxConfiguration)
        {
            var flights = new List<FlightCache>
            {
                CreateFlight(KnownCacheProviderCodes.Ryanair),
                CreateFlight(KnownCacheProviderCodes.Ryanair),
                CreateFlight(KnownCacheProviderCodes.Ryanair),
                CreateFlight(KnownCacheProviderCodes.Amadeus),
                CreateFlight(KnownCacheProviderCodes.Amadeus)
            };
            var context = CreateContext(paxConfiguration);

            var result = await _filter.Filter(flights, context);

            result.Flights.Should().HaveCount(2)
                .And.AllSatisfy(x => x.ProviderCode.Should().NotBe(KnownCacheProviderCodes.Ryanair));
        }

        [Theory]
        [InlineData("3.0.0.0")]
        [InlineData("2.1.0.0")]
        [InlineData("0.3.0.0")]
        [InlineData("1.2.0.0")]
        [InlineData("10.0.0.0")]
        public async Task WhenTooManyAdult_FlilterOutRyanairFlights(string paxConfiguration)
        {
            var flights = new List<FlightCache>
            {
                CreateFlight(KnownCacheProviderCodes.Ryanair),
                CreateFlight(KnownCacheProviderCodes.Ryanair),
                CreateFlight(KnownCacheProviderCodes.Ryanair),
                CreateFlight(KnownCacheProviderCodes.Amadeus),
                CreateFlight(KnownCacheProviderCodes.Amadeus)
            };
            var context = CreateContext(paxConfiguration);

            var result = await _filter.Filter(flights, context);

            result.Flights.Should().HaveCount(2)
                .And.AllSatisfy(x => x.ProviderCode.Should().NotBe(KnownCacheProviderCodes.Ryanair));
        }
    }
}