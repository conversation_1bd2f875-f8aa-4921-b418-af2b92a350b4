using Esky.FlightsCache.MessageContract;
using Esky.FlightsCache.ResultFilters.FlightListFilters;
using Esky.FlightsCache.ResultFilters.FlightListFilters.RelationalDatabaseOnly;
using Microsoft.Extensions.Options;
using NSubstitute;
using System.Collections.Generic;
using System.Linq;

namespace Esky.FlightsCache.Processing.Tests.ResultFilters.FlightListFilters.RelationalDatabaseOnly
{
    public class DomesticFlightsOnlyFilterTests
    {
        private static IOptions<FlightsFilterConfiguration> GetConfiguration(
            FlightsFilterConfiguration.DomesticOnlyFilterConfiguration configuration)
        {
            var config = Substitute.For<IOptions<FlightsFilterConfiguration>>();
            config.Value.Returns(new FlightsFilterConfiguration { DomesticOnlyFilter = configuration });
            return config;
        }

        private FlightCache BuildFlight(AirportDetails from, AirportDetails to)
        {
            return new FlightCache
            {
                Legs =
                [
                    new() { DepartureAirportDetails = from, ArrivalAirportDetails = to }
                ]
            };
        }

        private AirportDetails AirportInCountry(string countryCode)
        {
            return new AirportDetails() { CountryCode = countryCode };
        }

        [Fact]
        public void Filter_WhenExcludeInternationalDisabled_NoFlightIsFilteredOut()
        {
            // Arrange
            var config = GetConfiguration(new FlightsFilterConfiguration.DomesticOnlyFilterConfiguration
            {
                ExcludeInternational = false
            });
            var inputFlights = new List<FlightCache>
            {
                BuildFlight(AirportInCountry("PL"), AirportInCountry("PL")),
                BuildFlight(AirportInCountry("PL"), AirportInCountry("US")),
                BuildFlight(AirportInCountry("US"), AirportInCountry("US"))
            };

            IFlightListFilter subject = new DomesticFlightsOnlyFilter(config);

            // Act
            var filterResult = subject.Filter(inputFlights, null);

            // Assert
            filterResult.Flights.Should().BeEquivalentTo(inputFlights);
        }

        [Fact]
        public void Filter_WhenFilteredEnabledAndNoSpecificCountriesRestriction_InternationalFlightAreFilteredOut()
        {
            // Arrange
            var config = GetConfiguration(new FlightsFilterConfiguration.DomesticOnlyFilterConfiguration
            {
                ExcludeInternational = true
            });
            var inputFlights = new List<FlightCache>
            {
                BuildFlight(AirportInCountry("PL"), AirportInCountry("PL")),
                BuildFlight(AirportInCountry("PL"), AirportInCountry("US")),
                BuildFlight(AirportInCountry("US"), AirportInCountry("US"))
            };

            IFlightListFilter subject = new DomesticFlightsOnlyFilter(config);

            // Act
            var filterResult = subject.Filter(inputFlights, null);

            // Assert
            filterResult.Flights
                .Should().HaveCount(2)
                .And.OnlyContain(f => f.Legs
                    .All(leg => leg.DepartureAirportDetails.CountryCode == leg.ArrivalAirportDetails.CountryCode));
        }

        [Fact]
        public void Filter_WhenFilteredEnabledAndOnlySpecificCountriesRestriction_InternationalFlightAreFilteredOut()
        {
            // Arrange
            var config = GetConfiguration(new FlightsFilterConfiguration.DomesticOnlyFilterConfiguration
            {
                ExcludeInternational = true,
                Countries = new[] { "US" }
            });
            var inputFlights = new List<FlightCache>
            {
                BuildFlight(AirportInCountry("PL"), AirportInCountry("PL")),
                BuildFlight(AirportInCountry("PL"), AirportInCountry("US")),
                BuildFlight(AirportInCountry("US"), AirportInCountry("US"))
            };

            IFlightListFilter subject = new DomesticFlightsOnlyFilter(config);

            // Act
            var filterResult = subject.Filter(inputFlights, null);

            // Assert
            filterResult.Flights
                .Should().HaveCount(1)
                .And.OnlyContain(f => f.Legs
                    .All(leg => leg.DepartureAirportDetails.CountryCode == leg.ArrivalAirportDetails.CountryCode))
                .And.OnlyContain(f => f.Legs
                    .All(leg => leg.DepartureAirportDetails.CountryCode == "US"));
        }
    }
}