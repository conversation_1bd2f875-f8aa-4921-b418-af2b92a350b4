using Esky.FlightsCache.MessageContract;
using Esky.FlightsCache.Processing.Helpers;
using Esky.FlightsCache.ResultFilters.FlightListFilters;
using Esky.FlightsCache.ResultFilters.FlightListFilters.RelationalDatabaseOnly;
using Microsoft.Extensions.Options;
using NSubstitute;
using System.Collections.Generic;
using System.Linq;

namespace Esky.FlightsCache.Processing.Tests.ResultFilters.FlightListFilters.RelationalDatabaseOnly
{
    public class SkipRoundTripFlightListFilterTests
    {
        private static IOptions<FlightsFilterConfiguration> GetConfiguration(bool isRtSaveToSqlEnabled)
        {
            var config = Substitute.For<IOptions<FlightsFilterConfiguration>>();
            config.Value.Returns(new FlightsFilterConfiguration
            {
                IsRtSaveToSqlEnabled = isRtSaveToSqlEnabled
            });
            return config;
        }

        private FlightCache GetOneWayFlight()
        {
            return new FlightCache
            {
                Legs = [new() { Segments = [new()] }]
            };
        }

        private FlightCache GetRoundTripFlight()
        {
            return new FlightCache
            {
                Legs =
                [
                    new() { Segments = [new()] },
                    new() { Segments = [new()] }
                ]
            };
        }

        [Fact]
        public void Filter_WhenIsRtSaveToSqlDisabled_AllRtFlightsAreFilteredOut()
        {
            // Arrange
            var config = GetConfiguration(false);
            var inputFlights = new List<FlightCache>
            {
                GetOneWayFlight(),
                GetOneWayFlight(),
                GetRoundTripFlight(),
                GetRoundTripFlight(),
                GetRoundTripFlight()
            };

            IFlightListFilter subject = new SkipRoundTripFlightListFilter(config);

            // Act
            var filterResult = subject.Filter(inputFlights, null);

            // Assert
            Assert.True(filterResult.Flights.Count() == 2);
            Assert.All(filterResult.Flights, f => Assert.False(f.IsRoundTrip()));
        }

        [Fact]
        public void Filter_WhenIsRtSaveToSqlEnabled_NoneFlightIsFilteredOut()
        {
            // Arrange
            var config = GetConfiguration(true);
            var inputFlights = new List<FlightCache>
            {
                GetOneWayFlight(),
                GetOneWayFlight(),
                GetRoundTripFlight(),
                GetRoundTripFlight(),
                GetRoundTripFlight()
            };

            IFlightListFilter subject = new SkipRoundTripFlightListFilter(config);

            // Act
            var filterResult = subject.Filter(inputFlights, null);

            // Assert
            Assert.True(filterResult.Flights.Count() == 5);
            Assert.Equal(inputFlights, filterResult.Flights);
        }
    }
}