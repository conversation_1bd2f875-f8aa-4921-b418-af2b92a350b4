using Esky.FlightsCache.ResultFilters.FlightListFilters;
using Esky.FlightsCache.ResultFilters.FlightListFilters.Utilities;
using Microsoft.Extensions.Options;
using System;

namespace Esky.FlightsCache.Processing.Tests.ResultFilters
{
    public class TimeOfDayBucketizerTests
    {
        private readonly ITimeOfDayBucketizer _subject;

        public TimeOfDayBucketizerTests()
        {
            _subject = new TimeOfDayBucketizer(
                Options.Create(new FlightsFilterConfiguration { TimeSlotRanges = "09:15;14:00;18:00" }));
        }

        [Theory]
        [InlineData("2018-07-20 00:30", 0)]
        [InlineData("2018-07-20 02:30", 0)]
        [InlineData("2018-07-20 04:30", 0)]
        [InlineData("2018-07-20 06:30", 0)]
        [InlineData("2018-07-20 08:30", 0)]
        [InlineData("2018-07-20 10:30", 1)]
        [InlineData("2018-07-20 12:30", 1)]
        [InlineData("2018-07-20 14:30", 2)]
        [InlineData("2018-07-20 16:30", 2)]
        [InlineData("2018-07-20 18:30", 3)]
        [InlineData("2018-07-20 20:30", 3)]
        [InlineData("2018-07-20 22:30", 3)]
        public void GetSlotNumber(string inputDateTime, int expectedSlotNumber)
        {
            var dt = DateTime.Parse(inputDateTime);

            var slotNumber = _subject.GetSlotNumber(dt);

            Assert.Equal(expectedSlotNumber, slotNumber);
        }
    }
}