using Esky.FlightsCache.Processing.Helpers;
using Esky.FlightsCache.ResultFilters.AirportCode;
using System.Linq;

namespace Esky.FlightsCache.Processing.Tests.ResultFilters.AirportCode
{
    public class AirportCollectionTests
    {
        private readonly AirportCollection _subject = new(
            [
                new()
                {
                    Code = "LCJ",
                    Multiport = "LCJ",
                    NearbyAirports = [],
                    CityCode = "LCJ",
                    CountryCode = "PL",
                    ContinentCode = "EU"
                },
                new()
                {
                    Code = "WAW",
                    Multiport = "WAWA",
                    NearbyAirports = ["WMI"],
                    CityCode = "WAW",
                    CountryCode = "PL",
                    ContinentCode = "EU"
                },
                new()
                {
                    Code = "WMI",
                    Multiport = "WAWA",
                    NearbyAirports = ["WAW"],
                    CityCode = "WAW",
                    CountryCode = "PL",
                    ContinentCode = "EU"
                }
            ]
        );


        [Theory]
        [InlineData("LCJ")]
        [InlineData("WAW")]
        [InlineData("WMI")]
        public void GetAirport(string airportCode)
        {
            // Act
            var result = _subject[airportCode];

            // Assert
            result.Should().NotBeNull();
            result.Code.Should().Be(airportCode);
        }

        [Theory]
        [InlineData("WMI", new[] { "WMI" })]
        [InlineData("WAWA", new[] { "WAW", "WMI" })]
        [InlineData("WAW*", new[] { "WAW", "WMI" })]
        public void GetSupportedCodes(string searchCode, string[] expectedSupportedCodes)
        {
            // Act
            var result = _subject.GetSupportedCodes(searchCode);

            // Assert
            result.Should().BeEquivalentTo(expectedSupportedCodes.Select(FlightCacheHelpers.GetAirportHashCode));
        }

        [Theory]
        [InlineData("WMI", new[] { "WMI" })]
        [InlineData("WAWA", new[] { "WAW", "WMI" })]
        [InlineData("WAW*", new[] { "WAW", "WMI" })]
        public void ContainsAirportCode(string searchCode, string[] expectedSupportedCodes)
        {
            expectedSupportedCodes
                .Select(expectedCode => _subject.ContainsAirport(searchCode, expectedCode.GetAirportHashCode()))
                .Should().BeEquivalentTo(expectedSupportedCodes.Select(_ => true));
        }

        [Theory]
        [InlineData("WMI", true)]
        [InlineData("WAWA", true)]
        [InlineData("WAW*", true)]
        [InlineData("WAWX", false)]
        public void ContainsMultiportCode(string searchCode, bool expected)
        {
            // Act
            var result = _subject.ContainsMultiport(searchCode);

            // Assert
            result.Should().Be(expected);
        }

        [Theory]
        [InlineData("WMI", new[] { "WMI", "WAWA" })]
        [InlineData("WAW", new[] { "WAW", "WAWA" })]
        [InlineData("LCJ", new[] { "LCJ" })]
        public void GetSearchCodes_IncludeAirportAndParentMultiportIfExists(string airportCode, string[] expectedCodes)
        {
            // Act
            var result = _subject.GetSearchCodes(airportCode.GetAirportHashCode());

            // Assert
            result.Should().BeEquivalentTo(expectedCodes);
        }

        [Theory]
        [InlineData("LCJ", 7497)]
        [InlineData("WAW", 14894)]
        [InlineData("WMI", 15192)]
        [InlineData("WMI*", 15192)]
        public void GetAirportHashCode_ReturnsCorrectShort(string code, short hashCode)
        {
            code.GetAirportHashCode().Should().Be(hashCode);
        }

        [Theory]
        [InlineData("LCJ", 7497)]
        [InlineData("WAW", 14894)]
        [InlineData("WMI", 15192)]
        public void GetAirportCode_ReturnsCorrectString(string code, short hashCode)
        {
            hashCode.GetAirportCode().Should().Be(code);
        }
    }
}