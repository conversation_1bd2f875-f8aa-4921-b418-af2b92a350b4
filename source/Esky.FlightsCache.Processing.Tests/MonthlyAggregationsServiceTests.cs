using Esky.FlightsCache.Database.Aggregations;

namespace Esky.FlightsCache.Processing.Tests;

public class MonthlyAggregationsServiceTests
{
    public static readonly TheoryData<decimal?, int> TestData = new()
    {
        { null, 0 },
        { 0, 0 },
        { decimal.MinValue, int.MinValue },
        { decimal.MaxValue, int.MaxValue },
        { (decimal)int.MinValue - 1, int.MinValue },
        { (decimal)int.MaxValue + 1, int.MaxValue },
        { 2000.9934M, 2000993400 },
        { -2133.912M, -2133912000 }
    };

    [Theory, MemberData(nameof(TestData))]
    public void ToIntegerStandardScore(decimal? value, int expectedValue)
    {
        var result = MonthlyAggregationsService.ToIntegerStandardScore(value);
        result.Should().Be(expectedValue);
    }
}