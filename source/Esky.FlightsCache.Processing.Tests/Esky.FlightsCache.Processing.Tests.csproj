<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <IsPackable>false</IsPackable>
        <LangVersion>latest</LangVersion>
        <RootNamespace>Esky.FlightsCache.Processing.Tests</RootNamespace>
    </PropertyGroup>
    
    <ItemGroup>
        <PackageReference Include="Autofac.Extensions.DependencyInjection" Version="8.0.0" />
        <PackageReference Include="FluentAssertions" Version="6.10.0" />
        <PackageReference Include="FsCheck" Version="2.16.5" />
        <PackageReference Include="FsCheck.Xunit" Version="2.16.5" />
        <PackageReference Include="Microsoft.Extensions.Caching.Memory" Version="8.0.0" />
        <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.4.1" />
        <PackageReference Include="morelinq" Version="3.3.2" />
        <PackageReference Include="NSubstitute" Version="5.3.0" />
        <PackageReference Include="xunit" Version="2.4.2" />
        <PackageReference Include="xunit.runner.visualstudio" Version="2.4.5">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="XunitXml.TestLogger" Version="3.0.70" />
        <DotNetCliToolReference Include="dotnet-xunit" Version="2.3.1" />
    </ItemGroup>

    <ItemGroup>
        <EmbeddedResource Include="Utils\ResponseData\ESKY_80_WAW_JKT_20221120_20221127.json" />
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\Esky.FlightsCache.Processing\Esky.FlightsCache.Processing.csproj" />
    </ItemGroup>
</Project>
