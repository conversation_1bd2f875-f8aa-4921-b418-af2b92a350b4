using Esky.FlightsCache.CurrencyProvider;
using Esky.FlightsCache.MessageContract;
using Esky.FlightsCache.Processing.PriceDecorators;
using Esky.FlightsCache.Processing.PriceDecorators.EasyJet;
using NSubstitute;
using System.Threading.Tasks;

namespace Esky.FlightsCache.Processing.Tests.Processing;

public class CustomFeeDecoratorTests
{
    private readonly ICurrencyRatioProvider _currencyRatioProvider = Substitute.For<ICurrencyRatioProvider>();

    private readonly EasyJetExtraFee3Pax _easyJetExtraFee3Pax;
    private readonly EasyJetExtraFee5Pax _easyJetExtraFee5Pax;

    public CustomFeeDecoratorTests()
    {
        _currencyRatioProvider.GetRatio(Arg.Any<string>(), Arg.Any<string>()).Returns(1.0m);
        
        _easyJetExtraFee5Pax = new EasyJetExtraFee5Pax(_currencyRatioProvider);
        _easyJetExtraFee3Pax = new EasyJetExtraFee3Pax(_currencyRatioProvider);
    }
    
    [Fact]
    public async Task WhenMultipleExtraFee_ThenAppliedWithOrder()
    {
        var decorator = new CustomFeeDecorator([_easyJetExtraFee5Pax, _easyJetExtraFee3Pax]);

        var flights = new FlightCache[] {new()
        {
            Legs =
            [
                new FlightCacheLeg { AirlineCode = "U2", CurrencyCode = "EUR", AdultPrices = [new PriceCacheEntry(100, 23)] }
            ]
        }};
        await decorator.Decorate(flights);
        
        flights[0].Legs[0].AdultPrices.Should().HaveCount(3);
        flights[0].Legs[0].AdultPrices.Should().BeEquivalentTo([
            new PriceCacheEntry(100, 23, 1),
            new PriceCacheEntry(102, 23, 3),
            new PriceCacheEntry(105, 23, 5)
        ]);
    }
    
    
    
    [Fact]
    public async Task GivenProvidersMultiplePrices_WhenMultipleExtraFee_ThenAppliedWithOrder()
    {
        var decorator = new CustomFeeDecorator([_easyJetExtraFee5Pax, _easyJetExtraFee3Pax]);

        var flights = new FlightCache[] {new()
        {
            Legs =
            [
                new FlightCacheLeg { AirlineCode = "U2", CurrencyCode = "EUR", AdultPrices = 
                    [
                        new PriceCacheEntry(90, 23, minimumNumberOPaxes: 1),
                        new PriceCacheEntry(100, 23, minimumNumberOPaxes: 2)
                    ] }
            ]
        }};
        await decorator.Decorate(flights);
        
        flights[0].Legs[0].AdultPrices.Should().HaveCount(4);
        flights[0].Legs[0].AdultPrices.Should().BeEquivalentTo([
            new PriceCacheEntry(90, 23, 1),
            new PriceCacheEntry(100, 23, 2),
            new PriceCacheEntry(102, 23, 3),
            new PriceCacheEntry(105, 23, 5)
        ]);
    }  
}
