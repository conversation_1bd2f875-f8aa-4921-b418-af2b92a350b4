using Esky.FlightsCache.Database.Timetables;
using Esky.FlightsCache.MessageContract;
using NSubstitute;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Esky.FlightsCache.Processing.Tests.Processing
{
    public class TravelFusionTimetableServiceTests
    {
        [Fact]
        public async Task Generate_Should_Upsert_Timetable_Items()
        {
            // arrange 
            List<TimetableItem> timetableItemsAdded = [];
            var timetableRepository = Substitute.For<ITimetableRepository>();
            timetableRepository
                .UpsertTimetableItemsAsync(Arg.Do<IReadOnlyList<TimetableItem>>(i => timetableItemsAdded.AddRange(i)))
                .Returns(Task.CompletedTask);
            TravelFusionTimetableService travelFusionTimetableService = new( timetableRepository);
            var flights = new List<FlightCache>
            {
                new()
                {
                    Supplier = "SupplierA",
                    Legs =
                    [
                        new()
                        {
                            DepartureDate = new DateTime(2022, 1, 1),
                            DepartureCode = "A",
                            ArrivalCode = "B"
                        }
                    ]
                },
                new()
                {
                    Supplier = "SupplierB",
                    Legs =
                    [
                        new()
                        {
                            DepartureDate = new DateTime(2022, 1, 1),
                            DepartureCode = "A",
                            ArrivalCode = "B"
                        }
                    ]
                },
                new()
                {
                    Supplier = "SupplierA",
                    Legs =
                    [
                        new()
                        {
                            DepartureDate = new DateTime(2022, 1, 2),
                            DepartureCode = "A",
                            ArrivalCode = "B"
                        }
                    ]
                },
                new()
                {
                    Supplier = "SupplierA",
                    Legs =
                    [
                        new()
                        {
                            DepartureDate = new DateTime(2022, 1, 2),
                            DepartureCode = "A",
                            ArrivalCode = "C"
                        }
                    ]
                }
            };

            // act
            await travelFusionTimetableService.GenerateAsync(flights);

            // assert
            Assert.Collection(timetableItemsAdded,
                i =>
                {
                    Assert.Equal("A-B", i.Id.Route);
                    Assert.Equal(new DateTime(2022, 1, 1), i.Id.DepartureDate);
                    Assert.Equal(new[] { "SupplierA", "SupplierB" }, i.Suppliers);
                    Assert.NotEqual(DateTime.MinValue, i.Timestamp);
                },
                i =>
                {
                    Assert.Equal("A-B", i.Id.Route);
                    Assert.Equal(new DateTime(2022, 1, 2), i.Id.DepartureDate);
                    Assert.Equal(new[] { "SupplierA" }, i.Suppliers);
                    Assert.NotEqual(DateTime.MinValue, i.Timestamp);
                },
                i =>
                {
                    Assert.Equal("A-C", i.Id.Route);
                    Assert.Equal(new DateTime(2022, 1, 2), i.Id.DepartureDate);
                    Assert.Equal(new[] { "SupplierA" }, i.Suppliers);
                    Assert.NotEqual(DateTime.MinValue, i.Timestamp);
                });
        }
    }
}