using Esky.FlightsCache.CurrencyProvider;
using Esky.FlightsCache.MessageContract;
using Esky.FlightsCache.Processing.TechnicalMargin;
using Esky.FlightsCache.ProviderMapping;
using NSubstitute;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Esky.FlightsCache.Processing.Tests.Processing
{
    public class CacheRequestFlightsDecoratorTests
    {
        
        private readonly IFlightsCacheProviderConverter _providerConverter = Substitute.For<IFlightsCacheProviderConverter>();
        private readonly ICurrencyRatioProvider _currencyRatioProvider = new MockCurrencyRatioProvider();
        private readonly IMarginConfigurationService _marginConfigurationService = Substitute.For<IMarginConfigurationService>();
        private readonly ITechnicalMarginCalculator _technicalMarginCalculator = Substitute.For<ITechnicalMarginCalculator>();
    
        private readonly ProviderCodeCacheRequestFlightsDecorator _providerCodeAndMarginDecorator;

        public CacheRequestFlightsDecoratorTests()
        {
            _providerCodeAndMarginDecorator = new ProviderCodeCacheRequestFlightsDecorator(
                _providerConverter, _currencyRatioProvider, _marginConfigurationService, _technicalMarginCalculator);
            
            // mock default behavior
            _marginConfigurationService
                .GetMarginConfiguration(Arg.Any<int>(), Arg.Any<string>(), Arg.Any<string[]>())
                .Returns(MarginConfiguration.ZeroMargin);
        }
        
        [Fact]
        public void WhenMarginSpecified_ShouldAddMarginToBasePrice()
        {
            _marginConfigurationService
                .GetMarginConfiguration(Arg.Any<int>(), Arg.Any<string>(), Arg.Any<string[]>())
                .Returns(new MarginConfiguration { Amount = 40, Currency = "PLN" });

            // Margin currency is the same as provider currency:
            var flight = CreateFlight(100, "PLN");
            _providerCodeAndMarginDecorator.Decorate([flight]);

            Assert.Equal(140, flight.Legs.First().AdultPrices.First().BasePrice);

            // With currency conversion:
            flight = CreateFlight();
            _providerCodeAndMarginDecorator.Decorate([flight]);

            Assert.Equal(110, flight.Legs.First().AdultPrices.First().BasePrice);
        }

        
        [Theory]
        [InlineData(0)]
        [InlineData(14)]
        [InlineData(-14)]
        public void WhenRelativeMarginSpecified_ShouldAddMarginToBasePrice(decimal margin)
        {
            _marginConfigurationService
                .GetMarginConfiguration(Arg.Any<int>(), Arg.Any<string>(), Arg.Any<string[]>())
                .Returns(new MarginConfiguration { Amount = margin, MarginType = MarginType.Relative });
            
            // Margin currency is the same as provider currency:
            var flight = CreateFlight(100, "PLN");
            _providerCodeAndMarginDecorator.Decorate([flight]);
        
            Assert.Equal(margin + 100, flight.Legs.First().AdultPrices.First().BasePrice);
        }
        
        
        [Fact]
        public void WhenMarginZero_ShouldNotChangeBasePrice()
        {
            _marginConfigurationService
                .GetMarginConfiguration(Arg.Any<int>(), Arg.Any<string>(), Arg.Any<string[]>())
                .Returns(MarginConfiguration.ZeroMargin);
        
            var flight = CreateFlight(100, "PLN");
            _providerCodeAndMarginDecorator.Decorate([flight]);
            Assert.Equal(100, flight.Legs.First().AdultPrices.First().BasePrice);
        }
        
        [Fact]
        public void WhenWriteConfigurationSpecified_ShouldApplyCacheProviderCode()
        {
            _providerConverter
                .GetWriteConfiguration(Arg.Any<int>(), Arg.Any<string[]>())
                .ReturnsForAnyArgs(new FlightCacheWriteConfiguration { CacheProviderCode = 2, ReadProviderCode = 15 });
            // Margin currency is the same as provider currency:
            var flight = CreateFlight(providerCode: 1);
            _providerCodeAndMarginDecorator.Decorate([flight]);
            Assert.Equal(2, flight.ProviderCode);
            Assert.Equal(15, flight.ReadProviderCode);
        }
        
        [Fact]
        public void WhenWriteConfigurationNotSpecified_ShouldLeaveOriginalProviderCode()
        {
            _providerConverter
                .GetWriteConfiguration(Arg.Any<int>(), Arg.Any<string[]>())
                .ReturnsForAnyArgs((FlightCacheWriteConfiguration)null);
        
            // Margin currency is the same as provider currency:
            var flight = CreateFlight(providerCode: 1);
            _providerCodeAndMarginDecorator.Decorate([flight]);
            Assert.Equal(1, flight.ProviderCode);
            Assert.Equal(1, flight.ReadProviderCode);
        }
        
        [Fact]
        public void WhenProviderIsIncludedSetOfficeIdAsSupplier()
        {
            var subject = new OfficeIdAsSupplierFlightsDecorator();

            FlightCache[] flights = [
                CreateFlight(providerCode: 35),
                CreateFlight(providerCode: 15),
                CreateFlight(providerCode: 81),
                CreateFlight(providerCode: 74)
            ];
            subject.Decorate(flights);
            
            flights[0].Supplier.Should().Be("officeId123");
            flights[1].Supplier.Should().BeNull();
            flights[2].Supplier.Should().Be("officeId123");
            flights[3].Supplier.Should().Be("officeId123");
        }

        private FlightCache CreateFlight(int basePrice = 100, string currency = "EUR", int providerCode = 1)
        {
            return new FlightCache
            {
                ProviderCode = providerCode,
                Legs =
                [
                    new FlightCacheLeg
                    {
                        AdultPrices = CreatePrices(basePrice),
                        AirlineCode = "FR",
                        CurrencyCode = currency,
                        Segments =
                        [
                            new FlightCacheSegment { FareDetails = new FareDetails { OfficeId = "officeId123" } }
                        ]
                    }
                ]
            };
        }

        private List<PriceCacheEntry> CreatePrices(int basePrice)
        {
            return new List<PriceCacheEntry> { new() { BasePrice = basePrice, MinimumNumberOfPaxes = 1 } };
        }

        private class MockCurrencyRatioProvider : ICurrencyRatioProvider
        {
            public decimal GetRatio(string sourceCurrency, string targetCurrency)
            {
                if (sourceCurrency == "EUR" && targetCurrency == "PLN")
                {
                    return 4;
                }

                if ((sourceCurrency == "PLN") & (targetCurrency == "EUR"))
                {
                    return 0.25m;
                }

                if (sourceCurrency == targetCurrency)
                {
                    return 1;
                }

                throw new NotSupportedException();
            }
        }
    }
}