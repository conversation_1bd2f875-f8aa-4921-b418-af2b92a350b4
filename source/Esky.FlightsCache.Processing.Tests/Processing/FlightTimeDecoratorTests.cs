using Esky.FlightsCache.MessageContract;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Esky.FlightsCache.Processing.Tests.Processing;

public class FlightTimeDecoratorTests
{
    [Fact]
    public async Task Decorate_ShouldHandleDifferentTimeZones()
    {
        var decorator = new FlightTimeDecorator();

        var flights = new List<FlightCache>
        {
            new()
            {
                Legs =
                [
                    new FlightCacheLeg
                    {
                        DepartureAirportDetails = new AirportDetails { TimeZoneId = "Eastern Standard Time" },
                        ArrivalAirportDetails = new AirportDetails { TimeZoneId = "Pacific Standard Time" },
                        DepartureDate = new DateTime(2023, 10, 1, 10, 0, 0),
                        ArrivalDate = new DateTime(2023, 10, 1, 13, 0, 0)
                    }
                ]
            }
        };

        await decorator.Decorate(flights);

        Assert.Equal(TimeSpan.FromHours(6), flights.First().Legs.First().FlightTime);
    }

    [Fact]
    public async Task Decorate_ShouldFallbackToDefaultTimeZone()
    {
        var decorator = new FlightTimeDecorator();

        var flights = new List<FlightCache>
        {
            new()
            {
                Legs =
                [
                    new FlightCacheLeg
                    {
                        DepartureAirportDetails = new AirportDetails { TimeZoneId = null },
                        ArrivalAirportDetails = new AirportDetails { TimeZoneId = null },
                        DepartureDate = new DateTime(2023, 10, 1, 10, 0, 0),
                        ArrivalDate = new DateTime(2023, 10, 1, 12, 0, 0)
                    }
                ]
            }
        };

        await decorator.Decorate(flights);

        Assert.Equal(TimeSpan.FromHours(2), flights.First().Legs.First().FlightTime);
    }

    [Fact]
    public async Task Decorate_ShouldHandleZeroFlightTime()
    {
        var decorator = new FlightTimeDecorator();

        var flights = new List<FlightCache>
        {
            new()
            {
                Legs =
                [
                    new FlightCacheLeg
                    {
                        DepartureAirportDetails = new AirportDetails { TimeZoneId = "UTC" },
                        ArrivalAirportDetails = new AirportDetails { TimeZoneId = "UTC" },
                        DepartureDate = new DateTime(2023, 10, 1, 0, 0, 0),
                        ArrivalDate = new DateTime(2023, 10, 1, 0, 0, 0)
                    }
                ]
            }
        };

        await decorator.Decorate(flights);

        Assert.Equal(TimeSpan.Zero, flights.First().Legs.First().FlightTime);
    }
}