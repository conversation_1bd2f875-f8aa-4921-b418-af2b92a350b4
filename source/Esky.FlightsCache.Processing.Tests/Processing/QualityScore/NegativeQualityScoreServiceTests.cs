// using Esky.FlightsCache.Common.Model;
// using Esky.FlightsCache.GoogleFlightsFeed.TransactionFee;
// using Esky.FlightsCache.MessageContract;
// using Esky.FlightsCache.Processing.NegativeQualityScore;
// using Esky.FlightsCache.Processing.Utils;
// using Google.Api.Gax.Grpc;
// using Google.Cloud.AIPlatform.V1;
// using Microsoft.Extensions.Logging;
// using Microsoft.Extensions.Options;
// using NSubstitute;
// using System;
// using System.Collections.Generic;
// using System.Linq;
// using System.Threading.Tasks;
// using Value = Google.Protobuf.WellKnownTypes.Value;
//
// namespace Esky.FlightsCache.Processing.Tests.Processing.QualityScore
// {
//     public class NegativeQualityScoreServiceTests
//     {
//         private readonly NegativeQualityScoreService _sut;
//         private readonly PredictionServiceClient _predictionServiceClient = Substitute.For<PredictionServiceClient>();
//
//         private readonly ICurrencyExchangeServiceWrapper _currencyExchangeService =
//             Substitute.For<ICurrencyExchangeServiceWrapper>();
//
//         private readonly IFlightTimeCalculator _flightTimeCalculator = Substitute.For<IFlightTimeCalculator>();
//
//         private readonly ILogger<NegativeQualityScoreService> _logger =
//             Substitute.For<ILogger<NegativeQualityScoreService>>();
//
//         private readonly IOptions<NegativeQualityScoreOptions> _nqsOptions =
//             Substitute.For<IOptions<NegativeQualityScoreOptions>>();
//
//         private const string _currencyCode = "EUR";
//         private const double _nqsValue = 5.0;
//
//         public NegativeQualityScoreServiceTests()
//         {
//             _currencyExchangeService.ConvertCurrency(Arg.Any<decimal>(), Arg.Any<string>(), Arg.Any<string>())
//                 .Returns(x => (decimal)x[0]);
//             _flightTimeCalculator.CalculateFlightTime(Arg.Any<string>(),
//                     Arg.Any<DateTime>(),
//                     Arg.Any<string>(),
//                     Arg.Any<DateTime>())
//                 .Returns(x => (DateTime)x[3] - (DateTime)x[1]);
//             _nqsOptions.Value.Returns(new NegativeQualityScoreOptions
//             {
//                 ScoringCurrencyCode = _currencyCode,
//                 PredictionRequestEndpoint = string.Empty,
//                 PredictionServiceClientEndpoint = string.Empty
//             });
//
//             _sut = new NegativeQualityScoreService(_currencyExchangeService, _flightTimeCalculator,
//                 _predictionServiceClient, _logger, _nqsOptions);
//         }
//
//         [Fact]
//         public async Task GivenNQSService_WhenTwoFlights_ThenReturnTwoNSQs()
//         {
//             // arrange
//             var today = DateTime.Today;
//             var flights = new List<FlightWrapper>
//             {
//                 new() { Legs = new List<FlightCacheLeg> { CreateLeg(today, 1000, 0, segments: (1, 2)) } },
//                 new() { Legs = new List<FlightCacheLeg> { CreateLeg(today, 1000, 0, segments: (2, 3)) } }
//             };
//
//             _predictionServiceClient.PredictAsync(Arg.Any<PredictRequest>(), Arg.Any<CallSettings>())
//                 .Returns(x => new PredictResponse
//                 {
//                     Predictions = { ((PredictRequest)x[0]).Instances.Select(v => Value.ForNumber(_nqsValue)) }
//                 });
//
//             // act
//             var result = (await _sut.GetQualityScoreForFlights(flights)).ToArray();
//
//             // assert
//             await _predictionServiceClient.Received(1).PredictAsync(Arg.Any<PredictRequest>(), Arg.Any<CallSettings>());
//             result[0].Should().BeEquivalentTo(flights[0] as FlightCache);
//             result[1].Should().BeEquivalentTo(flights[1] as FlightCache);
//             result[0].NegativeQualityScore.Should().Be(_nqsValue);
//             result[1].NegativeQualityScore.Should().Be(_nqsValue);
//         }
//
//         [Fact]
//         public async Task GivenNQSService_WhenNoFlights_ThenNotQueryForNSQs()
//         {
//             // arrange
//             var flights = new List<FlightWrapper>(0);
//
//             // act
//             var result = (await _sut.GetQualityScoreForFlights(flights)).ToArray();
//
//             // assert
//             await _predictionServiceClient.Received(0).PredictAsync(Arg.Any<PredictRequest>(), Arg.Any<CallSettings>());
//             result.Length.Should().Be(0);
//         }
//
//         [Fact]
//         public async Task GivenNQSService_WhenTwoFlights_AndIncorrectNumberOfNqs_ThenReturnWithoutNqs()
//         {
//             // arrange
//             var today = DateTime.Today;
//             var flights = new List<FlightWrapper>
//             {
//                 new() { Legs = new List<FlightCacheLeg> { CreateLeg(today, 1000, 0, segments: (1, 2)) } },
//                 new() { Legs = new List<FlightCacheLeg> { CreateLeg(today, 1000, 0, segments: (2, 3)) } }
//             };
//
//             _predictionServiceClient.PredictAsync(Arg.Any<PredictRequest>(), Arg.Any<CallSettings>())
//                 .Returns(x => new PredictResponse { Predictions = { Value.ForNumber(_nqsValue) } });
//
//             // act
//             var result = (await _sut.GetQualityScoreForFlights(flights)).ToArray();
//
//             // assert
//             result.Length.Should().Be(2);
//             result.Should().OnlyContain(flightWrapper => flightWrapper.NegativeQualityScore == null);
//         }
//
//         [Fact]
//         public void Test_GetNqsCalculationContext_OneWay_OneSegmentFlight()
//         {
//             // arrange
//             var today = DateTime.Today;
//             var flight = new FlightWrapper
//             {
//                 Legs = new List<FlightCacheLeg> { CreateLeg(today, 1000, 0, segments: (1, 2)) }
//             };
//             var expected = new NegativeQualityScoreService.NqsCalculationContext
//             {
//                 FlightDurationPerTrip = 60.0, Leg1DepartureHour = 1.0, PricePerLeg = 1000
//             };
//
//             // act
//             var context = _sut.GetNqsCalculationContext(flight);
//
//             // assert
//             context.Should().NotBeNull();
//             context.Should().BeEquivalentTo(expected);
//         }
//
//         [Fact]
//         public void Test_GetNqsCalculationContext_RoundTrip_OneSegmentFlights()
//         {
//             // arrange
//             var today = DateTime.Today;
//             var flight = new FlightWrapper
//             {
//                 Legs = new List<FlightCacheLeg>
//                 {
//                     CreateLeg(today, 1000, 0, segments: (1, 2)),
//                     CreateLeg(today.AddDays(5), 1000, 0, segments: (1, 2))
//                 }
//             };
//             var expected = new NegativeQualityScoreService.NqsCalculationContext
//             {
//                 FlightDurationPerTrip = 60.0,
//                 Leg1DepartureHour = 1.0,
//                 Leg2DepartureHour = 1.0,
//                 PricePerLeg = 1000,
//                 RoundTripDays = 5
//             };
//
//             // act
//             var context = _sut.GetNqsCalculationContext(flight);
//
//             // assert
//             context.Should().NotBeNull();
//             context.Should().BeEquivalentTo(expected);
//         }
//
//         [Fact]
//         public void Test_GetNqsCalculationContext_RoundTrip_TwoSegmentFlights()
//         {
//             // arrange
//             var today = DateTime.Today;
//             var flight = new FlightWrapper
//             {
//                 Legs = new List<FlightCacheLeg>
//                 {
//                     CreateLeg(today, 1000, 500, (1, 2), (3, 5)),
//                     CreateLeg(today.AddDays(5), 2000, 500, (1, 4), (5, 7))
//                 }
//             };
//             var expected = new NegativeQualityScoreService.NqsCalculationContext
//             {
//                 FlightChangeDurationPerTrip = 60.0,
//                 FlightDurationPerTrip = 5 * 60.0,
//                 Leg1DepartureHour = 1.0,
//                 Leg2DepartureHour = 1.0,
//                 PricePerLeg = 2000,
//                 RoundTripDays = 5,
//                 StopsSumPerTrip = 1.0
//             };
//
//             // act
//             var context = _sut.GetNqsCalculationContext(flight);
//
//             // assert
//             context.Should().NotBeNull();
//             context.Should().BeEquivalentTo(expected);
//         }
//
//         [Fact(Skip =
//             "To test connection and permissions from developer's local machine with 'gcloud auth application-default login'; task FCACHE-383")]
//         public async Task Test_NQS_ML_Model_Connection_Locally()
//         {
//             var request = new PredictRequest()
//             {
//                 Endpoint =
//                     "projects/esky-analytics-ml-test/locations/europe-west1/endpoints/7517334610825445376",
//                 Instances =
//                 {
//                     Value.ForList(
//                         Value.ForNumber(1080.0),
//                         Value.ForNumber(295.0),
//                         Value.ForNumber(12.083),
//                         Value.ForNull(),
//                         Value.ForNumber(179.2),
//                         Value.ForNumber(0.0),
//                         Value.ForNumber(1.0))
//                 }
//             };
//
//             var serviceClientBuilder =
//                 new PredictionServiceClientBuilder { Endpoint = "https://europe-west1-aiplatform.googleapis.com" };
//             var predictionServiceClient = await serviceClientBuilder.BuildAsync();
//             var result = await predictionServiceClient.PredictAsync(request);
//
//             result.Predictions.Should().NotBeNull();
//             result.Predictions.First().NumberValue.Should().Be(8.467213780046333);
//         }
//
//
//         private FlightCacheLeg CreateLeg(DateTime date, decimal basePrice, decimal taxPrice,
//             params (double departureHours, double arrivalHours)[] segments)
//         {
//             return new FlightCacheLeg
//             {
//                 Segments = segments.Select(segment => new FlightCacheSegment
//                 {
//                     DepartureDate = date.AddHours(segment.departureHours),
//                     ArrivalDate = date.AddHours(segment.arrivalHours)
//                 }).ToList(),
//                 CurrencyCode = _currencyCode,
//                 AdultPrices = new List<PriceCacheEntry> { new(basePrice, taxPrice) }
//             };
//         }
//     }
// }