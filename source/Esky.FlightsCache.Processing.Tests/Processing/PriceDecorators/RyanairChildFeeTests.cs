using Esky.FlightsCache.CurrencyProvider;
using Esky.FlightsCache.MessageContract;
using Esky.FlightsCache.Processing.PriceDecorators.Ryanair;
using NSubstitute;
using System.Collections.Generic;

namespace Esky.FlightsCache.Processing.Tests.Processing.PriceDecorators;

public class RyanairChildFeeTests
{
    private readonly ICurrencyRatioProvider _currencyRatioProvider;
    private readonly RyanairChildFee _sut;
    private readonly Dictionary<string, decimal> _prices;
    private readonly IRyanairFeeCache _feeCache;

    public RyanairChildFeeTests()
    {
        _prices = new Dictionary<string, decimal> { ["EUR"] = 8, ["PLN"] = 36 };
        _currencyRatioProvider = Substitute.For<ICurrencyRatioProvider>();
        _feeCache = Substitute.For<IRyanairFeeCache>();
        _sut = new RyanairChildFee(new RyanairFeeSettings { Child = _prices }, _currencyRatioProvider, _feeCache);
    }

    [Fact]
    public void ChildPriceAlreadyExist_DontApplyCustomFee()
    {
        var flight = new FlightCache
        {
            Legs =
            [
                new() { CurrencyCode = "GBP", ChildPrices = [new(100, 23)] }
            ]
        };

        _sut.Apply(flight);

        flight.Legs[0].ChildPrices.Should().HaveCount(1);
        flight.Legs[0].ChildPrices[0].BasePrice.Should().Be(100);
        flight.Legs[0].ChildPrices[0].TaxPrice.Should().Be(23);
    }

    [Fact]
    public void FeeCache_ShouldNoLongerCalculateChildPrices()
    {
        _feeCache
            .Get("PLN", new Route { Departure = "WAW", Arrival = "STN" })
            .Returns(new Fee(MandatorySeatFee: 36, ChildDiscount: 0, InfantPrice: 120));
        var flight = new FlightCache
        {
            Legs =
            [
                new() { DepartureCode = "WAW", ArrivalCode = "STN", CurrencyCode = "PLN", AdultPrices = [new(100, 23)] }
            ]
        };

        _sut.Apply(flight);

        flight.Legs[0].ChildPrices.Should().BeNullOrEmpty();
    }

    [Fact]
    public void StoreRawPriceComponents()
    {
        _feeCache
            .Get("PLN", new Route { Departure = "WAW", Arrival = "STN" })
            .Returns(new Fee(MandatorySeatFee: 36, ChildDiscount: 10, InfantPrice: 120));
        var flight = new FlightCache
        {
            Legs =
            [
                new() { DepartureCode = "WAW", ArrivalCode = "STN", CurrencyCode = "PLN", AdultPrices = [new(100, 0)] }
            ]
        };

        _sut.Apply(flight);

        flight.Legs[0].RawPriceComponents
            .Should().ContainKey("_guardianSeatFee").WhoseValue.BasePrice.Should().Be(36);
        flight.Legs[0].RawPriceComponents
            .Should().ContainKey("_childDiscount").WhoseValue.BasePrice.Should().Be(10);
    }
}