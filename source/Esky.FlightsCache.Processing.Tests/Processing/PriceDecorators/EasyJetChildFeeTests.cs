using Esky.FlightsCache.MessageContract;
using Esky.FlightsCache.Processing.PriceDecorators.EasyJet;

namespace Esky.FlightsCache.Processing.Tests.Processing.PriceDecorators;

public class EasyJetChildFeeTests
{
    private readonly EasyJetChildFee _sut;
    private FlightCache _flight = new()
    {
        Legs =
        [
            new FlightCacheLeg {
                CurrencyCode = "GBP",
                AdultPrices =
                [
                    new PriceCacheEntry(100, 23, minimumNumberOPaxes:1),
                    new PriceCacheEntry(200, 23, minimumNumberOPaxes:3),
                ],
                DepartureAirportDetails = new()
                {
                    CityCode = "LON",
                    CountryCode = "GB"
                }
            }
        ]
    };

    public EasyJetChildFeeTests()
    {
        _sut = new EasyJetChildFee();
    }

    [Fact]
    public void WhenFlightDeaprtingFromUK_CopyAdultPricesWithoutTaxPriceAsChildPrices()
    {
        var leg = _flight.Legs[0];

        // Act
        _sut.Apply(_flight);

        // Assert
        leg.AdultPrices.Should().BeEquivalentTo(
            [
                new PriceCacheEntry(100, 23, minimumNumberOPaxes: 1),
                new PriceCacheEntry(200, 23, minimumNumberOPaxes: 3)
            ]);
        leg.ChildPrices.Should().BeEquivalentTo(
            [
                new PriceCacheEntry(100, 0, minimumNumberOPaxes: 1),
                new PriceCacheEntry(200, 0, minimumNumberOPaxes: 3)
            ]);
    }

    [Fact]
    public void WhenFlightNotDepartingFromUK_ChildPricesAreNotSet()
    {
        // Arrange
        var leg = _flight.Legs[0];
        leg.DepartureAirportDetails.CountryCode = "PL";

        // Act
        _sut.Apply(_flight);

        // Assert
        leg.AdultPrices.Should().BeEquivalentTo(
            [
                new PriceCacheEntry(100, 23, minimumNumberOPaxes: 1),
                new PriceCacheEntry(200, 23, minimumNumberOPaxes: 3)
            ]);
        leg.YouthPrices.Should().BeNullOrEmpty();
        leg.ChildPrices.Should().BeNullOrEmpty();
    }

    [Fact]
    public void WhenChildPricesAreAlreadySet_DoNotOverwrite()
    {
        // Arrange
        var leg = _flight.Legs[0];
        leg.ChildPrices =
        [
            new PriceCacheEntry(50, 0, minimumNumberOPaxes:1)
        ];

        // Act
        _sut.Apply(_flight);

        // Assert
        leg.AdultPrices.Should().BeEquivalentTo(
            [
                new PriceCacheEntry(100, 23, minimumNumberOPaxes: 1),
                new PriceCacheEntry(200, 23, minimumNumberOPaxes: 3)
            ]);
        leg.ChildPrices.Should().BeEquivalentTo(
            [
                new PriceCacheEntry(50, 0, minimumNumberOPaxes: 1)
            ]);
    }
}