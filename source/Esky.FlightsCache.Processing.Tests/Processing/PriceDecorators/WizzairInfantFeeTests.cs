using Esky.FlightsCache.CurrencyProvider;
using Esky.FlightsCache.MessageContract;
using Esky.FlightsCache.Processing.PriceDecorators.AirlineFees;
using Esky.FlightsCache.Processing.PriceDecorators.Wizzair;
using Microsoft.Extensions.Logging;
using NSubstitute;
using System.Collections.Generic;

namespace Esky.FlightsCache.Processing.Tests.Processing.PriceDecorators;

public class WizzairInfantFeeTests
{
    private readonly ICurrencyRatioProvider _currencyRatioProvider;
    private readonly WizzairInfantFee _sut;
    private readonly IAirlineFeeCache _feeCache;

    public WizzairInfantFeeTests()
    {
        _currencyRatioProvider = Substitute.For<ICurrencyRatioProvider>();
        _feeCache = Substitute.For<IAirlineFeeCache>();
        _sut = new WizzairInfantFee(_currencyRatioProvider, _feeCache);
    }

    [Theory]
    [InlineData(120, "W6", "PLN")]
    [InlineData(25, "W6", "EUR")]
    [InlineData(25, "W6", "GBP")]
    public void CustomFeeDefined_AppliesInfantFee(int expectedInfantPrice, string airline, string currency)
    {
        _feeCache.GetInfantFee(airline, currency, Arg.Any<int>()).Returns(new PriceCacheEntry(expectedInfantPrice, 0));
        var flight = new FlightCache
        {
            Legs =
            [
                new() { AirlineCode = airline, CurrencyCode = currency, AdultPrices = [new(100, 23)] }
            ]
        };

        _sut.Apply(flight);

        flight.Legs[0].InfantPrices.Should().HaveCount(1);
        flight.Legs[0].InfantPrices[0].BasePrice.Should().Be(expectedInfantPrice);
        flight.Legs[0].InfantPrices[0].TaxPrice.Should().Be(0);
    }
     
    [Fact]
    public void NoCustomFeeDefined()
    {
        var flight = new FlightCache
        {
            Legs =
            [
                new() { AirlineCode = "W6", CurrencyCode = "PLN", AdultPrices = [new(100, 23)] }
            ]
        };

        _sut.Apply(flight);

        flight.Legs[0].InfantPrices.Should().BeNull();
    }

    [Fact]
    public void InfantPriceAlreadyExist_DontApplyCustomFee()
    {
        var flight = new FlightCache
        {
            Legs =
            [
                new() { AirlineCode = "W6", CurrencyCode = "GBP", InfantPrices = [new(100, 23)] }
            ]
        };

        _sut.Apply(flight);

        flight.Legs[0].InfantPrices.Should().HaveCount(1);
        flight.Legs[0].InfantPrices[0].BasePrice.Should().Be(100);
        flight.Legs[0].InfantPrices[0].TaxPrice.Should().Be(23);
    }
    
    [Fact]
    public void FeeCache_AppliesInfantFee()
    {
        _feeCache.GetInfantFee("W6","PLN", Arg.Any<int>()).Returns(new PriceCacheEntry(123, 0));
        var flight = new FlightCache
        {
            Legs =
            [
                new() { AirlineCode = "W6", DepartureCode = "WAW", ArrivalCode = "STN", CurrencyCode = "PLN", AdultPrices = [new(100, 23)] }
            ]
        };
        
        _sut.Apply(flight);

        flight.Legs[0].InfantPrices.Should().HaveCount(1);
        flight.Legs[0].InfantPrices.Should().BeEquivalentTo(new List<PriceCacheEntry> { new(123, 0, minimumNumberOPaxes: 1) });
    }

    [Fact]
    public void ForGivenCurrency_WhenNoPriceForProvider_AppliesCheapestInfantFee()
    {
        _feeCache.GetCheapestInfantFee("W6","PLN").Returns(new PriceCacheEntry(123, 1));
        var flight = new FlightCache
        {
            Legs =
            [
                new() { AirlineCode = "W6", DepartureCode = "WAW", ArrivalCode = "STN", CurrencyCode = "PLN", AdultPrices = [new(100, 23)] }
            ]
        };
        
        _sut.Apply(flight);

        flight.Legs[0].InfantPrices.Should().HaveCount(1);
        flight.Legs[0].InfantPrices.Should().BeEquivalentTo(new List<PriceCacheEntry> { new(123, 1, minimumNumberOPaxes: 1) });
    }

    [Fact]
    public void ForGivenCurrency_WhenNoPrice_AppliesFallbackCurrencyForProvider()
    {
        _feeCache.GetInfantFee("W6","EUR", 58).Returns(new PriceCacheEntry(100, 2));
        _currencyRatioProvider.GetRatio("EUR", "PLN").Returns(4);
        var flight = new FlightCache
        {
            ProviderCode = 58,
            Legs =
            [
                new() { AirlineCode = "W6", DepartureCode = "WAW", ArrivalCode = "STN", CurrencyCode = "PLN", AdultPrices = [new(100, 23)] }
            ]
        };
        
        _sut.Apply(flight);

        flight.Legs[0].InfantPrices.Should().HaveCount(1);
        flight.Legs[0].InfantPrices.Should().BeEquivalentTo(new List<PriceCacheEntry> { new(400, 8, minimumNumberOPaxes: 1) });
    }
    
    [Fact]
    public void ForGivenCurrency_WhenNoPriceAndFallbackForProvider_AppliesFallbackCurrencyCheapest()
    {
        _feeCache.GetInfantFee("W6","EUR", 58).Returns((PriceCacheEntry) null);
        _feeCache.GetCheapestInfantFee("W6","EUR").Returns(new PriceCacheEntry(100, 2));
        _currencyRatioProvider.GetRatio("EUR", "PLN").Returns(4);
        var flight = new FlightCache
        {
            ProviderCode = 58,
            Legs =
            [
                new() { AirlineCode = "W6", DepartureCode = "WAW", ArrivalCode = "STN", CurrencyCode = "PLN", AdultPrices = [new(100, 23)] }
            ]
        };
        
        _sut.Apply(flight);

        flight.Legs[0].InfantPrices.Should().HaveCount(1);
        flight.Legs[0].InfantPrices.Should().BeEquivalentTo(new List<PriceCacheEntry> { new(400, 8, minimumNumberOPaxes: 1) });
    }

    [Fact]
    public void WizzairFeeCache_GetCheapestInfantFee()
    {
        var cache = new AirlineFeeCache(Substitute.For<ILogger<AirlineFeeCache>>());
        cache.ReplaceAll(
            [
                new("W6", "PLN")
                {
                    ProviderFees = new Dictionary<int, AirlineFeeModel.ProviderFee>
                    {
                        [58] = new() { InfantBasePrice = 60, InfantTaxPrice = 1 },
                        [75] = new() { InfantBasePrice = 30, InfantTaxPrice = 2 },
                        [80] = new() { InfantBasePrice = 32, InfantTaxPrice = 3 },
                    }
                },
                new("W6","EUR")
                {
                    ProviderFees = new Dictionary<int, AirlineFeeModel.ProviderFee>
                    {
                        [58] = new() { InfantBasePrice = 60, InfantTaxPrice = 1 },
                    }
                }
            ]
        );

        var price = cache.GetCheapestInfantFee("W6","PLN");

        price.Should().BeEquivalentTo(new PriceCacheEntry(30, 2));
    }
}