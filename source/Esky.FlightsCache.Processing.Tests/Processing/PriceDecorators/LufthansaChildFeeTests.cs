using Esky.FlightsCache.MessageContract;
using Esky.FlightsCache.Processing.PriceDecorators.Lufthansa;

namespace Esky.FlightsCache.Processing.Tests.Processing.PriceDecorators;

public class LufthansaChildFeeTests
{
    [Fact]
    public void WhenNoAdultPrice_DontApply()
    {
        var flight = new FlightCache
        {
            Legs =
            [
                new() { CurrencyCode = "GBP", AdultPrices = null }
            ]
        };

        var sut = new LufthansaChildFee();

        sut.Apply(flight);

        flight.Legs[0].ChildPrices.Should().HaveCount(0);
    }

    [Fact]
    public void OnRegularFare_ChildIs75PercentOfAdult()
    {
        var flight = new FlightCache
        {
            Legs =
            [
                new() { CurrencyCode = "GBP", AdultPrices = [new(100, 23)] }
            ]
        };

        var sut = new LufthansaChildFee();

        sut.Apply(flight);

        flight.Legs[0].ChildPrices.Should().HaveCount(1);
        flight.Legs[0].ChildPrices[0].BasePrice.Should().Be(75);
        flight.Legs[0].ChildPrices[0].TaxPrice.Should().Be(23);
    }

    [Fact]
    public void ChildPriceAlreadyExist_DontApplyCustomFee()
    {
        var flight = new FlightCache
        {
            Legs =
            [
                new() { CurrencyCode = "GBP", ChildPrices = [new(100, 23)] }
            ]
        };

        var sut = new LufthansaChildFee();

        sut.Apply(flight);

        flight.Legs[0].ChildPrices.Should().HaveCount(1);
        flight.Legs[0].ChildPrices[0].BasePrice.Should().Be(100);
        flight.Legs[0].ChildPrices[0].TaxPrice.Should().Be(23);
    }
}