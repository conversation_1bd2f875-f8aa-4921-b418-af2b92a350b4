using Esky.FlightsCache.CurrencyProvider;
using Esky.FlightsCache.MessageContract;
using Esky.FlightsCache.Processing.PriceDecorators.EasyJet;
using NSubstitute;

namespace Esky.FlightsCache.Processing.Tests.Processing.PriceDecorators;

public class EasyJetExtraFee3PaxTests
{
    private readonly ICurrencyRatioProvider _currencyRatioProvider;
    private readonly EasyJetExtraFee3Pax _easyJetExtraFee3Pax;

    public EasyJetExtraFee3PaxTests()
    {
        _currencyRatioProvider = Substitute.For<ICurrencyRatioProvider>();
        _easyJetExtraFee3Pax = new EasyJetExtraFee3Pax(_currencyRatioProvider);
    }

    [Fact]
    public void Apply_ShouldAddExtraFee_WhenConditionsAreMet()
    {
        var flight = new FlightCache
        {
            Legs =
            [
                new FlightCacheLeg() { CurrencyCode = "USD", AdultPrices = [new PriceCacheEntry(100, 23)] }
            ]
        };
        _currencyRatioProvider.GetRatio(Arg.Any<string>(), Arg.Any<string>()).Returns(1.2m);

        _easyJetExtraFee3Pax.Apply(flight);

        var adultPrice = flight.Legs[0].AdultPrices.Should().ContainSingle(x => x.MinimumNumberOfPaxes == 3).Which;
        adultPrice.BasePrice.Should().Be(102.4m);
        adultPrice.TaxPrice.Should().Be(23);
    }

    [Fact]
    public void Apply_ShouldAddExtraFee_WhenTheSameCurrency()
    {
        var flight = new FlightCache
        {
            Legs =
            [
                new FlightCacheLeg() { CurrencyCode = "EUR", AdultPrices = [new PriceCacheEntry(100, 23)] }
            ]
        };
        _currencyRatioProvider.GetRatio(Arg.Any<string>(), Arg.Any<string>()).Returns(1.0m);

        _easyJetExtraFee3Pax.Apply(flight);

        var adultPrice = flight.Legs[0].AdultPrices.Should().ContainSingle(x => x.MinimumNumberOfPaxes == 3).Which;
        adultPrice.BasePrice.Should().Be(102);
        adultPrice.TaxPrice.Should().Be(23);
    }

    [Fact]
    public void Apply_ShouldAddExtraFeeToChildPrices_WhenConditionsAreMet()
    {
        var flight = new FlightCache
        {
            Legs =
            [
                new FlightCacheLeg() { CurrencyCode = "USD", ChildPrices = [new PriceCacheEntry(50, 10)] }
            ]
        };
        _currencyRatioProvider.GetRatio(Arg.Any<string>(), Arg.Any<string>()).Returns(1.2m);

        _easyJetExtraFee3Pax.Apply(flight);

        var childPrice = flight.Legs[0].ChildPrices.Should().ContainSingle(x => x.MinimumNumberOfPaxes == 3).Which;
        childPrice.BasePrice.Should().Be(52.4m);
        childPrice.TaxPrice.Should().Be(10);
    }

    [Fact]
    public void Apply_ShouldNotChange_WhenChildPricesAreNull()
    {
        var flight = new FlightCache
        {
            Legs =
            [
                new FlightCacheLeg() { CurrencyCode = "USD", ChildPrices = null }
            ]
        };
        _currencyRatioProvider.GetRatio(Arg.Any<string>(), Arg.Any<string>()).Returns(1.2m);

        _easyJetExtraFee3Pax.Apply(flight);

        flight.Legs[0].ChildPrices.Should().BeNull();
    }

    [Fact]
    public void Apply_ShouldNotChange_WhenMinimumNumberOfPaxesIsGreaterOrEqualToThree()
    {
        var flight = new FlightCache
        {
            Legs =
            [
                new FlightCacheLeg { CurrencyCode = "USD", AdultPrices = [new PriceCacheEntry(100, 23, 3)] }
            ]
        };
        _currencyRatioProvider.GetRatio(Arg.Any<string>(), Arg.Any<string>()).Returns(1.2m);

        _easyJetExtraFee3Pax.Apply(flight);

        var adultPrice = flight.Legs[0].AdultPrices.Should().ContainSingle(x => x.MinimumNumberOfPaxes == 3).Which;
        adultPrice.BasePrice.Should().Be(100);
        adultPrice.TaxPrice.Should().Be(23);
    }

    [Fact]
    public void Apply_ShouldAddExtraFeeToYouthPrices_WhenConditionsAreMet()
    {
        var flight = new FlightCache
        {
            Legs =
            [
                new FlightCacheLeg() { CurrencyCode = "USD", YouthPrices = [new PriceCacheEntry(70, 15)] }
            ]
        };
        _currencyRatioProvider.GetRatio(Arg.Any<string>(), Arg.Any<string>()).Returns(1.2m);

        _easyJetExtraFee3Pax.Apply(flight);

        var youthPrice = flight.Legs[0].YouthPrices.Should().ContainSingle(x => x.MinimumNumberOfPaxes == 3).Which;
        youthPrice.BasePrice.Should().Be(72.4m);
        youthPrice.TaxPrice.Should().Be(15);
    }

    [Fact]
    public void Apply_ShouldNotChange_WhenYouthPricesAreNull()
    {
        var flight = new FlightCache
        {
            Legs =
            [
                new FlightCacheLeg() { CurrencyCode = "USD", YouthPrices = null }
            ]
        };
        _currencyRatioProvider.GetRatio(Arg.Any<string>(), Arg.Any<string>()).Returns(1.2m);

        _easyJetExtraFee3Pax.Apply(flight);

        flight.Legs[0].YouthPrices.Should().BeNull();
    }
}