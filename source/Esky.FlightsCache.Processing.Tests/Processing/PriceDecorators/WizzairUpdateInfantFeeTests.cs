using Esky.FlightsCache.MessageContract;
using Esky.FlightsCache.Processing.PriceDecorators.AirlineFees;
using Esky.FlightsCache.Processing.PriceDecorators.Wizzair;
using NSubstitute;

namespace Esky.FlightsCache.Processing.Tests.Processing.PriceDecorators;

public class WizzairUpdateInfantFeeTests
{
    private readonly IAirlineFeeCacheUpdater _feeUpdater;
    private readonly WizzairUpdateInfantFee _fee;

    public WizzairUpdateInfantFeeTests()
    {
        _feeUpdater = Substitute.For<IAirlineFeeCacheUpdater>();
        _fee = new WizzairUpdateInfantFee(_feeUpdater);
    }
    
    [Fact]
    public void NoActionWhenNoInfantPricesOnePax()
    {
        _fee.Apply(
            new FlightCache
            {
                ProviderCode = 58,
                Legs = [new FlightCacheLeg { CurrencyCode = "PLN", InfantPrices = [new PriceCacheEntry(0, 24, minimumNumberOPaxes: 2)] }]
            }
        );

        _feeUpdater.DidNotReceiveWithAnyArgs().UpdateInfantFee(default, default, default, default, default);
    }
        
    [Fact]
    public void NoActionWhenNullInfantPrices()
    {
        _fee.Apply(
            new FlightCache
            {
                ProviderCode = 58,
                Legs = [new FlightCacheLeg { CurrencyCode = "PLN", AdultPrices = [new PriceCacheEntry(200, 24)] }]
            }
        );

        _feeUpdater.DidNotReceiveWithAnyArgs().UpdateInfantFee(default, default, default, default, default);
    }
    
    [Fact]
    public void NoActionWhenNoInfantPrices()
    {
        _fee.Apply(
            new FlightCache
            {
                ProviderCode = 58,
                Legs = [new FlightCacheLeg { CurrencyCode = "PLN", InfantPrices = [] }]
            }
        );

        _feeUpdater.DidNotReceiveWithAnyArgs().UpdateInfantFee(default, default, default, default, default);
    }
        
    [Fact]
    public void FromOneWay()
    {
        _fee.Apply(
            new FlightCache
            {
                ProviderCode = 58,
                Legs = [new FlightCacheLeg { AirlineCode = "W6", CurrencyCode = "PLN", InfantPrices = [new PriceCacheEntry(200, 24)] }]
            }
        );

        _feeUpdater.Received(1).UpdateInfantFee("W6", "PLN", 58, 200, 24);
    }
    
    [Fact]
    public void FromRoundTrip()
    {
        _fee.Apply(
            new FlightCache
            {
                ProviderCode = 58,
                Legs =
                [
                    new FlightCacheLeg { AirlineCode = "W6", CurrencyCode = "PLN", InfantPrices = [new PriceCacheEntry(200, 24)] },
                    new FlightCacheLeg { AirlineCode = "W6", CurrencyCode = "PLN", InfantPrices = [new PriceCacheEntry(0, 0)] }
                ]
            }
        );

        _feeUpdater.Received(1).UpdateInfantFee("W6", "PLN", 58, 100, 12);
    }
}