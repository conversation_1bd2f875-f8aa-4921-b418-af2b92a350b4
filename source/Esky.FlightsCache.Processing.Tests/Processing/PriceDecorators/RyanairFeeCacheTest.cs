using Esky.FlightsCache.Processing.PriceDecorators.Ryanair;
using System;

namespace Esky.FlightsCache.Processing.Tests.Processing.PriceDecorators;

public class RyanairFeeCacheTest
{
    [Fact]
    public void WhenNoRoute_ReturnsNull()
    {
        var cache = new RyanairFeeCache();
        cache.ReplaceAll(ArraySegment<RyanairFee>.Empty);

        var fee = cache.Get("PLN", new Route { Departure = "WMI", Arrival = "STN" });

        fee.Should().BeNull();
    }

    [Fact]
    public void WhenHasRoute_ReturnsFee()
    {
        var cache = new RyanairFeeCache();
        cache.ReplaceAll(
        [
            new("PLN", new Route { Departure = "WMI", Arrival = "STN" }, new Fee(MandatorySeatFee: 36, ChildDiscount:10, InfantPrice: 120))
        ]);

        var fee = cache.Get("PLN", new Route { Departure = "WMI", Arrival = "STN" });

        fee.Should().NotBeNull();
        fee.Value.MandatorySeatFee.Should().Be(36);
        fee.Value.ChildDiscount.Should().Be(10);
        fee.Value.InfantPrice.Should().Be(120);
    }
}