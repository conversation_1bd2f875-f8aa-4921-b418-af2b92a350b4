using Esky.FlightsCache.CurrencyProvider;
using Esky.FlightsCache.MessageContract;
using Esky.FlightsCache.Processing.PriceDecorators.AirlineFees;
using Esky.FlightsCache.Processing.PriceDecorators.EasyJet;
using NSubstitute;

namespace Esky.FlightsCache.Processing.Tests.Processing.PriceDecorators;

public class EasyJetInfantFeeTests
{
    private readonly EasyJetInfantFee _sut;
    private readonly IAirlineFeeCache _feeCache;

    public EasyJetInfantFeeTests()
    {
        var currencyRatioProvider = Substitute.For<ICurrencyRatioProvider>();
        _feeCache = Substitute.For<IAirlineFeeCache>();
        _sut = new EasyJetInfantFee(currencyRatioProvider, _feeCache);
    }

    [Fact]
    public void WhenInfantFee_Ignore()
    {
        var flight = new FlightCache
        {
            Legs = [new FlightCacheLeg { CurrencyCode = "EUR", AdultPrices = [new PriceCacheEntry(100, 23)], InfantPrices = [new PriceCacheEntry(30, 0)] }]
        };

        _sut.Apply(flight);

        var infant = flight.Legs[0].InfantPrices.Should().ContainSingle().Which;
        infant.BasePrice.Should().Be(30);
        infant.TaxPrice.Should().Be(0);
    }

    [Theory]
    [InlineData(25, "GBP")]
    [InlineData(50, "PLN")]
    [InlineData(12.5, "EUR")]
    public void WhenNoInfantFee_Apply(decimal expectedInfantPrice, string currency)
    {
        _feeCache.GetInfantFee("U2", currency, Arg.Any<int>()).Returns(new PriceCacheEntry(expectedInfantPrice, 0));
        var flight = new FlightCache
        {
            Legs = [new FlightCacheLeg { AirlineCode = "U2", CurrencyCode = currency, AdultPrices = [new PriceCacheEntry(100, 23)] }]
        };

        _sut.Apply(flight);

        var infant = flight.Legs[0].InfantPrices.Should().ContainSingle().Which;
        infant.BasePrice.Should().Be(expectedInfantPrice);
        infant.TaxPrice.Should().Be(0);
    }
}