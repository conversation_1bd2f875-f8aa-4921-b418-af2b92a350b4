using Esky.FlightsCache.MessageContract;
using Esky.FlightsCache.ObjectDirectory;
using Esky.FlightsCache.Processing.PriceDecorators.Lufthansa;
using NSubstitute;

namespace Esky.FlightsCache.Processing.Tests.Processing.PriceDecorators;

public class LufthansaInfantFeeTests
{
    [Fact]
    public void RegularFare_WhenNoAdultPrice_DontApply()
    {
        var flight = new FlightCache
        {
            Legs =
            [
                new()
                {
                    CurrencyCode = "GBP",
                    AdultPrices = null
                }
            ]
        };

        var sut = new LufthansaInfantFee(Substitute.For<IObjectDirectoryService>());

        sut.Apply(flight);

        flight.Legs[0].InfantPrices.Should().HaveCount(0);
    }
    
    [Fact]
    public void GermanDomesticFlight_NoInfantFee()
    {
        var flight = new FlightCache
        {
            Legs =
            [
                new()
                {
                    CurrencyCode = "PLN",
                    AdultPrices = [new(50, 2)],
                    DepartureCode = "BER",
                    ArrivalCode = "FRA"
                }
            ]
        };
        var ob = Substitute.For<IObjectDirectoryService>();
        ob.IsAirport(Arg.Any<string>()).Returns(true);
        ob.GetCountryCodeByDestination("BER").Returns("DE");
        ob.GetCountryCodeByDestination("FRA").Returns("DE");
        var sut = new LufthansaInfantFee(ob);
        
        sut.Apply(flight);

        flight.Legs[0].InfantPrices.Should().HaveCount(1);
        flight.Legs[0].InfantPrices[0].BasePrice.Should().Be(0);
        flight.Legs[0].InfantPrices[0].TaxPrice.Should().Be(0);
    }  
    
    [Fact]
    public void OnRegularFare_InfantIs10PercentOfAdultWithNoTax()
    {
        var flight = new FlightCache
        {
            Legs =
            [
                new()
                {
                    CurrencyCode = "PLN",
                    AdultPrices = [new(50, 2)],
                    DepartureCode = "BER",
                    ArrivalCode = "WAW"
                }
            ]
        };
        var ob = Substitute.For<IObjectDirectoryService>();
        ob.IsAirport(Arg.Any<string>()).Returns(true);
        ob.GetCountryCodeByDestination("BER").Returns("DE");
        ob.GetCountryCodeByDestination("WAW").Returns("PL");
        var sut = new LufthansaInfantFee(ob);
        
        sut.Apply(flight);

        flight.Legs[0].InfantPrices.Should().HaveCount(1);
        flight.Legs[0].InfantPrices[0].BasePrice.Should().Be(5);
        flight.Legs[0].InfantPrices[0].TaxPrice.Should().Be(0);
    }
    
    [Fact]
    public void InfantPriceAlreadyExist_DontApplyCustomFee()
    {
        var flight = new FlightCache
        {
            Legs =
            [
                new() { CurrencyCode = "GBP", InfantPrices = [new(100, 23)] }
            ]
        };
        
        var sut = new LufthansaInfantFee(Substitute.For<IObjectDirectoryService>());
        
        sut.Apply(flight);

        flight.Legs[0].InfantPrices.Should().HaveCount(1);
        flight.Legs[0].InfantPrices[0].BasePrice.Should().Be(100);
        flight.Legs[0].InfantPrices[0].TaxPrice.Should().Be(23);
    }
}