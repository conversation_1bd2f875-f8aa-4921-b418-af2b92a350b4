using Esky.FlightsCache.CurrencyProvider;
using Esky.FlightsCache.MessageContract;
using Esky.FlightsCache.Processing.PriceDecorators.Ryanair;
using NSubstitute;
using System.Collections.Generic;

namespace Esky.FlightsCache.Processing.Tests.Processing.PriceDecorators;

public class RyanairInfantFeeTests
{
    private readonly Dictionary<string, decimal> _prices;
    private readonly ICurrencyRatioProvider _currencyRatioProvider;
    private readonly RyanairInfantFee _sut;
    private readonly IRyanairFeeCache _feeCache;

    public RyanairInfantFeeTests()
    {
        _prices = new Dictionary<string, decimal>
        {
            { "PLN", 120 },
            { "EUR", 25 },
            { "GBP", 25 },
            { "CHF", 30 },
            { "NOK", 250 },
            { "SEK", 275 },
            { "CZK", 750 },
            { "HUF", 8750 },
            { "DKK", 200 },
            { "MAD", 300 }
        };
        _currencyRatioProvider = Substitute.For<ICurrencyRatioProvider>();
        _feeCache = Substitute.For<IRyanairFeeCache>();
        _sut = new RyanairInfantFee(new RyanairFeeSettings { Infant = _prices }, _currencyRatioProvider, _feeCache);
    }

    [Theory]
    [InlineData(120, "PLN")]
    [InlineData(25, "EUR")]
    [InlineData(25, "GBP")]
    public void CustomFeeDefined_AppliesInfantFee(int expectedInfantPrice, string currency)
    {
        var flight = new FlightCache
        {
            Legs =
            [
                new() { CurrencyCode = currency, AdultPrices = [new(100, 23)] }
            ]
        };

        _sut.Apply(flight);

        flight.Legs[0].InfantPrices.Should().HaveCount(1);
        flight.Legs[0].InfantPrices[0].BasePrice.Should().Be(expectedInfantPrice);
        flight.Legs[0].InfantPrices[0].TaxPrice.Should().Be(0);
    }

    [Theory]
    [InlineData(2.239, "YYY")]
    [InlineData(0.281, "XXX")]
    public void NoCustomFeeDefined_AppliesExchangedPriceFromEUR(decimal ratio, string currency)
    {
        const string eur = "EUR";
        var flight = new FlightCache
        {
            Legs =
            [
                new() { CurrencyCode = currency, AdultPrices = [new(100, 23)] }
            ]
        };
        _currencyRatioProvider.GetRatio(eur, currency).Returns(ratio);

        _sut.Apply(flight);

        flight.Legs[0].InfantPrices.Should().HaveCount(1);
        flight.Legs[0].InfantPrices[0].BasePrice.Should().Be(_prices[eur] * ratio);
        flight.Legs[0].InfantPrices[0].TaxPrice.Should().Be(0);
    }

    [Fact]
    public void InfantPriceAlreadyExist_DontApplyCustomFee()
    {
        var flight = new FlightCache
        {
            Legs =
            [
                new() { CurrencyCode = "GBP", InfantPrices = [new(100, 23)] }
            ]
        };

        _sut.Apply(flight);

        flight.Legs[0].InfantPrices.Should().HaveCount(1);
        flight.Legs[0].InfantPrices[0].BasePrice.Should().Be(100);
        flight.Legs[0].InfantPrices[0].TaxPrice.Should().Be(23);
    }

    [Fact]
    public void FeeCache_AppliesInfantFee()
    {
        _feeCache
            .Get("PLN", new Route { Departure = "WAW", Arrival = "STN" })
            .Returns(new Fee(MandatorySeatFee: 36, ChildDiscount: 0, InfantPrice: 120));
        var flight = new FlightCache
        {
            Legs =
            [
                new() { DepartureCode = "WAW", ArrivalCode = "STN", CurrencyCode = "PLN", AdultPrices = [new(100, 23)] }
            ]
        };

        _sut.Apply(flight);

        flight.Legs[0].InfantPrices.Should().HaveCount(1);
        flight.Legs[0].InfantPrices.Should().BeEquivalentTo(new List<PriceCacheEntry> { new(120, 0, minimumNumberOPaxes: 1) });
    }
}