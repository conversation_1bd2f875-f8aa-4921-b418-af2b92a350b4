using Esky.FlightsCache.CurrencyProvider;
using Esky.FlightsCache.MessageContract;
using Esky.FlightsCache.Processing.Multipliers;
using NSubstitute;
using System;
using System.Threading.Tasks;

namespace Esky.FlightsCache.Processing.Tests.Multipliers
{
    public class SSCEasyJetCacheRequestMultiplierTests
    {
        [Fact]
        public async Task WhenTravelFusion_ThenAddMarkupInSourceCurrency_WhenAtlas_ThenAddNoMarkup()
        {
            var cacheRequest = new CacheRequest
            {
                SourceDescription = new SourceDescription { Provider = "6", SearchDepartureDate = DateTime.Today.AddDays(50) },
                CommandOptions = new CommandDataOptions { DeleteOptions = new DeleteOptions { ProviderCodes = [6] } },
                Flights =
                [
                    new FlightCache
                    {
                        ProviderCode = 6,
                        Supplier = "supplier",
                        Legs =
                        [
                            new FlightCacheLeg
                            {
                                CurrencyCode = "GBP",
                                ConversionRatioToReferenceCurrency = 1.5M,
                                AdultPrices = [new PriceCacheEntry { BasePrice = 100, TaxPrice = 10 }]
                            }
                        ]
                    }
                ]
            };

            var currencyRatioProvider = Substitute.For<ICurrencyRatioProvider>();
            currencyRatioProvider.GetRatio("GBP", "EUR").ReturnsForAnyArgs(1.2M);
            currencyRatioProvider.GetRatio("EUR", "GBP").ReturnsForAnyArgs(1 / 1.2M);
            var multiplier = new SSCEasyJetCacheRequestMultiplier(currencyRatioProvider);

            var array = await multiplier.Map(cacheRequest);

            currencyRatioProvider.ReceivedCalls().Should().BeEmpty();

            array.Should().HaveCount(2);
            array[0].SourceDescription.Provider.Should().Be("77");
            array[0].SourceDescription.Supplier.Should().Be("easyjet");
            array[0].CommandOptions.DeleteOptions.ProviderCodes.Should().Contain(77);
            array[0].Flights.Should().Satisfy(x => x.ProviderCode == 77 && x.Supplier == "easyjet");
            array[0].Flights[0].Legs.Should().Satisfy(x => x.CurrencyCode == "GBP" && x.ConversionRatioToReferenceCurrency == 1.5M);
            array[0].Flights[0].Legs[0].AdultPrices.Should()
                .SatisfyRespectively(x =>
                {
                    x.BasePrice.Should().Be(100);
                    x.TaxPrice.Should().Be(10);
                });

            array[1].SourceDescription.Provider.Should().Be("58");
            array[1].SourceDescription.Supplier.Should().Be("easyjet");
            array[1].CommandOptions.DeleteOptions.ProviderCodes.Should().Contain(58);
            array[1].Flights.Should().Satisfy(x => x.ProviderCode == 58 && x.Supplier == "easyjet");
            array[1].Flights[0].Legs.Should().Satisfy(x => x.CurrencyCode == "GBP" && x.ConversionRatioToReferenceCurrency == 1.5M);
            array[1].Flights[0].Legs[0].AdultPrices.Should()
                .SatisfyRespectively(x =>
                {
                    x.BasePrice.Should().Be(112);
                    x.TaxPrice.Should().Be(10);
                });
        }

        [Fact]
        public async Task WhenDepartureDateIsLaterThan250Days_ThenReturnOnlyTravelFusion()
        {
            var cacheRequest = new CacheRequest
            {
                SourceDescription = new SourceDescription { Provider = "6" },
                CommandOptions = new CommandDataOptions { DeleteOptions = new DeleteOptions { ProviderCodes = [6] } },
                Flights =
                [
                    new FlightCache
                    {
                        ProviderCode = 6,
                        Supplier = "supplier",
                        Legs =
                        [
                            new FlightCacheLeg
                            {
                                CurrencyCode = "GBP",
                                ConversionRatioToReferenceCurrency = 1.5M,
                                AdultPrices = [new PriceCacheEntry { BasePrice = 100, TaxPrice = 10 }],
                                DepartureDate = DateTime.Today.AddDays(251)
                            }
                        ]
                    }
                ]
            };

            var currencyRatioProvider = Substitute.For<ICurrencyRatioProvider>();
            var multiplier = new SSCEasyJetCacheRequestMultiplier(currencyRatioProvider);

            var array = await multiplier.Map(cacheRequest);

            currencyRatioProvider.ReceivedCalls().Should().BeEmpty();

            array.Should().HaveCount(2);

            array[0].SourceDescription.Provider.Should().Be("77");
            array[0].SourceDescription.Supplier.Should().Be("easyjet");
            array[0].CommandOptions.DeleteOptions.ProviderCodes.Should().Contain(77);
            array[0].Flights.Should().HaveCount(0);

            array[1].SourceDescription.Provider.Should().Be("58");
            array[1].SourceDescription.Supplier.Should().Be("easyjet");
            array[1].CommandOptions.DeleteOptions.ProviderCodes.Should().Contain(58);
            array[1].Flights.Should().Satisfy(x => x.ProviderCode == 58 && x.Supplier == "easyjet");
            array[1].Flights[0].Legs.Should().Satisfy(x => x.CurrencyCode == "GBP" && x.ConversionRatioToReferenceCurrency == 1.5M);
            array[1].Flights[0].Legs[0].AdultPrices.Should()
                .SatisfyRespectively(x =>
                {
                    x.BasePrice.Should().Be(112);
                    x.TaxPrice.Should().Be(10);
                });
        }

        [Fact]
        public async Task WhenTravelFusionInNewCurrency_ThenAddMarkupInSourceCurrencyConvertedFrom12GBP()
        {
            var cacheRequest = new CacheRequest
            {
                SourceDescription = new SourceDescription { Provider = "6", SearchDepartureDate = DateTime.Today.AddDays(50) },
                CommandOptions = new CommandDataOptions { DeleteOptions = new DeleteOptions { ProviderCodes = [6] } },
                Flights =
                [
                    new FlightCache
                    {
                        ProviderCode = 6,
                        Supplier = "supplier",
                        Legs =
                        [
                            new FlightCacheLeg
                            {
                                CurrencyCode = "XYZ",
                                ConversionRatioToReferenceCurrency = 1.5M,
                                AdultPrices = [new PriceCacheEntry { BasePrice = 100, TaxPrice = 10 }]
                            }
                        ]
                    }
                ]
            };

            var currencyRatioProvider = Substitute.For<ICurrencyRatioProvider>();
            currencyRatioProvider.GetRatio("GBP", "XYZ").ReturnsForAnyArgs(10M);
            var multiplier = new SSCEasyJetCacheRequestMultiplier(currencyRatioProvider);

            var array = await multiplier.Map(cacheRequest);

            currencyRatioProvider.ReceivedCalls().Should().NotBeEmpty();

            array.Should().HaveCount(2);

            array[1].SourceDescription.Provider.Should().Be("58");
            array[1].SourceDescription.Supplier.Should().Be("easyjet");
            array[1].CommandOptions.DeleteOptions.ProviderCodes.Should().Contain(58);
            array[1].Flights.Should().Satisfy(x => x.ProviderCode == 58 && x.Supplier == "easyjet");
            array[1].Flights[0].Legs.Should().Satisfy(x => x.CurrencyCode == "XYZ");
            array[1].Flights[0].Legs[0].AdultPrices.Should()
                .SatisfyRespectively(x =>
                {
                    x.BasePrice.Should().Be(100 + 10 * 12);
                    x.TaxPrice.Should().Be(10);
                });
        }
    }
}