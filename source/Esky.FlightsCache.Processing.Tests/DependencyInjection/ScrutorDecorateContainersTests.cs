using Autofac;
using Autofac.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection;
using System;

namespace Esky.FlightsCache.Processing.Tests.DependencyInjection
{
    public class ScrutorDecorateContainersTests
    {
        private readonly ServiceCollection _services;

        public ScrutorDecorateContainersTests()
        {
            _services = new ServiceCollection();
            _services
                .AddScoped<DummyDependency>()
                .AddScoped<ITestService, Decorated>()
                .Decorate<ITestService, Decorator>();
        }

        [Fact]
        public void BuiltInDI_GivenScrutorDecorate_ReturnDecorated()
        {
            var sp = _services.BuildServiceProvider();

            var test = sp.GetService<ITestService>();

            Assert.IsType<Decorator>(test);
        }

        [Fact]
        public void Autofac_GivenScrutorDecorate_ReturnDecorated()
        {
            var builder = new ContainerBuilder();
            builder.Populate(_services);
            var container = builder.Build();
            var sp = container.Resolve<IServiceProvider>();

            var test = sp.GetService<ITestService>();

            Assert.IsType<Decorator>(test);
        }
    }

    file class DummyDependency
    {
    }

    file interface ITestService
    {
    }

    file class Decorated : ITestService
    {
        public Decorated(DummyDependency dummyDependency) { }
    }

    file class Decorator : ITestService
    {
        public Decorator(ITestService decorated, DummyDependency dummyDependency) { }
    }
}