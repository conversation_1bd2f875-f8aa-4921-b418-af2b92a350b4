using Esky.FlightsCache.Database.Helpers;

namespace Esky.FlightsCache.Processing.Tests.Database
{
    public class ModelExtensionsTests
    {
        [InlineData("flightsearch-api")]
        [InlineData("flightsearch-api-1pax")]
        [InlineData("flightsearch-api-additional")]
        [InlineData("SearchFlightsCommand_SOAP")]
        [Theory]
        public void IsSmartSource_WhenOrganicSearchFlightsSource_IsOrganic(string sourceName)
        {
            ModelExtensions.IsOrganic(sourceName).Should().BeTrue();
        }

        [InlineData("TravelFusionConsumer_Categorized_Every6Hours_aurigny,blueislands")]
        [InlineData("CacheDemand_1001_0801_3d61")]
        [InlineData("SSCProviderConsumer")]
        [Theory]
        public void IsSmartSource_WhenRobotOrCalendarSource_IsOrganic(string sourceName)
        {
            ModelExtensions.IsOrganic(sourceName).Should().BeFalse();
        }
    }
}