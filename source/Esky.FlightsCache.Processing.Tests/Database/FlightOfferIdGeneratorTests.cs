using Esky.FlightsCache.Database.Configuration;
using Esky.FlightsCache.Database.FlightOffers;
using Esky.FlightsCache.Database.FlightOffers.Model;
using NSubstitute;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Esky.FlightsCache.Processing.Tests.Database;

public class IdentifiableFlightOfferExtensionsTests
{
    private readonly ISupplierStorage _supplierStorage = Substitute.For<ISupplierStorage>();

    [Fact]
    public async Task CreateId_OneWayTrip_ShouldReturnExpectedString()
    {
        _supplierStorage.GetSupplierId("supplier1").Returns(15);

        var mockOffer = Substitute.For<IIdentifiableFlightOfferOneWay>();
        mockOffer.Departure.Returns("ABC");
        mockOffer.Arrival.Returns("XYZ");
        mockOffer.DepartureDate.Returns(new DateTime(2030, 1, 1));
        mockOffer.Provider.Returns(100);
        mockOffer.Supplier.Returns("supplier1");
        mockOffer.TripType.Returns(TripType.OneWay);
        mockOffer.OutboundFlightNumbers.Returns(new List<(string, string)> { ("UA", "123") });

        var result = await mockOffer.CreateId(_supplierStorage);
        result.Should().Be("ABCXYZ300101100.15||UA123");
    }

    [Fact]
    public async Task CreateId_RoundTrip_ShouldReturnExpectedString()
    {
        var mockOffer = Substitute.For<IIdentifiableFlightOfferRoundTrip>();
        mockOffer.Departure.Returns("XXX");
        mockOffer.Arrival.Returns("YYY");
        mockOffer.DepartureDate.Returns(new DateTime(2040, 4, 10));
        mockOffer.ReturnDepartureDate.Returns(new DateTime(2040, 4, 20));
        mockOffer.Provider.Returns(200);
        mockOffer.TripType.Returns(TripType.RoundTrip);
        mockOffer.OutboundFlightNumbers.Returns(new List<(string, string)> { ("DL", "200") });
        mockOffer.InboundFlightNumbers.Returns(new List<(string, string)> { ("DL", "201") });

        var result = await mockOffer.CreateId(_supplierStorage);
        result.Should().Be("XXXYYY400410400420200||DL200||DL201");
    }

    [Fact]
    public async Task CreateId_OutboundOnly_ShouldReturnExpectedString()
    {
        var mockOffer = Substitute.For<IIdentifiableFlightOfferOneWay>();
        mockOffer.Departure.Returns("DEF");
        mockOffer.Arrival.Returns("GHI");
        mockOffer.DepartureDate.Returns(new DateTime(2025, 5, 5));
        mockOffer.Provider.Returns(300);
        mockOffer.TripType.Returns(TripType.Outbound);
        mockOffer.OutboundFlightNumbers.Returns(new List<(string, string)> { ("AA", "300") });

        var result = await mockOffer.CreateId(_supplierStorage);
        result.Should().Be("DEFGHI250505300O||AA300");
    }

    [Fact]
    public async Task CreateId_InboundOnly_ShouldReturnExpectedString()
    {
        _supplierStorage.GetSupplierId("supplier4").Returns(15);
        
        var mockOffer = Substitute.For<IIdentifiableFlightOfferOneWay>();
        mockOffer.Departure.Returns("JKL");
        mockOffer.Arrival.Returns("MNO");
        mockOffer.DepartureDate.Returns(new DateTime(2023, 3, 3));
        mockOffer.Provider.Returns(400);
        mockOffer.Supplier.Returns("supplier4");
        mockOffer.TripType.Returns(TripType.Inbound);
        mockOffer.OutboundFlightNumbers.Returns(new List<(string, string)> { ("BA", "400") });

        var result = await mockOffer.CreateId(_supplierStorage);
        result.Should().Be("JKLMNO230303400I.15||BA400");
    }

    [Fact]
    public async Task CreateId_OutboundAndInbound_ShouldReturnExpectedString()
    {
        var mockOffer = Substitute.For<IIdentifiableFlightOfferOneWay>();
        mockOffer.Departure.Returns("PQR");
        mockOffer.Arrival.Returns("STU");
        mockOffer.DepartureDate.Returns(new DateTime(2022, 2, 2));
        mockOffer.Provider.Returns(500);
        mockOffer.TripType.Returns(TripType.Outbound | TripType.Inbound);
        mockOffer.OutboundFlightNumbers.Returns(new List<(string, string)> { ("LH", "500") });

        var result = await mockOffer.CreateId(_supplierStorage);
        result.Should().Be("PQRSTU220202500S||LH500");
    }

    [Fact]
    public async Task CreateId_NoDeparture_ShouldHandleGracefully()
    {
        var mockOffer = Substitute.For<IIdentifiableFlightOfferOneWay>();
        mockOffer.Departure.Returns(string.Empty);
        mockOffer.Arrival.Returns("XYZ");
        mockOffer.DepartureDate.Returns(DateTime.UtcNow);
        mockOffer.Provider.Returns(101);
        mockOffer.Supplier.Returns("test");
        mockOffer.TripType.Returns(TripType.OneWay);
        mockOffer.OutboundFlightNumbers.Returns(new List<(string, string)> { ("UA", "123") });

        var result = await mockOffer.CreateId(_supplierStorage);
        result.Should().Contain("XYZ");
    }

    [Fact]
    public async Task CreateId_NullSupplier_ShouldReturnExpectedString()
    {
        _supplierStorage.GetSupplierId(null).Returns(0);

        var mockOffer = Substitute.For<IIdentifiableFlightOfferOneWay>();
        mockOffer.Departure.Returns("ABC");
        mockOffer.Arrival.Returns("XYZ");
        mockOffer.DepartureDate.Returns(new DateTime(2035, 1, 1));
        mockOffer.Provider.Returns(200);
        mockOffer.Supplier.Returns((string)null);
        mockOffer.TripType.Returns(TripType.OneWay);
        mockOffer.OutboundFlightNumbers.Returns(new List<(string, string)> { ("UA", "999") });

        var result = await mockOffer.CreateId(_supplierStorage);
        result.Should().Be("ABCXYZ350101200||UA999");
    }

    [Fact]
    public async Task CreateId_EmptyFlightNumbers_ShouldHandleGracefully()
    {
        _supplierStorage.GetSupplierId("someSupplier").Returns(15);
        
        var mockOffer = Substitute.For<IIdentifiableFlightOfferOneWay>();
        mockOffer.Departure.Returns("MNO");
        mockOffer.Arrival.Returns("PQR");
        mockOffer.DepartureDate.Returns(new DateTime(2050, 5, 5));
        mockOffer.Provider.Returns(300);
        mockOffer.Supplier.Returns("someSupplier");
        mockOffer.TripType.Returns(TripType.RoundTrip);

        var result = await mockOffer.CreateId(_supplierStorage);
        result.Should().Be("MNOPQR500505300.15||");
    }
}