using Esky.FlightsCache.Database.Helpers;
using Esky.FlightsCache.MessageContract;
using System;

namespace Esky.FlightsCache.Processing.Tests.Database
{
    public class StayLengthHelperTests
    {
        [Fact]
        public void GetArrivalToDeparture_ShouldReturnCorrectStayLength()
        {
            var outboundLeg = new FlightCacheLeg
            {
                ArrivalDate = new DateTime(2025, 04, 05, 23, 0, 0)
            };
            var inboundLeg = new FlightCacheLeg
            {
                DepartureDate = new DateTime(2025, 04, 10, 3, 0, 0)
            };

            var result = StayLengthHelper.GetArrivalToDeparture(outboundLeg, inboundLeg);

            result.Should().Be(5);
        }

        [Fact]
        public void GetArrivalToDeparture_ShouldHandleSameDayArrivalAndDeparture()
        {
            var outboundLeg = new FlightCacheLeg
            {
                ArrivalDate = new DateTime(2025, 04, 05, 1, 0, 0)
            };
            var inboundLeg = new FlightCacheLeg
            {
                DepartureDate = new DateTime(2025, 04, 05, 5, 0, 0)
            };

            var result = StayLengthHelper.GetArrivalToDeparture(outboundLeg, inboundLeg);

            result.Should().Be(0);
        }

        [Fact]
        public void GetArrivalToDeparture_ShouldHandleLateArrivalAndEarlyDeparture()
        {
            var outboundLeg = new FlightCacheLeg
            {
                ArrivalDate = new DateTime(2025, 04, 05, 23, 0, 0)
            };
            var inboundLeg = new FlightCacheLeg
            {
                DepartureDate = new DateTime(2025, 04, 06, 1, 0, 0)
            };

            var result = StayLengthHelper.GetArrivalToDeparture(outboundLeg, inboundLeg);

            result.Should().Be(1);
        }

        [Fact]
        public void GetDepartureToDeparture_ShouldReturnCorrectStayLength()
        {
            var outboundLeg = new FlightCacheLeg
            {
                DepartureDate = new DateTime(2025, 04, 05)
            };
            var inboundLeg = new FlightCacheLeg
            {
                DepartureDate = new DateTime(2025, 04, 10)
            };

            var result = StayLengthHelper.GetDepartureToDeparture(outboundLeg, inboundLeg);

            result.Should().Be(5);
        }

        [Fact]
        public void GetDepartureToDeparture_ShouldHandleSameDayDepartureAndReturn()
        {
            var outboundLeg = new FlightCacheLeg
            {
                DepartureDate = new DateTime(2025, 04, 05)
            };
            var inboundLeg = new FlightCacheLeg
            {
                DepartureDate = new DateTime(2025, 04, 05)
            };

            var result = StayLengthHelper.GetDepartureToDeparture(outboundLeg, inboundLeg);

            result.Should().Be(0);
        }

        [Fact]
        public void GetDepartureToDeparture_ShouldHandleLeapYear()
        {
            var outboundLeg = new FlightCacheLeg
            {
                DepartureDate = new DateTime(2024, 02, 28)
            };
            var inboundLeg = new FlightCacheLeg
            {
                DepartureDate = new DateTime(2024, 03, 01)
            };

            var result = StayLengthHelper.GetDepartureToDeparture(outboundLeg, inboundLeg);

            result.Should().Be(2);
        }
    }
}