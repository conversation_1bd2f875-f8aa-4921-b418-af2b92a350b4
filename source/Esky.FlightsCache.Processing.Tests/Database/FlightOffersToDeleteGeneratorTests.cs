using Esky.FlightsCache.Database.FlightOffers;
using Esky.FlightsCache.Database.FlightOffers.Model;
using Esky.FlightsCache.MessageContract;
using Esky.FlightsCache.ProviderMapping;
using Esky.FlightsCache.ResultFilters.AirportCode;
using NSubstitute;
using System;
using System.Threading.Tasks;

namespace Esky.FlightsCache.Processing.Tests.Database;

public class FlightOffersToDeleteGeneratorTests
{
    private readonly IAirportsRepository _airportsRepository = Substitute.For<IAirportsRepository>();
    private readonly IFlightsCacheProviderConverter _flightsCacheProviderConverter = Substitute.For<IFlightsCacheProviderConverter>();

    public FlightOffersToDeleteGeneratorTests()
    {
        var airportCollection = new AirportCollection(
        [
            new AirportCollection.Airport { Code = "WAW", Multiport = "WAWA", CityCode = "WAW", CountryCode = "PL", ContinentCode = "EU", NearbyAirports = [] },
            new AirportCollection.Airport { Code = "WMI", Multiport = "WAWA", CityCode = "WAW", CountryCode = "PL", ContinentCode = "EU", NearbyAirports = [] },
            new AirportCollection.Airport { Code = "RDO", Multiport = "WAWA", CityCode = "WAW", CountryCode = "PL", ContinentCode = "EU", NearbyAirports = [] },
            
            new AirportCollection.Airport { Code = "LHR", Multiport = "LON", CityCode = "LON", CountryCode = "GB", ContinentCode = "EU", NearbyAirports = [] },
            new AirportCollection.Airport { Code = "LGW", Multiport = "LON", CityCode = "LON", CountryCode = "GB", ContinentCode = "EU", NearbyAirports = [] },
            new AirportCollection.Airport { Code = "LTN", Multiport = "LON", CityCode = "LON", CountryCode = "GB", ContinentCode = "EU", NearbyAirports = [] },
            new AirportCollection.Airport { Code = "SEN", Multiport = "LON", CityCode = "LON", CountryCode = "GB", ContinentCode = "EU", NearbyAirports = [] },
            new AirportCollection.Airport { Code = "STN", Multiport = "LON", CityCode = "LON", CountryCode = "GB", ContinentCode = "EU", NearbyAirports = [] },
            new AirportCollection.Airport { Code = "LCY", Multiport = "LON", CityCode = "LON", CountryCode = "GB", ContinentCode = "EU", NearbyAirports = [] }
        ]);

        _airportsRepository.GetAirportsAsync().Returns(Task.FromResult(airportCollection));

        _flightsCacheProviderConverter.GetCacheProviderCodes(6, null).Returns([]);
        _flightsCacheProviderConverter.GetCacheProviderCodes(6, "tui").Returns([248]);
        _flightsCacheProviderConverter.GetCacheProviderCodes(6, "wizzair").Returns([223]);
        _flightsCacheProviderConverter.GetCacheProviderCodes(19, null).Returns([160, 161, 162]);
        _flightsCacheProviderConverter.GetCacheProviderCodes(58, null).Returns([157, 158]);
    }

    [Fact]
    public async Task GenerateFlightOffersToDeleteOneWay_ShouldReturnDefault_WhenNoFlightOffersProvided_SimpleHappyPath()
    {
        var cacheRequest = new CacheRequest
        {
            SourceDescription = new SourceDescription { SearchDepartureCode = "WAW", SearchArrivalCode = "LHR", SearchDepartureDate = DateTime.Now.Date.AddDays(10), Supplier = "tui" },
            CommandOptions = new CommandDataOptions
            {
                DeleteOptions = new DeleteOptions { ProviderCodes = [6], DeleteDepartureDayFrom = DateTime.Now.Date.AddDays(10), DeleteDepartureDayTo = DateTime.Now.Date.AddDays(10) }
            },
            Flights = []
        };

        var flightOffersToDeleteGenerator = new FlightOffersToDeleteGenerator(_airportsRepository, _flightsCacheProviderConverter);
        var result = await flightOffersToDeleteGenerator.GenerateFlightOffersToDeleteOneWay([], cacheRequest);

        result.Should().ContainSingle();
        result[0].DepartureFrom.Should().Be(DateTime.Now.Date.AddDays(10));
        result[0].DepartureTo.Should().Be(DateTime.Now.Date.AddDays(10));
        result[0].ProviderSuppliers.Should().BeEquivalentTo([(248, "tui")]);
        result[0].Routes.Should().BeEquivalentTo([("WAW", "LHR")]);
        result[0].TripTypes.Should().BeEquivalentTo([TripType.OneWay | TripType.Outbound | TripType.Inbound, TripType.OneWay | TripType.Outbound, TripType.Inbound]);
    }

    [Fact]
    public async Task GenerateFlightOffersToDeleteOneWay_ShouldReturnDefault_WhenNoFlightOffersProvided_WhenMultiports()
    {
        var cacheRequest = new CacheRequest
        {
            SourceDescription = new SourceDescription { SearchDepartureCode = "WAWA", SearchArrivalCode = "LON", SearchDepartureDate = DateTime.Now.Date.AddDays(10), Supplier = "tui" },
            CommandOptions = new CommandDataOptions
            {
                DeleteOptions = new DeleteOptions { ProviderCodes = [6], DeleteDepartureDayFrom = DateTime.Now.Date.AddDays(10), DeleteDepartureDayTo = DateTime.Now.Date.AddDays(10) }
            },
            Flights = []
        };

        var flightOffersToDeleteGenerator = new FlightOffersToDeleteGenerator(_airportsRepository, _flightsCacheProviderConverter);
        var result = await flightOffersToDeleteGenerator.GenerateFlightOffersToDeleteOneWay([], cacheRequest);

        result.Should().BeEmpty();
    }

    [Fact]
    public async Task GenerateFlightOffersToDeleteOneWay_ShouldReturnDefault_WhenNoFlightOffersProvided_WhenFewCacheProviderCodes()
    {
        var cacheRequest = new CacheRequest
        {
            SourceDescription = new SourceDescription { SearchDepartureCode = "WAW", SearchArrivalCode = "LHR", SearchDepartureDate = DateTime.Now.Date.AddDays(10) },
            CommandOptions = new CommandDataOptions
            {
                DeleteOptions = new DeleteOptions { ProviderCodes = [19], DeleteDepartureDayFrom = DateTime.Now.Date.AddDays(10), DeleteDepartureDayTo = DateTime.Now.Date.AddDays(10) }
            },
            Flights = []
        };

        var flightOffersToDeleteGenerator = new FlightOffersToDeleteGenerator(_airportsRepository, _flightsCacheProviderConverter);
        var result = await flightOffersToDeleteGenerator.GenerateFlightOffersToDeleteOneWay([], cacheRequest);

        result.Should().ContainSingle();
        result[0].DepartureFrom.Should().Be(DateTime.Now.Date.AddDays(10));
        result[0].DepartureTo.Should().Be(DateTime.Now.Date.AddDays(10));
        result[0].ProviderSuppliers.Should().BeEquivalentTo([(160, (string)null), (161, null), (162, null)]);
        result[0].Routes.Should().BeEquivalentTo([("WAW", "LHR")]);
        result[0].TripTypes.Should().BeEquivalentTo([TripType.OneWay | TripType.Outbound | TripType.Inbound, TripType.OneWay | TripType.Outbound, TripType.Inbound]);
    }

    [Fact]
    public async Task GenerateFlightOffersToDeleteOneWay_ShouldReturnDefault_WhenNoFlightOffersProvided_WhenNoCacheProviderCodes()
    {
        var cacheRequest = new CacheRequest
        {
            SourceDescription = new SourceDescription { SearchDepartureCode = "WAW", SearchArrivalCode = "LHR", SearchDepartureDate = DateTime.Now.Date.AddDays(10) },
            CommandOptions = new CommandDataOptions
            {
                DeleteOptions = new DeleteOptions { ProviderCodes = [6], DeleteDepartureDayFrom = DateTime.Now.Date.AddDays(10), DeleteDepartureDayTo = DateTime.Now.Date.AddDays(10) }
            },
            Flights = []
        };

        var flightOffersToDeleteGenerator = new FlightOffersToDeleteGenerator(_airportsRepository, _flightsCacheProviderConverter);
        var result = await flightOffersToDeleteGenerator.GenerateFlightOffersToDeleteOneWay([], cacheRequest);

        result.Should().ContainSingle();
        result[0].DepartureFrom.Should().Be(DateTime.Now.Date.AddDays(10));
        result[0].DepartureTo.Should().Be(DateTime.Now.Date.AddDays(10));
        result[0].ProviderSuppliers.Should().BeEquivalentTo([(6, (string)null)]);
        result[0].Routes.Should().BeEquivalentTo([("WAW", "LHR")]);
        result[0].TripTypes.Should().BeEquivalentTo([TripType.OneWay | TripType.Outbound | TripType.Inbound, TripType.OneWay | TripType.Outbound, TripType.Inbound]);
    }

    [Fact]
    public async Task GenerateFlightOffersToDeleteOneWay_ShouldReturnDefault_WhenNoFlightOffersProvided_WhenFewProvidersInDeleteOptions()
    {
        var cacheRequest = new CacheRequest
        {
            SourceDescription = new SourceDescription { SearchDepartureCode = "WAW", SearchArrivalCode = "LHR", SearchDepartureDate = DateTime.Now.Date.AddDays(10) },
            CommandOptions = new CommandDataOptions
            {
                DeleteOptions = new DeleteOptions { ProviderCodes = [19, 58], DeleteDepartureDayFrom = DateTime.Now.Date.AddDays(10), DeleteDepartureDayTo = DateTime.Now.Date.AddDays(10) }
            },
            Flights = []
        };

        var flightOffersToDeleteGenerator = new FlightOffersToDeleteGenerator(_airportsRepository, _flightsCacheProviderConverter);
        var result = await flightOffersToDeleteGenerator.GenerateFlightOffersToDeleteOneWay([], cacheRequest);

        result.Should().ContainSingle();
        result[0].DepartureFrom.Should().Be(DateTime.Now.Date.AddDays(10));
        result[0].DepartureTo.Should().Be(DateTime.Now.Date.AddDays(10));
        result[0].ProviderSuppliers.Should().BeEquivalentTo([(160, (string)null), (161, null), (162, null), (157, null), (158, null)]);
        result[0].Routes.Should().BeEquivalentTo([("WAW", "LHR")]);
        result[0].TripTypes.Should().BeEquivalentTo([TripType.OneWay | TripType.Outbound | TripType.Inbound, TripType.OneWay | TripType.Outbound, TripType.Inbound]);
    }

    [Fact]
    public async Task GenerateFlightOffersToDeleteOneWay_ShouldReturnDefault_WhenNoFlightOffersProvided_WhenDeleteDatesAreRange()
    {
        var cacheRequest = new CacheRequest
        {
            SourceDescription = new SourceDescription { SearchDepartureCode = "WAW", SearchArrivalCode = "LHR", SearchDepartureDate = DateTime.Now.Date.AddDays(10), Supplier = "tui" },
            CommandOptions = new CommandDataOptions
            {
                DeleteOptions = new DeleteOptions { ProviderCodes = [6], DeleteDepartureDayFrom = DateTime.Now.Date.AddDays(5), DeleteDepartureDayTo = DateTime.Now.Date.AddDays(10) }
            },
            Flights = []
        };

        var flightOffersToDeleteGenerator = new FlightOffersToDeleteGenerator(_airportsRepository, _flightsCacheProviderConverter);
        var result = await flightOffersToDeleteGenerator.GenerateFlightOffersToDeleteOneWay([], cacheRequest);

        result.Should().ContainSingle();
        result[0].DepartureFrom.Should().Be(DateTime.Now.Date.AddDays(5));
        result[0].DepartureTo.Should().Be(DateTime.Now.Date.AddDays(10));
        result[0].ProviderSuppliers.Should().BeEquivalentTo([(248, "tui")]);
        result[0].Routes.Should().BeEquivalentTo([("WAW", "LHR")]);
        result[0].TripTypes.Should().BeEquivalentTo([TripType.OneWay | TripType.Outbound | TripType.Inbound, TripType.OneWay | TripType.Outbound, TripType.Inbound]);
    }

    [Fact]
    public async Task GenerateFlightOffersToDeleteOneWay_ShouldReturnKeys_WithDifferentTripTypes()
    {
        var cacheRequest = new CacheRequest
        {
            SourceDescription = new SourceDescription { SearchDepartureCode = "WAW", SearchArrivalCode = "LON", SearchDepartureDate = DateTime.Now.Date.AddDays(10), Supplier = "tui" },
            CommandOptions = new CommandDataOptions
            {
                DeleteOptions = new DeleteOptions { DeleteDepartureDayFrom = DateTime.Now.Date.AddDays(10), DeleteDepartureDayTo = DateTime.Now.Date.AddDays(10) }
            },
            Flights = [new FlightCache()]
        };

        var now = DateTime.UtcNow;

        var flightOffers = new[]
        {
            FlightOfferOneWay.Empty with
            {
                ModificationDate = now,
                Provider = 248,
                Supplier = "tui",
                Route = new Route { Departure = "WAW", Arrival = "LHR", MultiportDeparture = "WAWA", MultiportArrival = "LON" },
                TripType = TripType.OneWay | TripType.Outbound | TripType.Inbound
            },
            FlightOfferOneWay.Empty with
            {
                ModificationDate = now,
                Provider = 248,
                Supplier = "tui",
                Route = new Route { Departure = "WAW", Arrival = "LHR", MultiportDeparture = "WAWA", MultiportArrival = "LON" },
                TripType = TripType.OneWay | TripType.Outbound | TripType.Inbound
            },
            FlightOfferOneWay.Empty with
            {
                ModificationDate = now,
                Provider = 248,
                Supplier = "tui",
                Route = new Route { Departure = "WAW", Arrival = "LHR", MultiportDeparture = "WAWA", MultiportArrival = "LON" },
                TripType = TripType.OneWay | TripType.Outbound
            },
            FlightOfferOneWay.Empty with
            {
                ModificationDate = now,
                Provider = 248,
                Supplier = "tui",
                Route = new Route { Departure = "WAW", Arrival = "LHR", MultiportDeparture = "WAWA", MultiportArrival = "LON" },
                TripType = TripType.Inbound
            }
        };

        var flightOffersToDeleteGenerator = new FlightOffersToDeleteGenerator(_airportsRepository, _flightsCacheProviderConverter);
        var result = await flightOffersToDeleteGenerator.GenerateFlightOffersToDeleteOneWay(flightOffers, cacheRequest);

        result.Should().ContainSingle();
        result[0].DepartureFrom.Should().Be(DateTime.Now.Date.AddDays(10));
        result[0].DepartureTo.Should().Be(DateTime.Now.Date.AddDays(10));
        result[0].ProviderSuppliers.Should().BeEquivalentTo([(248, "tui")]);
        result[0].Routes.Should().BeEquivalentTo([("WAW", "LHR")]);
        result[0].TripTypes.Should().BeEquivalentTo([TripType.OneWay | TripType.Outbound | TripType.Inbound, TripType.OneWay | TripType.Outbound, TripType.Inbound]);
        result[0].ModificationDateEarlierThan.Should().Be(now);
    }

    [Fact]
    public async Task GenerateFlightOffersToDeleteOneWay_ShouldReturnKeys_WithoutMapping()
    {
        var cacheRequest = new CacheRequest
        {
            SourceDescription = new SourceDescription { SearchDepartureCode = "WAW", SearchArrivalCode = "LON", SearchDepartureDate = DateTime.Now.Date.AddDays(10) },
            CommandOptions = new CommandDataOptions
            {
                DeleteOptions = new DeleteOptions { DeleteDepartureDayFrom = DateTime.Now.Date.AddDays(10), DeleteDepartureDayTo = DateTime.Now.Date.AddDays(10) }
            },
            Flights = [new FlightCache()]
        };

        var now = DateTime.UtcNow;

        var flightOffers = new[]
        {
            FlightOfferOneWay.Empty with
            {
                ModificationDate = now,
                Provider = 6,
                Route = new Route { Departure = "WAW", Arrival = "LHR", MultiportDeparture = "WAWA", MultiportArrival = "LON" },
                TripType = TripType.OneWay
            }
        };

        var flightOffersToDeleteGenerator = new FlightOffersToDeleteGenerator(_airportsRepository, _flightsCacheProviderConverter);
        var result = await flightOffersToDeleteGenerator.GenerateFlightOffersToDeleteOneWay(flightOffers, cacheRequest);

        result.Should().ContainSingle();
        result[0].DepartureFrom.Should().Be(DateTime.Now.Date.AddDays(10));
        result[0].DepartureTo.Should().Be(DateTime.Now.Date.AddDays(10));
        result[0].ProviderSuppliers.Should().BeEquivalentTo([(6, (string)null)]);
        result[0].Routes.Should().BeEquivalentTo([("WAW", "LHR")]);
        result[0].TripTypes.Should().BeEquivalentTo([TripType.OneWay]);
        result[0].ModificationDateEarlierThan.Should().Be(now);
    }

    [Fact]
    public async Task GenerateFlightOffersToDeleteOneWay_ShouldReturnKeys_WithNearbyAirport()
    {
        var cacheRequest = new CacheRequest
        {
            SourceDescription = new SourceDescription { SearchDepartureCode = "WAW", SearchArrivalCode = "LON", SearchDepartureDate = DateTime.Now.Date.AddDays(10) },
            CommandOptions = new CommandDataOptions
            {
                DeleteOptions = new DeleteOptions { DeleteDepartureDayFrom = DateTime.Now.Date.AddDays(10), DeleteDepartureDayTo = DateTime.Now.Date.AddDays(10) }
            },
            Flights = [new FlightCache()]
        };

        var now = DateTime.UtcNow;

        var flightOffers = new[]
        {
            FlightOfferOneWay.Empty with
            {
                ModificationDate = now,
                Provider = 19,
                Route = new Route { Departure = "WMI", Arrival = "LHR", MultiportDeparture = "WAWA", MultiportArrival = "LON" },
                TripType = TripType.OneWay
            }
        };

        var flightOffersToDeleteGenerator = new FlightOffersToDeleteGenerator(_airportsRepository, _flightsCacheProviderConverter);
        var result = await flightOffersToDeleteGenerator.GenerateFlightOffersToDeleteOneWay(flightOffers, cacheRequest);

        result.Should().BeEmpty();
    }

    [Fact]
    public async Task GenerateFlightOffersToDeleteOneWay_ShouldReturnKeys_WithDeleteDatesRange()
    {
        var cacheRequest = new CacheRequest
        {
            SourceDescription = new SourceDescription { SearchDepartureCode = "WAW", SearchArrivalCode = "LHR", SearchDepartureDate = DateTime.Now.Date.AddDays(10), Supplier = "tui" },
            CommandOptions = new CommandDataOptions
            {
                DeleteOptions = new DeleteOptions { ProviderCodes = [6], DeleteDepartureDayFrom = DateTime.Now.Date.AddDays(5), DeleteDepartureDayTo = DateTime.Now.Date.AddDays(10) }
            },
            Flights = [new FlightCache()]
        };

        var now = DateTime.UtcNow;

        var flightOffers = new[]
        {
            FlightOfferOneWay.Empty with
            {
                ModificationDate = now,
                Provider = 19,
                Route = new Route { Departure = "WAW", Arrival = "LHR", MultiportDeparture = "WAWA", MultiportArrival = "LON" },
                TripType = TripType.OneWay
            }
        };

        var flightOffersToDeleteGenerator = new FlightOffersToDeleteGenerator(_airportsRepository, _flightsCacheProviderConverter);
        var result = await flightOffersToDeleteGenerator.GenerateFlightOffersToDeleteOneWay(flightOffers, cacheRequest);

        result.Should().ContainSingle();
        result[0].DepartureFrom.Should().Be(DateTime.Now.Date.AddDays(5));
        result[0].DepartureTo.Should().Be(DateTime.Now.Date.AddDays(10));
        result[0].ProviderSuppliers.Should().BeEquivalentTo([(19, (string)null)]);
        result[0].Routes.Should().BeEquivalentTo([("WAW", "LHR")]);
        result[0].TripTypes.Should().BeEquivalentTo([TripType.OneWay]);
        result[0].ModificationDateEarlierThan.Should().Be(now);
    }

    [Fact]
    public async Task GenerateFlightOffersToDeleteRoundTrip_ShouldReturnKeys_HappyPath()
    {
        var cacheRequest = new CacheRequest
        {
            SourceDescription = new SourceDescription { SearchDepartureCode = "WAW", SearchArrivalCode = "LON", SearchDepartureDate = DateTime.Now.Date.AddDays(10), Supplier = "tui" },
            CommandOptions = new CommandDataOptions
            {
                DeleteOptions = new DeleteOptions
                {
                    DeleteDepartureDayFrom = DateTime.Now.Date.AddDays(10),
                    DeleteDepartureDayTo = DateTime.Now.Date.AddDays(10),
                    DeleteReturnDepartureDayFrom = DateTime.Now.Date.AddDays(15),
                    DeleteReturnDepartureDayTo = DateTime.Now.Date.AddDays(15)
                }
            }
        };

        var now = DateTime.UtcNow;

        var flightOffers = new[]
        {
            FlightOfferRoundTrip.Empty with
            {
                ModificationDate = now,
                Provider = 6,
                Supplier = "tui",
                Route = new Route { Departure = "WAW", Arrival = "LHR", MultiportDeparture = "WAWA", MultiportArrival = "LON" }
            },
            FlightOfferRoundTrip.Empty with
            {
                ModificationDate = now,
                Provider = 6,
                Supplier = "tui",
                Route = new Route { Departure = "WAW", Arrival = "LHR", MultiportDeparture = "WAWA", MultiportArrival = "LON" }
            },
            FlightOfferRoundTrip.Empty with
            {
                ModificationDate = now,
                Provider = 6,
                Supplier = "tui",
                Route = new Route { Departure = "WAW", Arrival = "LHR", MultiportDeparture = "WAWA", MultiportArrival = "LON" }
            },
            FlightOfferRoundTrip.Empty with
            {
                ModificationDate = now,
                Provider = 6,
                Supplier = "tui",
                Route = new Route { Departure = "WAW", Arrival = "LHR", MultiportDeparture = "WAWA", MultiportArrival = "LON" }
            }
        };

        var flightOffersToDeleteGenerator = new FlightOffersToDeleteGenerator(_airportsRepository, _flightsCacheProviderConverter);
        var result = await flightOffersToDeleteGenerator.GenerateFlightOffersToDeleteRoundTrip(flightOffers, cacheRequest);

        result.Should().ContainSingle();
        result[0].DepartureFrom.Should().Be(DateTime.Now.Date.AddDays(10));
        result[0].DepartureTo.Should().Be(DateTime.Now.Date.AddDays(10));
        result[0].StayLength.Should().Be(5);
        result[0].ProviderSuppliers.Should().BeEquivalentTo([(6, "tui")]);
        result[0].Routes.Should().BeEquivalentTo([("WAW", "LHR")]);
        result[0].ModificationDateEarlierThan.Should().Be(now);
    }

    [Fact]
    public async Task GenerateFlightOffersToDeleteRoundTrip_ShouldReturnKeys_WithNearbyAirport()
    {
        var cacheRequest = new CacheRequest
        {
            SourceDescription = new SourceDescription { SearchDepartureCode = "WAW", SearchArrivalCode = "LON", SearchDepartureDate = DateTime.Now.Date.AddDays(10) },
            CommandOptions = new CommandDataOptions
            {
                DeleteOptions = new DeleteOptions
                {
                    DeleteDepartureDayFrom = DateTime.Now.Date.AddDays(10),
                    DeleteDepartureDayTo = DateTime.Now.Date.AddDays(10),
                    DeleteReturnDepartureDayFrom = DateTime.Now.Date.AddDays(15),
                    DeleteReturnDepartureDayTo = DateTime.Now.Date.AddDays(15)
                }
            }
        };

        var flightOffers = new[] { FlightOfferRoundTrip.Empty with { Provider = 19, Route = new Route { Departure = "WMI", Arrival = "LHR", MultiportDeparture = "WAWA", MultiportArrival = "LON" } } };

        var flightOffersToDeleteGenerator = new FlightOffersToDeleteGenerator(_airportsRepository, _flightsCacheProviderConverter);
        var result = await flightOffersToDeleteGenerator.GenerateFlightOffersToDeleteRoundTrip(flightOffers, cacheRequest);

        result.Should().BeEmpty();
    }

    [Fact]
    public async Task GenerateFlightOffersToDeleteRoundTrip_ShouldReturnKeys_WithDeleteDatesRange()
    {
        var cacheRequest = new CacheRequest
        {
            SourceDescription = new SourceDescription { SearchDepartureCode = "WAW", SearchArrivalCode = "LHR", SearchDepartureDate = DateTime.Now.Date.AddDays(10) },
            CommandOptions = new CommandDataOptions
            {
                DeleteOptions = new DeleteOptions
                {
                    ProviderCodes = [6],
                    DeleteDepartureDayFrom = DateTime.Now.Date.AddDays(9),
                    DeleteDepartureDayTo = DateTime.Now.Date.AddDays(10),
                    DeleteReturnDepartureDayFrom = DateTime.Now.Date.AddDays(15),
                    DeleteReturnDepartureDayTo = DateTime.Now.Date.AddDays(16)
                }
            }
        };

        var flightOffers = new[]
        {
            FlightOfferRoundTrip.Empty with
            {
                Provider = 19, Route = new Route { Departure = "WAW", Arrival = "LHR", MultiportDeparture = "WAWA", MultiportArrival = "LON" }
            }
        };

        var flightOffersToDeleteGenerator = new FlightOffersToDeleteGenerator(_airportsRepository, _flightsCacheProviderConverter);
        var result = await flightOffersToDeleteGenerator.GenerateFlightOffersToDeleteRoundTrip(flightOffers, cacheRequest);

        result.Should().HaveCount(1);
        result[0].DepartureFrom.Should().Be(DateTime.Now.Date.AddDays(9));
        result[0].DepartureTo.Should().Be(DateTime.Now.Date.AddDays(10));
        result[0].StayLength.Should().Be(6);
        result[0].ProviderSuppliers.Should().BeEquivalentTo([(19, (string)null)]);
        result[0].Routes.Should().BeEquivalentTo([("WAW", "LHR")]);
    }
}
























