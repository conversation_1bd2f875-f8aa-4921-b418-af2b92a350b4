using Esky.FlightsCache.Common;
using Esky.FlightsCache.Database;
using Esky.FlightsCache.Database.Aggregations;
using Esky.FlightsCache.Database.FlightOffers;
using Esky.FlightsCache.OpenTelemetry;
using Microsoft.Extensions.Options;

namespace Esky.FlightsCache.MonthlyAggregator;

public static class RegistrationExtensions
{
    public static IServiceCollection RegisterServices(this IServiceCollection services, IConfiguration configuration)
    {
        services
            .AddLogging()
            .ConfigureOpenTelemetry(configuration.GetSection("OpenTelemetry").Get<OpenTelemetryOptions>()!)
            .RegisterOpenTracingShim()
            .AddHostedService<MonthlyAggregationHostedService>()
            .AddSingleton<IRouteAggregationBuffer, RedisRouteAggregationBuffer>()
            .AddSingleton<IRedisConnection, RedisConnection>()
            .Configure<AggregationConfiguration>(configuration.GetSection("Aggregation"))
            .Configure<BulkAggregationConfiguration>(configuration.GetSection("Aggregation:BulkAggregation"))
            .AddSingleton(sp => sp.GetRequiredService<IOptions<BulkAggregationConfiguration>>().Value)
            .Configure<RedisCachingConfiguration>(configuration.GetSection("RedisCaching"))
            .AddCustomHealthChecks();

        services.Configure<CacheRequestProcessorConfiguration>(configuration.GetSection("CacheRequestProcessor"));
        
        services.Configure<FlightOffersConfiguration>(configuration.GetSection("FlightOffersConfiguration"));
        services.AddSingleton(sp => sp.GetRequiredService<IOptions<FlightOffersConfiguration>>().Value);
        services.AddSingleton(sp => new DatabaseContext(sp.GetRequiredService<FlightOffersConfiguration>().ConnectionString));

        services.AddSingleton<IRouteAggregationService>(
            sp =>
            {
                var connectionStrings = sp.GetRequiredService<IOptions<FlightOffersConfiguration>>().Value.AdditionalMongoConnectionStrings;

                var aggregationServices = connectionStrings
                    .Select(connectionString => new MonthlyAggregationsService(
                            sp.GetRequiredService<IOptions<AggregationConfiguration>>(),
                            sp.GetRequiredService<ILogger<MonthlyAggregationsService>>(),
                            new DatabaseContext(connectionString)
                        )
                    )
                    .Cast<IRouteAggregationService>()
                    .ToList();

                aggregationServices.Add(
                    new MonthlyAggregationsService(
                        sp.GetRequiredService<IOptions<AggregationConfiguration>>(),
                        sp.GetRequiredService<ILogger<MonthlyAggregationsService>>(),
                        sp.GetRequiredService<DatabaseContext>()
                    )
                );

                return new MultiRouteAggregationService(aggregationServices);
            }
        );
        
        return services;
    }
}