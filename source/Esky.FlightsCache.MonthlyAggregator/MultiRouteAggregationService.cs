using Esky.FlightsCache.Database.Aggregations;

namespace Esky.FlightsCache.MonthlyAggregator;

public class MultiRouteAggregationService : IRouteAggregationService
{
    private readonly IRouteAggregationService[] _aggregationsServices;

    public MultiRouteAggregationService(IEnumerable<IRouteAggregationService> aggregationServices)
    {
        _aggregationsServices = aggregationServices.ToArray();
    }

    public async Task<int> RecalculateMonthlyAggregates(IReadOnlyCollection<RouteUpdateEvent> events)
    {
        var tasks = await Task.WhenAll(_aggregationsServices.Select(s => s.RecalculateMonthlyAggregates(events)));
        return tasks[0];
    }
}