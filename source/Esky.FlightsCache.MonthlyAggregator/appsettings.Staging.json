{"Aggregation": {"DegreeOfParallelism": 1}, "FlightOffersConfiguration": {"ConnectionString": "secret", "AdditionalMongoConnectionStrings": []}, "ServiceBus_Clustermembers": "rabbitmq-logs.service.consul.", "ServiceBus_Login": "ets", "ServiceBus_VHost": "ets", "ServiceBus_Password": "secret", "NLog": {"rules": [{"logger": "Microsoft.AspNetCore.*", "finalMinLevel": "<PERSON><PERSON>"}, {"logger": "*", "minLevel": "Debug", "writeTo": "rabbit", "final": true}]}}