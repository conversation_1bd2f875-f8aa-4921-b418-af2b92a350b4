{
    "RedisCaching": {
        "ConnectionString": "secret"
    },
    "CacheRequestProcessor": {
        "TargetDbConnectionString": "secret",
    },
    "FlightOffersConfiguration": {
        "ConnectionString": "secret",
        "AdditionalMongoConnectionStrings": []
    },
    "ServiceBus_Clustermembers": "rabbitmq-logs.service.consul.",
    "ServiceBus_Login": "ets",
    "ServiceBus_VHost": "ets",
    "ServiceBus_Password": "secret",
    "NLog": {
        "rules": [
            { "logger": "Microsoft.AspNetCore.*", "finalMinLevel": "Warn" },
            { "logger": "*", "minLevel": "Info", "writeTo": "rabbit", "final": true }
        ]
    }
}