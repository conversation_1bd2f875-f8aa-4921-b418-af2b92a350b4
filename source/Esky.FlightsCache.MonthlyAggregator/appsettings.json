{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "Aggregation": {"IsGlobalMultiticketAggregationDisabled": false, "IsProviderMultiticketAggregationDisabled": false, "IsOneWayAggregationDisabled": false, "IsRoundTripAggregationDisabled": false, "DegreeOfParallelism": 8, "BulkAggregation": {"IsEnabledForOneWay": true, "IntervalInMinutes": 3, "ProcessingTimeoutInMinutes": 45, "MaxElementsToProcess": 100000, "MaxBufferLength": 10000000, "GroupingMode": "RouteAndConsecutiveMonths"}}, "RedisCaching": {"InstanceName": "FlightsCache"}, "OpenTelemetry": {"ServiceName": "FlightsCache.MonthlyAggregator", "Metrics": "FlightsCache.Database,FlightsCache.MonthlyAggregator"}, "CacheRequestProcessor": {"TargetDbConnectionString": "secret"}, "NLog": {"throwConfigExceptions": true, "internalLogFile": "~\\nlog\\internal-nlog.txt", "internalLogLevel": "<PERSON><PERSON>", "internalLogToConsole": true, "autoReload": true, "extensions": [{"assembly": "Esky.NLog.RabbitMQ.Target"}], "targets": {"async": true, "rabbit": {"type": "RabbitMQ", "username": "${configsetting:item=ServiceBus_Login}", "password": "${configsetting:item=ServiceBus_Password}", "clustermembers": "${configsetting:item=ServiceBus_Clustermembers}", "vhost": "${configsetting:item=ServiceBus_VHost}", "compression": "GZip", "fields": [{"key": "HostName", "name": "HostName", "layout": "${machinename}"}, {"key": "Date", "name": "Date", "layout": "${date:universalTime=False:format=s}"}, {"key": "Application", "name": "Application", "layout": "esky-flightscache-monthlyaggregator"}, {"key": "Environment", "name": "Environment", "layout": "${configsetting:item=ASPNETCORE_ENVIRONMENT}"}, {"key": "Exception", "name": "Exception", "layout": "${exception}"}, {"key": "AdditionalMessage", "name": "AdditionalMessage", "layout": "${exception:format=toString,Data}"}, {"key": "StackTrace", "name": "StackTrace", "layout": "${exception:format=StackTrace}"}, {"key": "Context", "name": "Provider", "layout": "${event-properties:Context}"}]}}}}