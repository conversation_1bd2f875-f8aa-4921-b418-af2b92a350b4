{
  "RedisCaching": {
    "ConnectionString": "host.docker.internal:6379,password=k8rBZeXosd,asyncTimeout=120000"
  },
  "CacheRequestProcessor": {
    "TargetDbConnectionString": "Server=host.docker.internal,1433;Database=IBE_FlightsPromoCache;uid=sa;pwd=************;Connection Timeout=30",
  },
  "FlightOffersConfiguration": {
    "ConnectionString": "mongodb://host.docker.internal:27018/FlightsPromoCache?appname=esky-flightscache-monthlyaggregator",
    "AdditionalMongoConnectionStrings": []
  },
  "Aggregation": {
    "DegreeOfParallelism": 1,
    "BulkAggregation": {
      "IsEnabledForOneWay": true,
      "IntervalInMinutes": 1,
      "ProcessingTimeoutInMinutes": 45,
      "MaxElementsToProcess": 100000,
      "MaxBufferLength": 10000000,
      "GroupingMode": "RouteAndConsecutiveMonths"
    }
  },
  "ServiceBus_Clustermembers": "host.docker.internal",
  "ServiceBus_Login": "cacheuser",
  "ServiceBus_VHost": "/",
  "ServiceBus_Password": "cachepassword",
  "NLog": {
    "targets": {
      "console": {
        "type": "Console"
      }
    },
    "rules": [
      {
        "logger": "Microsoft.AspNetCore.*",
        "finalMinLevel": "Warn"
      },
      {
        "logger": "*",
        "minLevel": "Info",
        "writeTo": "console",
        "final": true
      },
      {
        "logger": "*",
        "minLevel": "Info",
        "writeTo": "rabbit"
      }
      // to avoid Warn: Unused target detected
    ]
  }
}
