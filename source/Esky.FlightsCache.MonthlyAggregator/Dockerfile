FROM europe-docker.pkg.dev/esky-common/esky-docker-virtual/dotnet/aspnet:8.0 AS base
ENV ASPNETCORE_HTTP_PORTS=80
WORKDIR /app

FROM europe-docker.pkg.dev/esky-common/esky-docker-virtual/dotnet/sdk:8.0 AS build
WORKDIR /src
COPY ["nuget.config", "."]
COPY ["source/Esky.FlightsCache.MonthlyAggregator/Esky.FlightsCache.MonthlyAggregator.csproj", "source/Esky.FlightsCache.MonthlyAggregator/"]
COPY ["source/Esky.FlightsCache.ResultFilters/Esky.FlightsCache.ResultFilters.csproj", "source/Esky.FlightsCache.ResultFilters/"]
COPY ["source/Esky.FlightsCache.MessageContract/Esky.FlightsCache.MessageContract.csproj", "source/Esky.FlightsCache.MessageContract/"]
COPY ["source/Esky.FlightsCache.ObjectDirectory/Esky.FlightsCache.ObjectDirectory.csproj", "source/Esky.FlightsCache.ObjectDirectory/"]
COPY ["source/Esky.FlightsCache.PartnerSettings/Esky.FlightsCache.PartnerSettings.csproj", "source/Esky.FlightsCache.PartnerSettings/"]
COPY ["source/Esky.FlightsCache.ProviderMapping/Esky.FlightsCache.ProviderMapping.csproj", "source/Esky.FlightsCache.ProviderMapping/"]
COPY ["source/Esky.FlightsCache.Common/Esky.FlightsCache.Common.csproj", "source/Esky.FlightsCache.Common/"]
COPY ["source/Esky.FlightsCache.Database/Esky.FlightsCache.Database.csproj", "source/Esky.FlightsCache.Database/"]
COPY ["source/Esky.FlightsCache.OpenTelemetry/Esky.FlightsCache.OpenTelemetry.csproj", "source/Esky.FlightsCache.OpenTelemetry/"]
RUN dotnet restore "source/Esky.FlightsCache.MonthlyAggregator/Esky.FlightsCache.MonthlyAggregator.csproj"
COPY . .
WORKDIR "/src/source/Esky.FlightsCache.MonthlyAggregator"
RUN dotnet build "Esky.FlightsCache.MonthlyAggregator.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "Esky.FlightsCache.MonthlyAggregator.csproj" -c Release -o /app/publish /p:UseAppHost=false

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "Esky.FlightsCache.MonthlyAggregator.dll"]
