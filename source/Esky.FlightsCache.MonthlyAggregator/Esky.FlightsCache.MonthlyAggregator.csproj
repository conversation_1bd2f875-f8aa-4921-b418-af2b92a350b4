<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
        <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
        <RootNamespace>Esky.FlightsCache.MonthlyAggregator</RootNamespace>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="AspNetCore.HealthChecks.UI.Client" Version="7.1.0"/>
        <PackageReference Include="Esky.NLog.RabbitMQ.Target" Version="1.1.3" />
        <PackageReference Include="NLog.Web.AspNetCore" Version="5.3.5" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\Esky.FlightsCache.Database\Esky.FlightsCache.Database.csproj"/>
        <ProjectReference Include="..\Esky.FlightsCache.OpenTelemetry\Esky.FlightsCache.OpenTelemetry.csproj"/>
    </ItemGroup>

    <ItemGroup>
        <Content Update="appsettings.Development.json">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
            <DependentUpon>appsettings.json</DependentUpon>
        </Content>
        <Content Update="appsettings.Production.json">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
            <DependentUpon>appsettings.json</DependentUpon>
        </Content>
        <Content Update="appsettings.Staging.json">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
            <DependentUpon>appsettings.json</DependentUpon>
        </Content>
    </ItemGroup>

</Project>
