using System.Diagnostics.Metrics;

namespace Esky.FlightsCache.MonthlyAggregator
{
    public static class MonthlyAggregatorMetrics
    {
        private static readonly Meter _meter = new("FlightsCache.MonthlyAggregator");

        public static readonly Counter<long> ProcessedRoutes = _meter.CreateCounter<long>("monthlyaggregator_processed-routes", "row",
            "Number of processed routes in MonthlyAggregator");

        public static readonly Histogram<long> ProcessingTime = _meter.CreateHistogram<long>("monthlyaggregator_processing-time", "ms");
    }
}