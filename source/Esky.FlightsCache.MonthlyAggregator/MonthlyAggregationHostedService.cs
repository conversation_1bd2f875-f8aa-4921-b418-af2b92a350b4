using Esky.FlightsCache.Common;
using Esky.FlightsCache.Database.Aggregations;
using System.Diagnostics;

namespace Esky.FlightsCache.MonthlyAggregator
{
    public class MonthlyAggregationHostedService : BackgroundService
    {
        private readonly IRouteAggregationBuffer _aggregationBuffer;
        private readonly IRouteAggregationService _aggregationService;
        private readonly ILogger<MonthlyAggregationHostedService> _logger;
        private readonly PeriodicTimer _timer;

        public MonthlyAggregationHostedService(
            IRouteAggregationBuffer aggregationBuffer,
            IRouteAggregationService aggregationService,
            BulkAggregationConfiguration configuration,
            ILogger<MonthlyAggregationHostedService> logger)
        {
            _aggregationBuffer = aggregationBuffer;
            _aggregationService = aggregationService;
            _logger = logger;
            _timer = new PeriodicTimer(TimeSpan.FromMinutes(configuration.IntervalInMinutes));
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation("Service started...");

            try
            {
                stoppingToken.Register(() => _logger.LogInformation("Service stopped..."));
                do
                {
                    await CalculateAggregates(RouteTypes.OneWay);
                } while (await _timer.WaitForNextTickAsync(stoppingToken));
            }
            catch (OperationCanceledException) { }
        }

        private async Task CalculateAggregates(string routeType)
        {
            try
            {
                await _aggregationBuffer.InitConsumer(RouteTypes.OneWay);

                _logger.LogInformation("Starting bulk aggregation...");

                var sw = Stopwatch.StartNew();

                (string Key, RouteUpdateEvent Event)[] data = await _aggregationBuffer.GetValues(routeType);
                var processedRouteCount = await _aggregationService.RecalculateMonthlyAggregates(data.Select(x => x.Event).ToArray());
                await _aggregationBuffer.RemoveValues(routeType, data.Select(x => x.Key).ToArray());

                sw.Stop();

                MonthlyAggregatorMetrics.ProcessingTime.Record(sw.ElapsedMilliseconds);
                MonthlyAggregatorMetrics.ProcessedRoutes.Add(processedRouteCount);

                _logger.LogInformation(
                    "Processed {processedRouteCount} {routeType} aggregation operations based on {updateCount} cache update events. Processing time: {elapsedMilliseconds}ms",
                    processedRouteCount,
                    routeType,
                    data.Length,
                    sw.ElapsedMilliseconds);
            }
            catch (Exception e)
            {
                _logger.LogError(e, "Error during bulk aggregation");
            }
        }
    }
}