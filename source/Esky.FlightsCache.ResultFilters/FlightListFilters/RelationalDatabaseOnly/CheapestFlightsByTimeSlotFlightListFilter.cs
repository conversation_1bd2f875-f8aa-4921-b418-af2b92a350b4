using Esky.FlightsCache.MessageContract;
using Esky.FlightsCache.ResultFilters.Contract;
using Esky.FlightsCache.ResultFilters.FlightListFilters.Utilities;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Esky.FlightsCache.ResultFilters.FlightListFilters.RelationalDatabaseOnly
{
    public class CheapestFlightsByTimeSlotFlightListFilter : IFlightListFilter
    {
        private readonly FlightsFilterConfiguration _config;
        private readonly ITimeOfDayBucketizer _timeOfDayBucketizer;

        public CheapestFlightsByTimeSlotFlightListFilter(ITimeOfDayBucketizer timeOfDayBucketizer,
            IOptions<FlightsFilterConfiguration> config)
        {
            _timeOfDayBucketizer = timeOfDayBucketizer;
            _config = config.Value;
        }

        public FilterResult Filter(List<FlightCache> flights, IFlightResultContext context)
        {
            if (!flights.Any() || _config.FlightsPerChunk == 0)
            {
                return new FilterResult { Flights = flights };
            }

            object BucketSelector(DateTime dt)
            {
                return new { dt.Date, Slot = _timeOfDayBucketizer.GetSlotNumber(dt) };
            }

            var shouldSkipFilterLookup = flights.ToLookup(ShouldSkipFilter);

            var cheapestFlights = shouldSkipFilterLookup[false]
                .GroupBy(f =>
                    new
                    {
                        dep = BucketSelector(f.Legs.First().DepartureDate),
                        ret = BucketSelector(f.Legs.Last().DepartureDate)
                    })
                .SelectMany(g => g
                    .OrderBy(f => GetLegPrice(f.Legs.First()))
                    .Take(_config.FlightsPerChunk))
                .Union(shouldSkipFilterLookup[true])
                .ToList();

            var skippedFlightCount = flights.Count - cheapestFlights.Count;

            if (skippedFlightCount > 0)
            {
                return new FilterResult
                {
                    Flights = cheapestFlights,
                    StatisticsMessage = $"Skipped ByTimeSlot: {skippedFlightCount}"
                };
            }

            return new FilterResult { Flights = flights };
        }

        protected virtual bool ShouldSkipFilter(FlightCache f)
        {
            return f.Legs.First().Segments.Count == 1; // do not apply the rule to direct flights
        }

        private decimal GetLegPrice(FlightCacheLeg leg)
        {
            if (leg.AdultPrices != null && leg.AdultPrices.Any())
            {
                var adultPrice = leg.AdultPrices.First();
                var totalPrice = adultPrice.BasePrice + adultPrice.TaxPrice;

                if (leg.ConversionRatioToReferenceCurrency != decimal.Zero)
                {
                    totalPrice *= leg.ConversionRatioToReferenceCurrency;
                }

                return totalPrice;
            }

            return 0M;
        }
    }
}