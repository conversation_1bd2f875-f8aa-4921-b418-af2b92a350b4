using Esky.FlightsCache.MessageContract;
using Esky.FlightsCache.ResultFilters.Contract;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Esky.FlightsCache.ResultFilters.FlightListFilters.RelationalDatabaseOnly
{
    public class DomesticFlightsOnlyFilter : IFlightListFilter
    {
        private readonly bool _isFilterActive;

        private readonly Func<(bool isDomestic, string countryCode), bool> _isGroupValid;

        public DomesticFlightsOnlyFilter(IOptions<FlightsFilterConfiguration> config)
        {
            _isFilterActive = config.Value?.DomesticOnlyFilter?.ExcludeInternational == true;
            if (config.Value?.DomesticOnlyFilter?.Countries?.Length > 0)
            {
                _isGroupValid = group =>
                    group.isDomestic
                    && config.Value.DomesticOnlyFilter.Countries.Contains(group.countryCode);
            }
            else
            {
                _isGroupValid = group => group.isDomestic;
            }
        }

        public FilterResult Filter(List<FlightCache> flights, IFlightResultContext context)
        {
            if (!_isFilterActive)
            {
                return new FilterResult { Flights = flights };
            }

            var flightsGroupedByValidityAndType = flights
                .ToLookup(IsDomestic)
                .ToLookup(g => _isGroupValid(g.Key));

            return new FilterResult
            {
                Flights = flightsGroupedByValidityAndType[true].SelectMany(g => g).ToList(),
                ErrorMessage = GetErrorMessage(flightsGroupedByValidityAndType[false])
            };
        }

        private (bool isDomestic, string countryCode) IsDomestic(FlightCache f)
        {
            var leg = f.Legs.First();
            return leg.DepartureAirportDetails.CountryCode == leg.ArrivalAirportDetails.CountryCode
                ? (isDomestic: true, countryCode: leg.DepartureAirportDetails.CountryCode)
                : (isDomestic: false, countryCode: null);
        }

        private string GetErrorMessage(
            IEnumerable<IGrouping<(bool isDomestic, string countryCode), FlightCache>> grouping)
        {
            return string.Join(", ", grouping.Select(g => g.Key.isDomestic
                ? $"International flights skipped: {g.Count()}"
                : $"Domestic [{g.Key.countryCode}] flights skipped: {g.Count()}"));
        }
    }
}