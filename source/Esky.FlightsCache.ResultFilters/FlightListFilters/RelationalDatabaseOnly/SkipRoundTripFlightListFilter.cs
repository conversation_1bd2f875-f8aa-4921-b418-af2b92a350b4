using Esky.FlightsCache.MessageContract;
using Esky.FlightsCache.Processing.Helpers;
using Esky.FlightsCache.ResultFilters.Contract;
using Microsoft.Extensions.Options;
using System.Collections.Generic;
using System.Linq;

namespace Esky.FlightsCache.ResultFilters.FlightListFilters.RelationalDatabaseOnly
{
    public class SkipRoundTripFlightListFilter : IFlightListFilter
    {
        private readonly bool _isSaveToRtEnabled;

        public SkipRoundTripFlightListFilter(IOptions<FlightsFilterConfiguration> configuration)
        {
            var config = configuration.Value;
            _isSaveToRtEnabled = config.IsRtSaveToSqlEnabled;
        }

        public FilterResult Filter(List<FlightCache> flights, IFlightResultContext context)
        {
            if (_isSaveToRtEnabled)
            {
                return new FilterResult { Flights = flights };
            }

            return new FilterResult { Flights = flights.Where(f => !f.IsRoundTrip()).ToList() };
        }
    }
}