using Esky.FlightsCache.MessageContract;
using Esky.FlightsCache.ResultFilters.Contract;
using System.Collections.Generic;
using System.Linq;

namespace Esky.FlightsCache.ResultFilters.FlightListFilters.RelationalDatabaseOnly
{
    public class SqlProviderCodeRangeFilter : IFlightListFilter
    {
        public FilterResult Filter(List<FlightCache> flights, IFlightResultContext context)
        {
            var filtered = flights.Where(f => f.ProviderCode <= 255).ToList();
            ;

            var skippedFlightCount = flights.Count - filtered.Count;

            return new FilterResult
            {
                Flights = filtered,
                StatisticsMessage = skippedFlightCount > 0
                    ? $"Skipped by ProviderCodeRange: {skippedFlightCount}"
                    : null
            };
        }
    }
}