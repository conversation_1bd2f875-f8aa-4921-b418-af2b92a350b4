using Esky.FlightsCache.ResultFilters.AirportCode;
using Esky.FlightsCache.ResultFilters.FlightListFilters.Global;
using Esky.FlightsCache.ResultFilters.FlightListFilters.RelationalDatabaseOnly;
using Esky.FlightsCache.ResultFilters.FlightListFilters.Utilities;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using CheapestFlightsByTimeSlotFlightListFilter =
    Esky.FlightsCache.ResultFilters.FlightListFilters.Global.CheapestFlightsByTimeSlotFlightListFilter;
using IFlightListFilter = Esky.FlightsCache.ResultFilters.FlightListFilters.Global.IFlightListFilter;

namespace Esky.FlightsCache.ResultFilters.FlightListFilters
{
    public static class ConfigurationExtensions
    {
        public static IServiceCollection ConfigureFlightsFilter(this IServiceCollection services,
            IConfiguration configuration)
        {
            services.AddSingleton<ITimeOfDayBucketizer, TimeOfDayBucketizer>();
            services.AddSingleton<IFlightListFilter, CheapestFlightsByTimeSlotFlightListFilter>();
            services.AddSingleton<RelationalDatabaseOnly.IFlightListFilter, SqlProviderCodeRangeFilter>();
            services.AddSingleton<RelationalDatabaseOnly.IFlightListFilter, SkipRoundTripFlightListFilter>();
            services
                .AddSingleton<RelationalDatabaseOnly.IFlightListFilter,
                    RelationalDatabaseOnly.CheapestFlightsByTimeSlotFlightListFilter>();
            services.AddSingleton<RelationalDatabaseOnly.IFlightListFilter, DomesticFlightsOnlyFilter>();

            services.AddSingleton<IFlightListFilter, AirportCodeFlightListFilter>();

            services.AddSingleton<IFlightListFilter, OpenJawFlightListFilter>();

            services.AddSingleton<IFlightListFilter, AirportCodeFilter>();
            services.AddSingleton<IFlightListFilter, OfficeIdFilter>();
            services.AddSingleton<IFlightListFilter, AvailableSeatsFilter>();
            services.AddSingleton<IFlightListFilter, UnknownSeatsFilter>();
            services.AddSingleton<IFlightListFilter, RyanairPaxConfigurationFilter>();
            services.AddSingleton<IFlightListFilter, AirlineFlightListFilter>();
            services.AddSingleton<IFlightListFilter, NoAdultPriceFilter>();
            services.AddSingleton<IFlightListFilter, SkipDefinedRoundTripFlightsFilter>();

            services.AddSingleton<IFlightListFilter, ProviderAirlineExcludeFlightListFilter>();
            services.AddSingleton<IAirportCodesIgnoreList, DefaultAirportCodesIgnoreList>();

            services.Configure<FlightsFilterConfiguration>(configuration.GetSection("FlightsFilter"));

            return services;
        }
    }
}