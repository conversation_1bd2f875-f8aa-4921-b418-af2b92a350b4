using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Esky.FlightsCache.ResultFilters.FlightListFilters.Utilities
{
    public class TimeOfDayBucketizer : ITimeOfDayBucketizer
    {
        private readonly List<TimeSpan> _timeSlots;

        public TimeOfDayBucketizer(IOptions<FlightsFilterConfiguration> configuration)
        {
            _timeSlots = (configuration.Value.TimeSlotRanges ?? string.Empty)
                .Split(new[] { ';' }, StringSplitOptions.RemoveEmptyEntries)
                .Concat(new[] { "1.00:00" }) // closing value "24:00"
                .Select(TimeSpan.Parse)
                .OrderBy(x => x)
                .ToList();
        }

        public int GetSlotNumber(DateTime dt)
        {
            return _timeSlots.FindIndex(x => dt.TimeOfDay < x);
        }
    }
}