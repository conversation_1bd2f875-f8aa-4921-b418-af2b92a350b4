using Esky.FlightsCache.MessageContract;
using Esky.FlightsCache.Processing.Helpers;
using Esky.FlightsCache.ResultFilters.Contract;
using Esky.FlightsCache.ResultFilters.ProviderAirlineExclude;
using Microsoft.Extensions.Caching.Memory;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Esky.FlightsCache.ResultFilters.FlightListFilters.Global
{
    public class ProviderAirlineExcludeFlightListFilter : IFlightListFilter
    {
        private const string CacheKey = "ProviderAirlineExcludeSettings";
        private const int CacheDurationInMinutes = 60;

        private static readonly SemaphoreSlim _semaphore = new(1, 1);

        private readonly IMemoryCache _cache;
        private readonly IProviderAirlineExcludeSettingsDatabase _db;
        private readonly Lazy<ProviderAirlineExcludeSettingsCollection> _excludeSettingsLazy;

        public ProviderAirlineExcludeFlightListFilter(
            IMemoryCache cache,
            IProviderAirlineExcludeSettingsDatabase db)
        {
            _cache = cache;
            _db = db;
            _excludeSettingsLazy =
                new Lazy<ProviderAirlineExcludeSettingsCollection>(GetProviderAirlineExcludeSettingsAsync().GetAwaiter()
                    .GetResult);
        }

        private ProviderAirlineExcludeSettingsCollection ExcludeSettings => _excludeSettingsLazy.Value;

        public Task<FilterResult> Filter(List<FlightCache> flights, IFlightResultContext context)
        {
            var messages = new List<string>();
            var filteredFlights = new List<FlightCache>();

            foreach (var flight in flights.Where(f => f.ProviderCode > 0))
            {
                if (flight.IsRoundTrip())
                {
                    HandleRtFlight(filteredFlights, messages, flight);
                }
                else
                {
                    HandleFlightWithSeparationOptions(filteredFlights, messages, flight);
                }
            }

            messages = messages
                .GroupBy(message => message)
                .Select(group => $"{group.Key}:{group.Count()}")
                .ToList();

            return Task.FromResult(new FilterResult
            {
                Flights = filteredFlights,
                StatisticsMessage = messages.Any()
                    ? string.Concat("excluded routes: ", string.Join(", ", messages))
                    : string.Empty
            });
        }

        private static string CreateMessage(FlightCache flight, FlightCacheLeg leg)
        {
            return
                $"{leg.DepartureCode}->{leg.ArrivalCode}(PC:{flight.ProviderCode} AC:{leg.AirlineCode}, RT:{(flight.IsRoundTrip() ? 1 : 0)})";
        }

        private async Task<ProviderAirlineExcludeSettingsCollection> GetProviderAirlineExcludeSettingsAsync()
        {
            if (!_cache.TryGetValue(CacheKey, out IEnumerable<ProviderAirlineExcludeSettings> excludeSettings))
            {
                await _semaphore.WaitAsync();
                try
                {
                    if (!_cache.TryGetValue(CacheKey, out excludeSettings))
                    {
                        var cacheEntryOptions = new MemoryCacheEntryOptions()
                            .SetAbsoluteExpiration(TimeSpan.FromMinutes(CacheDurationInMinutes));

                        excludeSettings = (await _db.GetProviderAirlineExcludeSettingsAsync())
                            .Select(settings =>
                                new ProviderAirlineExcludeSettings
                                {
                                    ProviderCode = settings.ProviderCode,
                                    AirlineCode = settings.AirlineCode
                                })
                            .ToArray();

                        _cache.Set(CacheKey, excludeSettings, cacheEntryOptions);
                    }
                }
                finally
                {
                    _semaphore.Release();
                }
            }

            return new ProviderAirlineExcludeSettingsCollection(excludeSettings);
        }

        private void HandleRtFlight(ICollection<FlightCache> flights, ICollection<string> errorMessages,
            FlightCache flight)
        {
            var firstLeg = flight.Legs.First();
            if (ExcludeSettings.HasExcludeRuleFor(flight.ProviderCode, firstLeg.AirlineCode))
            {
                errorMessages.Add(CreateMessage(flight, firstLeg));
                return;
            }

            flights.Add(flight);
        }

        private void HandleFlightWithSeparationOptions(ICollection<FlightCache> flights,
            ICollection<string> errorMessages, FlightCache flight)
        {
            var filteredLegs = new List<FlightCacheLeg>();

            foreach (var leg in flight.Legs)
            {
                if (ExcludeSettings.HasExcludeRuleFor(flight.ProviderCode, leg.AirlineCode))
                {
                    errorMessages.Add(CreateMessage(flight, leg));
                    continue;
                }

                filteredLegs.Add(leg);
            }

            if (filteredLegs.Count == flight.Legs.Count)
            {
                flights.Add(flight);
            }
            else if (filteredLegs.Count > 0)
            {
                flights.Add(CreateFlight(flight, filteredLegs));
            }
        }

        private FlightCache CreateFlight(FlightCache flight, List<FlightCacheLeg> legs)
        {
            return new FlightCache
            {
                ExpirationDate = flight.ExpirationDate,
                Legs = legs,
                LegsCanBeUseSeparately = flight.LegsCanBeUseSeparately,
                ProviderCode = flight.ProviderCode,
                SearchDate = flight.SearchDate,
                SearchId = flight.SearchId,
                SessionId = flight.SessionId,
                Supplier = flight.Supplier,
            };
        }
    }
}