using Esky.FlightsCache.MessageContract;
using Esky.FlightsCache.ProviderMapping;
using Esky.FlightsCache.ResultFilters.Contract;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace Esky.FlightsCache.ResultFilters.FlightListFilters.Global
{
    /// <summary>
    ///     Traveling with children on Ryanair increases price of the first adult price by obligatory seats selection (4-6 EUR)
    ///     It is hard to find regular price. Child price sometimes is decreased (departure from UK market, -12 GBP) and
    ///     none of prices correspond to regular adult price.
    ///     On flights to/from Italy no charge for Adult for obligatory seat selection when traveling with child.
    /// </summary>
    public partial class RyanairPaxConfigurationFilter : IFlightListFilter
    {
        public Task<FilterResult> Filter(List<FlightCache> flights, IFlightResultContext context)
        {
            if (IsSearchResultWithChild(context) || IsSearchForTooManyPaxes(context))
            {
                flights = flights.Where(f => f.ProviderCode != KnownCacheProviderCodes.Ryanair).ToList();
            }

            return Task.FromResult(new FilterResult { Flights = flights });
        }

        [GeneratedRegex(@"^\d\.\d\.[1-9]\.\d$")]
        private static partial Regex PaxConfigurationWithChildRegex();

        private static bool IsSearchResultWithChild(IFlightResultContext context)
        {
            return PaxConfigurationWithChildRegex().IsMatch(context.PaxConfiguration);
        }

        private static bool IsSearchForTooManyPaxes(IFlightResultContext context)
        {
            try
            {
                var paxConfiguration = context.PaxConfiguration;
                var adultPrice = int.Parse(paxConfiguration[..1]);
                var youthPrice = int.Parse(paxConfiguration[2..3]);
                return adultPrice + youthPrice > 2;
            }
            catch
            {
                return true;
            }
        }
    }
}