using Esky.FlightsCache.MessageContract;
using Esky.FlightsCache.ResultFilters.Contract;
using Esky.FlightsCache.ResultFilters.FlightListFilters.Utilities;
using Microsoft.Extensions.Options;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Esky.FlightsCache.ResultFilters.FlightListFilters.Global
{
    public class
        CheapestFlightsByTimeSlotFlightListFilter : RelationalDatabaseOnly.CheapestFlightsByTimeSlotFlightListFilter,
            IFlightListFilter
    {
        private readonly bool _include;
        private readonly int[] _providersToLimitGlobally;

        public CheapestFlightsByTimeSlotFlightListFilter(ITimeOfDayBucketizer timeOfDayBucketizer,
            IOptions<FlightsFilterConfiguration> config)
            : base(timeOfDayBucketizer, config)
        {
            _providersToLimitGlobally = config.Value.ProvidersToFilterGlobally?.ProviderCodes ?? new int[0];
            _include = config.Value.ProvidersToFilterGlobally?.Include ?? false;
        }

        Task<FilterResult> IFlightListFilter.Filter(List<FlightCache> flights, IFlightResultContext context)
        {
            return Task.FromResult(Filter(flights, context));
        }

        protected override bool ShouldSkipFilter(FlightCache f)
        {
            return _providersToLimitGlobally.Contains(f.ProviderCode) != _include || base.ShouldSkipFilter(f);
        }
    }
}