using Esky.FlightsCache.MessageContract;
using Esky.FlightsCache.Processing.Helpers;
using Esky.FlightsCache.ResultFilters.Contract;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Esky.FlightsCache.ResultFilters.FlightListFilters.Global
{
    public class OpenJawFlightListFilter : IFlightListFilter
    {
        public Task<FilterResult> Filter(List<FlightCache> flights, IFlightResultContext context)
        {
            var messages = new List<string>();
            var filteredFlights = new List<FlightCache>();

            foreach (var flight in flights)
            {
                if (flight.IsOpenJaw())
                {
                    messages.Add(
                        $"{flight.Legs[0].DepartureCode}->{flight.Legs[0].ArrivalCode}, {flight.Legs[1].DepartureCode}->{flight.Legs[1].ArrivalCode} open jaw flight not supported");
                    continue;
                }

                filteredFlights.Add(flight);
            }

            return Task.FromResult(new FilterResult
            {
                Flights = filteredFlights,
                StatisticsMessage = messages.Any() ? string.Join(", ", messages.Distinct()) : string.Empty
            });
        }
    }
}