using Esky.FlightsCache.MessageContract;
using Esky.FlightsCache.Processing.Helpers;
using Esky.FlightsCache.ResultFilters.AirportCode;
using Esky.FlightsCache.ResultFilters.Contract;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Esky.FlightsCache.ResultFilters.FlightListFilters.Global
{
    public class AirportCodeFlightListFilter : IFlightListFilter
    {
        private readonly IAirportCodesIgnoreList _airportCodesIgnoreList;
        private readonly IAirportsRepository _airportsRepository;

        public AirportCodeFlightListFilter(IAirportCodesIgnoreList airportCodesIgnoreList,
            IAirportsRepository airportsRepository)
        {
            _airportCodesIgnoreList = airportCodesIgnoreList;
            _airportsRepository = airportsRepository;
        }

        public async Task<FilterResult> Filter(List<FlightCache> flights, IFlightResultContext context)
        {
            var airports = await _airportsRepository.GetAirportsAsync();
            var messages = new List<string>();

            flights = RemoveFlightsWhereNotSupportedCode(flights, context.DepartureCode, l => l.DepartureCode,
                l => l.ArrivalCode, messages, airports);
            flights = RemoveFlightsWhereNotSupportedCode(flights, context.ArrivalCode, l => l.ArrivalCode,
                l => l.DepartureCode, messages, airports);

            return new FilterResult { Flights = flights, StatisticsMessage = string.Join(", ", messages) };
        }

        private List<FlightCache> RemoveFlightsWhereNotSupportedCode(
            List<FlightCache> flights,
            string multiportCode,
            Func<FlightCacheLeg, string> airportCodeSelector,
            Func<FlightCacheLeg, string> returnAirportCodeSelector,
            List<string> messages,
            AirportCollection airports)
        {
            if (!airports.ContainsMultiport(multiportCode))
            {
                messages.Add($"{multiportCode} not supported");
                return new List<FlightCache>();
            }

            bool IsAirportCodeSupported(string airportCode)
            {
                return airports.ContainsAirport(multiportCode, airportCode.GetAirportHashCode());
            }

            bool CanBeUsedAsReturnFlight(FlightCache f)
            {
                return f.Legs.First().SeparationOptions.Options
                    .HasFlag(SeparationOptionEnum.RoundTripInbound);
            }

            bool ValidateFlight(FlightCache f)
            {
                return IsAirportCodeSupported(airportCodeSelector(f.Legs.First()))
                       || (CanBeUsedAsReturnFlight(f) &&
                           IsAirportCodeSupported(returnAirportCodeSelector(f.Legs.First())));
            }

            var validatedFlightsLookup = flights.ToLookup(ValidateFlight);

            var toRemove = validatedFlightsLookup[false];

            if (toRemove.Any())
            {
                _airportCodesIgnoreList.Init();
                var stats = toRemove
                    .GroupBy(f => airportCodeSelector(f.Legs.First()))
                    .Where(g => !_airportCodesIgnoreList.CanIgnoreLoggingForNotSupportedCode(multiportCode, g.Key))
                    .Select(g => $"{g.Key}:{g.Count()}")
                    .ToArray();
                if (stats.Any())
                {
                    messages.Add($"[{multiportCode}] skipped ({string.Join(", ", stats)})");
                }
            }

            return validatedFlightsLookup[true].ToList();
        }
    }
}