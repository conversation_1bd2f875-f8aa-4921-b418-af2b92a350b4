using Esky.FlightsCache.MessageContract;
using Esky.FlightsCache.ResultFilters.Contract;
using Microsoft.Extensions.Options;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Esky.FlightsCache.ResultFilters.FlightListFilters.Global
{
    public class AvailableSeatsFilter : IFlightListFilter
    {
        private readonly HashSet<int> _singleSeatAllowedProviders;
        public AvailableSeatsFilter(IOptions<FlightsFilterConfiguration> config)
        {
            _singleSeatAllowedProviders = [..config.Value.ProvidersWithAllowedSingleSeatsStoring ?? Enumerable.Empty<int>()];
        }
        
        public Task<FilterResult> Filter(List<FlightCache> flights, IFlightResultContext context)
        {
            if(context.PaxConfiguration != "1.0.0.0" || context.IsFromRobots)
            {
                return Task.FromResult(new FilterResult { Flights = flights });
            }
            
            var providersToExclude = flights
                .Where(f =>  
                    !_singleSeatAllowedProviders.Contains(f.ProviderCode)
                    && f.Legs.Any(l => l.AvailableSeatsCount == 1))
                .Select(f => f.ProviderCode)
                .ToHashSet();

            var result = providersToExclude.Any()
                ? new FilterResult
                {
                    Flights = flights.Where(f => !providersToExclude.Contains(f.ProviderCode)).ToList(),
                    StatisticsMessage = string.Concat("1pax results filtered out: ",
                        string.Join(", ", providersToExclude))
                }
                : new FilterResult { Flights = flights };

            return Task.FromResult(result);
        }
    }
}