using Esky.FlightsCache.MessageContract;
using Esky.FlightsCache.ResultFilters.Contract;
using Microsoft.Extensions.Options;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Esky.FlightsCache.ResultFilters.FlightListFilters.Global
{
    public class UnknownSeatsFilter : IFlightListFilter
    {
        private readonly HashSet<string> _unknownSeatsDisallowedAirlines;
        public UnknownSeatsFilter(IOptions<FlightsFilterConfiguration> config)
        {
            _unknownSeatsDisallowedAirlines = config.Value.AirlinesWithDisallowedUnknownSeatsStoring?.ToHashSet() ?? [];
        }
        
        public Task<FilterResult> Filter(List<FlightCache> flights, IFlightResultContext context)
        {
            if(context.PaxConfiguration != "1.0.0.0" || context.IsFromRobots)
            {
                return Task.FromResult(new FilterResult { Flights = flights });
            }

            var providersToExcludeWithUnknownSeats =
                flights
                    .Where(f =>  
                        _unknownSeatsDisallowedAirlines.Overlaps(f.Legs.Select(l => l.AirlineCode))
                        && f.Legs.All(l => l.AvailableSeatsCount is null))
                    .Select(f => (f.ProviderCode, f.Supplier))
                    .ToHashSet();

            var result = providersToExcludeWithUnknownSeats.Any()
                ? new FilterResult
                {
                    Flights = flights.Where(f => !providersToExcludeWithUnknownSeats.Contains((f.ProviderCode, f.Supplier))).ToList(),
                    StatisticsMessage = string.Concat("Unknown seats results filtered out: ",
                        string.Join(", ", providersToExcludeWithUnknownSeats))
                }
                : new FilterResult { Flights = flights };

            return Task.FromResult(result);
        }
    }
}