using Esky.FlightsCache.MessageContract;
using Esky.FlightsCache.ObjectDirectory;
using Esky.FlightsCache.ResultFilters.Contract;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Esky.FlightsCache.ResultFilters.FlightListFilters.Global
{
    public class AirlineFlightListFilter : IFlightListFilter
    {
        private readonly IObjectDirectoryService _objectDirectoryService;

        public AirlineFlightListFilter(IObjectDirectoryService objectDirectoryService)
        {
            _objectDirectoryService = objectDirectoryService;
        }

        public Task<FilterResult> Filter(List<FlightCache> flights, IFlightResultContext context)
        {
            var errorMessages = new List<string>();

            var airlines = flights
                .SelectMany(f => f.Legs.Select(x => x.AirlineCode))
                .Distinct();

            var airlinesSupported = new HashSet<string>();

            foreach (var airline in airlines)
            {
                if (airline is null)
                {
                    errorMessages.Add("Null airline filtered out from search results");
                    continue;
                }

                if (_objectDirectoryService.IsAirline(airline))
                {
                    airlinesSupported.Add(airline);
                }
                else
                {
                    errorMessages.Add($"Not supported airline {airline} filtered out from search results");
                }
            }

            return Task.FromResult(new FilterResult
            {
                Flights =
                    flights.Where(f => f.Legs.Select(l => l.AirlineCode).All(a => airlinesSupported.Contains(a)))
                        .ToList(),
                ErrorMessage = errorMessages.Any() ? string.Join(", ", errorMessages) : string.Empty
            });
        }
    }
}