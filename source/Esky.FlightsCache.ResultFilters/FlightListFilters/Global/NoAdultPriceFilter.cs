using Esky.FlightsCache.MessageContract;
using Esky.FlightsCache.ResultFilters.Contract;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Esky.FlightsCache.ResultFilters.FlightListFilters.Global
{
    public class NoAdultPriceFilter : IFlightListFilter
    {
        public Task<FilterResult> Filter(List<FlightCache> flights, IFlightResultContext context)
        {
            var flightsWithZeroPrices = flights
                .Where(f => !ContainsAdultPrices(f))
                .ToArray();

            if (flightsWithZeroPrices.Length == 0)
            {
                return Task.FromResult(new FilterResult { Flights = flights });
            }

            var zeroPricesFlightsDescription =
                String.Join(", ", flightsWithZeroPrices.Select(GetSingleFlightDescription));
            var result = new FilterResult
            {
                Flights = flights.Except(flightsWithZeroPrices).ToList(),
                StatisticsMessage =
                    $"flights without adult prices filtered ({flightsWithZeroPrices.Length} flights): {zeroPricesFlightsDescription}"
            };

            return Task.FromResult(result);

            static bool ContainsAdultPrices(FlightCache f) =>
                f.Legs.Exists(l => l.AdultPrices?.Exists(p => p.BasePrice + p.TaxPrice > 0) ?? false);

            static string GetSingleFlightDescription(FlightCache f)
            {
                var legs = String.Join("|", f.Legs.Select(GetLegDescription));
                return $"{f.ProviderCode}|{legs}";
                
                string GetLegDescription(FlightCacheLeg l) =>
                    $"{l.DepartureCode}-{l.ArrivalCode}{l.DepartureDate:yyMMdd}";
            }
        }
    }
}