using Esky.FlightsCache.MessageContract;
using Esky.FlightsCache.ObjectDirectory;
using Esky.FlightsCache.ResultFilters.Contract;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Esky.FlightsCache.ResultFilters.FlightListFilters.Global
{
    public class AirportCodeFilter : IFlightListFilter
    {
        private readonly IObjectDirectoryService _odService;

        public AirportCodeFilter(IObjectDirectoryService odService)
        {
            _odService = odService;
        }

        public Task<FilterResult> Filter(List<FlightCache> flights, IFlightResultContext context)
        {
            var invalidAirports = new List<string>();
            var errorMessages = new List<string>();

            bool ValidateFlight(FlightCache f)
            {
                return f.Legs.SelectMany(leg =>
                        leg.Segments.Select(x => x.DepartureCode).Concat(leg.Segments.Select(x => x.ArrivalCode)))
                    .Distinct()
                    .All(airportCode =>
                    {
                        if (!_odService.IsAirport(airportCode))
                        {
                            invalidAirports.Add(airportCode);
                            return false;
                        }

                        return true;
                    });
            }

            var validatedFlightsLookup = flights.ToLookup(ValidateFlight);

            if (validatedFlightsLookup[false].Any())
            {
                errorMessages.Add(
                    $"[{context.DepartureCode}-{context.ArrivalCode}] has invalid airports: {string.Join(", ", invalidAirports.Distinct())}");
            }

            return Task.FromResult(new FilterResult
            {
                Flights = validatedFlightsLookup[true].ToList(),
                ErrorMessage = errorMessages.Any() ? string.Join(", ", errorMessages) : string.Empty
            });
        }
    }
}