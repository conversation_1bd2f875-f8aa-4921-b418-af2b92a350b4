using Esky.FlightsCache.MessageContract;
using Esky.FlightsCache.Processing.Helpers;
using Esky.FlightsCache.ResultFilters.Contract;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;


namespace Esky.FlightsCache.ResultFilters.FlightListFilters.Global
{
    public class SkipDefinedRoundTripFlightsFilter : IFlightListFilter
    {
        private readonly IRoundTripMergeOptions _roundTripMergeOptions;
        public SkipDefinedRoundTripFlightsFilter(IRoundTripMergeOptions roundTripMergeOptions)
        {
            _roundTripMergeOptions = roundTripMergeOptions;
        }
        
        public Task<FilterResult> Filter(List<FlightCache> flights, IFlightResultContext context)
        {
            var flightsToExclude = flights
                .Where(f =>
                    f.IsRoundTrip()
                    && f.Legs.TrueForAll(l => 
                        _roundTripMergeOptions.CanMergeIntoRoundTrip(f.ReadProviderCode, f.Supplier)))
                .ToArray();
            var exclusions = flightsToExclude
                .Select(f => $"{f.ReadProviderCode} {f.Supplier}")
                .Distinct();

            var result = flightsToExclude.Length != 0
                ? new FilterResult
                {
                    Flights = flights.Except(flightsToExclude).ToList(),
                    StatisticsMessage = $"RT flights filtered out : {flightsToExclude.Length}, (readProviderCode + supplier): " +
                                        $"{string.Join(", ", exclusions)}"
                }
                : new FilterResult { Flights = flights };

            return Task.FromResult(result);
        }
    }
}