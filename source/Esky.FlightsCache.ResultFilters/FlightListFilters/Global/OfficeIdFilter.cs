using Esky.FlightsCache.MessageContract;
using Esky.FlightsCache.PartnerSettings;
using Esky.FlightsCache.ResultFilters.Contract;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace Esky.FlightsCache.ResultFilters.FlightListFilters.Global
{
    public class OfficeIdFilter : IFlightListFilter
    {
        private const string MasterPartnerCode = "MASTER";
        private readonly IPartnerSettingsService _partnerSettingsService;

        public OfficeIdFilter(IPartnerSettingsService partnerSettingsService)
        {
            _partnerSettingsService = partnerSettingsService;
        }

        public async Task<FilterResult> Filter(List<FlightCache> flights, IFlightResultContext context)
        {
            var partnerSettings = await _partnerSettingsService.GetPartnerSettingsAsync(MasterPartnerCode);
            var pattern = partnerSettings.SpecialOccasionsSettings.DisallowedOfficeIdRegexPattern;

            if (string.IsNullOrEmpty(pattern))
            {
                return new FilterResult { Flights = flights, StatisticsMessage = string.Empty };
            }

            var filter = (
                from flight in flights
                let officeId = flight.Legs[0].Segments[0].GetOfficeId()
                select new
                {
                    ToBeExcluded = !string.IsNullOrWhiteSpace(officeId) && Regex.IsMatch(officeId, pattern),
                    OfficeId = officeId,
                    Flight = flight
                }
                )
                .ToLookup(x => x.ToBeExcluded);

            return new FilterResult
            {
                Flights = filter[false].Select(x => x.Flight).ToList(),
                StatisticsMessage = filter[true].Any()
                    ? string.Concat("excluded office ids: ",
                        string.Join(", ", filter[true].Select(x => x.OfficeId).Distinct()))
                    : string.Empty
            };
        }
    }
}