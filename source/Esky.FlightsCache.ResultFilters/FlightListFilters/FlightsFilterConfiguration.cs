namespace Esky.FlightsCache.ResultFilters.FlightListFilters;

public class FlightsFilterConfiguration
{
    /// <summary>
    ///     Number of cheapest flights to save in cache from single chunk
    /// </summary>
    public int FlightsPerChunk { get; set; }

    /// <summary>
    ///     Time slot ranges flight for departure time grouping
    /// </summary>
    public string TimeSlotRanges { get; set; }

    public ProvidersToFilter ProvidersToFilterGlobally { get; set; }

    public DomesticOnlyFilterConfiguration DomesticOnlyFilter { get; set; }

    public bool IsRtSaveToSqlEnabled { get; set; }
    
    public int[] ProvidersWithAllowedSingleSeatsStoring { get; set; }
    public string[] AirlinesWithDisallowedUnknownSeatsStoring { get; set; }

    public class ProvidersToFilter
    {
        public int[] ProviderCodes { get; set; }
        public bool Include { get; set; }
    }

    public class DomesticOnlyFilterConfiguration
    {
        public string[] Countries { get; set; }
        public bool ExcludeInternational { get; set; }
    }
}