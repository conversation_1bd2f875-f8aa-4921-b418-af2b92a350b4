using Esky.FlightsCache.MessageContract;
using Esky.FlightsCache.Processing.Helpers;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Esky.FlightsCache.ResultFilters.AirportCode
{
    public class AirportCollection
    {
        private readonly Dictionary<string, Airport> _airports;

        private readonly Lazy<Dictionary<string, short[]>>
            _destinationCodeToHashCodes; // WAWA -> (WAW),(WMI) // WMI -> (WMI)

        private readonly Lazy<Dictionary<short, Airport>> _hashCodeToAirport;
        private readonly Lazy<Dictionary<short, string[]>> _hashCodeToMultiportCodes; // (WMI)-> WMI, WAWA

        public AirportCollection(IEnumerable<Airport> airports)
        {
            _airports = airports.ToDictionary(x => x.Code);

            _destinationCodeToHashCodes = new Lazy<Dictionary<string, short[]>>(
                BuildAirportCodes()
                    .GroupBy(e => e.Code)
                    .ToDictionary(e => e.Key, e => e.Select(a => a.HashCode).ToArray())
            );

            _hashCodeToMultiportCodes = new Lazy<Dictionary<short, string[]>>(
                BuildAirportCodes()
                    .Where(IsNotNearbyGroup)
                    .GroupBy(e => e.HashCode)
                    .ToDictionary(e => e.Key, e => e.Select(a => a.Code).ToArray())
            );

            _hashCodeToAirport = new Lazy<Dictionary<short, Airport>>(_airports.Values.ToDictionary(x => x.HashCode));
        }

        public Airport this[string key] => _airports[key];

        public bool ContainsMultiport(string multiportCode)
        {
            return _destinationCodeToHashCodes.Value.ContainsKey(multiportCode);
        }

        public bool ContainsAirport(short hashCode)
        {
            return _hashCodeToAirport.Value.ContainsKey(hashCode);
        }

        public bool ContainsAirport(string multiportCode, short airportHashCode)
        {
            return _destinationCodeToHashCodes.Value.TryGetValue(multiportCode, out var codes) &&
                   codes.Contains(airportHashCode);
        }

        public short[] GetSupportedCodes(string multiportCode)
        {
            return _destinationCodeToHashCodes.Value.TryGetValue(multiportCode, out var codes)
                ? codes
                : [];
        }

        public string[] GetSearchCodes(short hashCode)
        {
            return _hashCodeToMultiportCodes.Value.TryGetValue(hashCode, out var codes)
                ? codes
                : [];
        }

        public string[] GetAirports(string code)
        {
            var codes = _destinationCodeToHashCodes.Value.GetValueOrDefault(code)?.Select(e => e.GetAirportCode()).ToArray();
            return codes ?? [];
        }

        public Airport GetAirport(short hashCode)
        {
            return _hashCodeToAirport.Value.TryGetValue(hashCode, out var airport)
                ? airport
                : Airport.Empty;
        }

        public Airport GetAirport(string code) => _airports.GetValueOrDefault(code);

        private IEnumerable<(string Code, short HashCode)> BuildAirportCodes()
        {
            foreach (var airport in _airports.Values)
            {
                var hashCode = airport.HashCode;

                yield return (airport.Code, hashCode);
                yield return (airport.Code + "*", hashCode);

                if (!string.IsNullOrWhiteSpace(airport.Multiport) && airport.Code != airport.Multiport)
                {
                    yield return (airport.Multiport, hashCode);
                    yield return (airport.Multiport + "*", hashCode);
                }

                foreach (var nearbyAirport in airport.NearbyAirports)
                {
                    yield return (nearbyAirport + "*", hashCode);
                }
            }
        }

        private static bool IsNotNearbyGroup((string Code, short Hash) tuple)
        {
            return !tuple.Code.EndsWith('*');
        }

        public record Airport
        {
            public static readonly Airport Empty = new()
            {
                Code = string.Empty,
                Multiport = string.Empty,
                TimeZoneId = string.Empty,
                NearbyAirports = [],
                CityCode = string.Empty,
                CountryCode = string.Empty,
                ContinentCode = string.Empty
            };

            private short? _hashCode;

            public short HashCode
            {
                get
                {
                    _hashCode ??= string.IsNullOrWhiteSpace(Code) ? (short?)0 : Code.GetAirportHashCode();
                    return _hashCode.Value;
                }
            }

            public bool IsEmpty => this == Empty || string.IsNullOrWhiteSpace(Code);
            public required string Code { get; init; }
            public required string Multiport { get; init; }
            public required IEnumerable<string> NearbyAirports { get; init; }
            public required string CityCode { get; init; }
            public required string CountryCode { get; init; }
            public required string ContinentCode { get; init; }
            public string TimeZoneId { get; init; }
        }
    }

    public static class AirportCollectionExtensions
    {
        public static AirportDetails GetAirportDetails(this AirportCollection airports, short hashCode)
        {
            return airports.GetAirport(hashCode).ToDetails();
        }

        public static AirportDetails ToDetails(this AirportCollection.Airport airport)
        {
            var a = airport ?? AirportCollection.Airport.Empty;

            return new AirportDetails
            {
                ContinentCode = a.ContinentCode,
                CountryCode = a.CountryCode,
                HashCode = a.HashCode,
                CityCode = a.CityCode,
                Multiport = a.Multiport,
                TimeZoneId = a.TimeZoneId
            };
        }
    }
}