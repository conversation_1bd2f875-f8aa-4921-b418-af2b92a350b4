using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;

namespace Esky.FlightsCache.ResultFilters.ProviderAirlineExclude
{
    public class ProviderAirlineExcludeSettingsCollection : IReadOnlyCollection<ProviderAirlineExcludeSettings>
    {
        private readonly IList<ProviderAirlineExcludeSettings> _excludeSettings;

        public ProviderAirlineExcludeSettingsCollection(IEnumerable<ProviderAirlineExcludeSettings> excludeSettings)
        {
            _excludeSettings = excludeSettings?.ToList() ?? [];
        }

        public int Count => _excludeSettings.Count;

        public IEnumerator<ProviderAirlineExcludeSettings> GetEnumerator()
        {
            return _excludeSettings.GetEnumerator();
        }

        IEnumerator IEnumerable.GetEnumerator()
        {
            return GetEnumerator();
        }

        public bool HasExcludeRuleFor(int providerCode, string airlineCode)
        {
            return _excludeSettings.Any(settings =>
                settings.ProviderCode == providerCode && string.Equals(settings.AirlineCode, airlineCode,
                    StringComparison.OrdinalIgnoreCase));
        }
    }
}