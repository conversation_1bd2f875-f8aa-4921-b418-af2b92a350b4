using System.Collections.Generic;
using System.Linq;

namespace Esky.FlightsCache.ProviderMapping;

public interface IMarginConfigurationService
{
    void UpdateMargins(List<ProviderMarginConfiguration> providerMarginConfigurations);
    MarginConfiguration GetMarginConfiguration(int providerCode, string? supplier, string[] airlineCodes);
}

public class MarginConfigurationService: IMarginConfigurationService
{
    private Dictionary<int, Dictionary<string, (HashSet<string>, MarginConfiguration Margin)[]>> _configs;
    
    public void UpdateMargins(List<ProviderMarginConfiguration> providerMarginConfigurations)
    {
        _configs = providerMarginConfigurations
            .ToDictionary(
                c => c.ProviderCode,
                c => c.Margins
                    .GroupBy(m => m.Supplier, m => (new HashSet<string>(m.AirlineCodes), m.Margin))
                    .ToDictionary(
                        m => m.Key,
                        m => m.To<PERSON>rray()));
    }

    public MarginConfiguration GetMarginConfiguration(int providerCode, string? supplier, string[] airlineCodes)
    {
        if (!_configs.TryGetValue(providerCode, out var config))
        {
            return MarginConfiguration.ZeroMargin;
        }

        if (supplier != null && config.TryGetValue(supplier, out var value) && 
            TryGetAirlinesMargin(value, out var margin))
        {
            return margin;
        }
        
        if (config.TryGetValue("*", out value) && 
            TryGetAirlinesMargin(value, out margin))
        {
            return margin;
        }

        return MarginConfiguration.ZeroMargin;

        bool TryGetAirlinesMargin((HashSet<string>, MarginConfiguration Margin)[] airlinesMargin, out MarginConfiguration allAirlinesMargin1)
        {
            MarginConfiguration allAirlinesMargin = null;
            foreach (var(airlines, margin) in airlinesMargin)
            {
                if (airlines.IsSupersetOf(airlineCodes))
                {
                    allAirlinesMargin1 = margin;
                    return true;
                }

                if (airlines.Contains("*"))
                {
                    allAirlinesMargin = margin;
                }
            }

            if (allAirlinesMargin != null)
            {
                allAirlinesMargin1 = allAirlinesMargin;
                return true;
            }
            
            allAirlinesMargin1 = null;
            return false;
        }
    }
}