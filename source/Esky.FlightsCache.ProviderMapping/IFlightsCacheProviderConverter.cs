using System.Collections.Generic;

namespace Esky.FlightsCache.ProviderMapping
{
    public interface IFlightsCacheProviderConverter
    {
        FlightCacheWriteConfiguration GetWriteConfiguration(int providerCode, string[] airlineCodes);
        FlightCacheWriteConfiguration GetWriteConfiguration(int providerCode, string supplier);
        List<int> GetCacheProviderCodes(int providerCode, string supplier);
        List<int> ToFCProviderCodes(List<int> providerCodes, List<string> airlineCodes);

        void UpdateInternalMapping(List<CacheProviderConfiguration> providerConfigurations);
    }
}