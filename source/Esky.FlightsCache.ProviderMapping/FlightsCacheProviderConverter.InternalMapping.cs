using System.Collections.Generic;
using System.Linq;

namespace Esky.FlightsCache.ProviderMapping
{
    public partial class FlightsCacheProviderConverter
    {
        private class InternalMapping
        {
            public InternalMapping(List<CacheProviderConfiguration> configs)
            {
                FCPConfigurations = configs;
                ProviderToAirlinesConfig = BuildProviderAirlineConfig(configs);
                FcProviderToIbeProvider = configs.ToDictionary(x => x.CacheProviderCode, x => x.ReadProviderCode);
            }

            public List<CacheProviderConfiguration> FCPConfigurations { get; }
            public Dictionary<int, List<(HashSet<string> Airlines, FlightCacheWriteConfiguration Config)>> ProviderToAirlinesConfig { get; }
            public Dictionary<int, int> FcProviderToIbeProvider { get; }

            private static Dictionary<int, List<(HashSet<string> Airlines, FlightCacheWriteConfiguration Config)>> BuildProviderAirlineConfig(List<CacheProviderConfiguration> configs)
            {
                var result = new Dictionary<int, List<(HashSet<string> Airlines, FlightCacheWriteConfiguration Config)>>();

                foreach (var cacheProviderConfig in configs)
                {
                    foreach (var writeConfig in cacheProviderConfig.WriteConfigurations)
                    {
                        if (!result.TryGetValue(writeConfig.ProviderCode, out var airlinesConfig))
                        {
                            airlinesConfig = new List<(HashSet<string>, FlightCacheWriteConfiguration)>();
                            result.Add(writeConfig.ProviderCode, airlinesConfig);
                        }

                        airlinesConfig.Add(
                            (
                                new HashSet<string>(writeConfig.AirlineCodes),
                                new FlightCacheWriteConfiguration
                                {
                                    CacheProviderCode = cacheProviderConfig.CacheProviderCode,
                                    ReadProviderCode = cacheProviderConfig.ReadProviderCode,
                                    Supplier = writeConfig.Supplier
                                }
                            )
                        );
                    }
                }

                return result;
            }
        }
    }
}