using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace Esky.FlightsCache.ProviderMapping
{
    public class TechnicalMarginConfiguration
    {
        public string Id { get; set; }
        public int? ProviderCode { get; set; } = null; // null = all providers
        public string Supplier { get; set; }
        public string[] AirlineCodes { get; set; } = {};
        public int FromNumberOfPassengers { get; set; } = 1;
        [BsonRepresentation(BsonType.String)]
        public PassengerType Passenger { get; set; } = PassengerType.All;

        [BsonRepresentation(BsonType.String)]
        public MarginContext Context { get; set; } // Base or Packages
        public MarginConfiguration Margin { get; set; }
    }

    public enum PassengerType
    {
        All,
        Adult,
        Youth,
        Child,
        Infant,
    }

    public enum MarginContext
    {
        Base = 1,
        Packages = 2
    }
}