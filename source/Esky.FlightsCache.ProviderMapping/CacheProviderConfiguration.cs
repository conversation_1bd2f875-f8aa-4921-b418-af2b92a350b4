using MongoDB.Bson.Serialization.Attributes;

namespace Esky.FlightsCache.ProviderMapping
{
    public class CacheProviderConfiguration
    {
        [BsonId] public int CacheProviderCode { get; set; }

        public int ReadProviderCode { get; set; }

        public WriteConfiguration[] WriteConfigurations { get; set; }

        public class WriteConfiguration
        {
            public int ProviderCode { get; set; }
            public string[] AirlineCodes { get; set; }
            public string Supplier { get; set; }
            public MarginConfiguration Margin { get; set; }
        }
    }
}