using System.Collections.Generic;
using System.Linq;

namespace Esky.FlightsCache.ProviderMapping;

public interface ITechnicalMarginService
{
    void UpdateMargins(List<TechnicalMarginConfiguration> marginConfigurations);

    TechnicalMarginConfiguration[] GetTechnicalMargins(int providerCode, string? supplier, HashSet<string> airlines,
        int? totalSeatsLeft, PassengerType passengerType);
}

//todo mb: rename to *Storage?
public class TechnicalMarginService: ITechnicalMarginService
{
    private TechnicalMarginConfiguration[] _configs;
    
    public void UpdateMargins(List<TechnicalMarginConfiguration> marginConfigurations)
    {
        _configs = marginConfigurations.ToArray();
    }

    public TechnicalMarginConfiguration[] GetTechnicalMargins(int providerCode, string? supplier, HashSet<string> airlines, int? totalSeatsLeft, PassengerType passengerType)
    {
        var margins = _configs
            .Where(x => x.ProviderCode == null || x.ProviderCode == providerCode)
            .Where(x => x.Supplier == "*" || x.Supplier == supplier)
            .Where(x => airlines.IsSupersetOf(x.AirlineCodes) || x.AirlineCodes.Contains("*"))
            .Where(x => totalSeatsLeft == null || x.FromNumberOfPassengers <= totalSeatsLeft)
            .Where(x => x.Passenger == passengerType || x.Passenger == PassengerType.All)
            .ToArray();

        return margins;
    }
}