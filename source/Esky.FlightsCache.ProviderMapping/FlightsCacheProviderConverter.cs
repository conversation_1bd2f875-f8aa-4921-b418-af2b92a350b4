using System.Collections.Generic;
using System.Linq;

namespace Esky.FlightsCache.ProviderMapping
{
    public partial class FlightsCacheProviderConverter : IFlightsCacheProviderConverter
    {
        private InternalMapping _mapping;

        public FlightsCacheProviderConverter(List<CacheProviderConfiguration> providerConfigurations)
        {
            UpdateInternalMapping(providerConfigurations);
        }

        public void UpdateInternalMapping(List<CacheProviderConfiguration> providerConfigurations)
        {
            _mapping = new InternalMapping(providerConfigurations);
        }

        public FlightCacheWriteConfiguration GetWriteConfiguration(int providerCode, string[] airlineCodes)
        {
            if (!_mapping.ProviderToAirlinesConfig.TryGetValue(providerCode, out var configs))
            {
                return null;
            }

            foreach (var (mappedAirlines, config) in configs)
            {
                if (mappedAirlines.IsSupersetOf(airlineCodes))
                {
                    return config;
                }
            }

            return null;
        }

        public FlightCacheWriteConfiguration GetWriteConfiguration(int providerCode, string supplier)
        {
            if (!_mapping.ProviderToAirlinesConfig.TryGetValue(providerCode, out var configs))
            {
                return null;
            }

            return configs.FirstOrDefault(x => x.Config.Supplier == supplier).Config;
        }

        public List<int> GetCacheProviderCodes(int providerCode, string supplier)
        {
            if (!_mapping.ProviderToAirlinesConfig.TryGetValue(providerCode, out var configs))
            {
                return new List<int>();
            }

            return configs.Where(x => x.Config.Supplier == supplier).Select(x => x.Config.CacheProviderCode).ToList();
        }

        public List<int> ToFCProviderCodes(List<int> providerCodes, List<string> airlineCodes)
        {
            var results = new HashSet<int>();

            foreach (var providerCode in providerCodes)
            {
                if (_mapping.ProviderToAirlinesConfig.TryGetValue(providerCode, out var configs))
                {
                    var cacheProviderCodes = airlineCodes.Any()
                        ? airlineCodes.Select(ac => configs.FirstOrDefault(e => e.Airlines.Contains(ac)).Config?.CacheProviderCode ?? providerCode)
                        : configs.Select(x => x.Config.CacheProviderCode).Append(providerCode);
                    foreach (var code in cacheProviderCodes) results.Add(code);
                }
                else
                {
                    results.Add(providerCode);
                }
            }

            return results.ToList();
        }
    }
}