using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace Esky.FlightsCache.MonthlyAggregator.Tests;

public class DiTests
{
    [Fact]
    public void Verify()
    {
        var configuration = new ConfigurationBuilder()
            .AddJsonFile("appsettings.json")
            .AddJsonFile("appsettings.Development.json")
            .Build();
        new ServiceCollection()
            .RegisterServices(configuration)
            .BuildServiceProvider(new ServiceProviderOptions { ValidateOnBuild = true, ValidateScopes = true });
    }
}