using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc.Testing;

namespace Esky.FlightsCache.MonthlyAggregator.Tests;

public class Application : WebApplicationFactory<MonthlyAggregationHostedService>
{
    protected override void ConfigureWebHost(IWebHostBuilder builder)
    {
        builder.UseDefaultServiceProvider(opt =>
        {
            opt.ValidateScopes = true;
            opt.ValidateOnBuild = true;
        });
    }
}