using MassTransit;
using MassTransit.Serialization;
using System.Net.Mime;

namespace Esky.FlightsCache.ServiceBus.Serialization
{
    internal class NewtonsoftJsonGzipSerializerFactory : ISerializerFactory
    {
        public IMessageSerializer CreateSerializer()
        {
            return new GzipJsonMessageSerializer(new NewtonsoftJsonMessageSerializer());
        }

        public IMessageDeserializer CreateDeserializer()
        {
            return new GzipJsonMessageDeserializer(
                new NewtonsoftJsonMessageDeserializer(NewtonsoftJsonMessageSerializer.Deserializer));
        }

        public ContentType ContentType => GzipJsonMessageSerializer.JsonGzipContentType;
    }
}