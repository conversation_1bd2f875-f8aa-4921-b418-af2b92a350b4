using MassTransit;
using MassTransit.Serialization;
using System.Net.Mime;

namespace Esky.FlightsCache.ServiceBus.Serialization
{
    internal class SystemTextJsonGzipSerializerFactory : ISerializerFactory
    {
        public IMessageSerializer CreateSerializer()
        {
            return new GzipJsonMessageSerializer(SystemTextJsonMessageSerializer.Instance);
        }

        public IMessageDeserializer CreateDeserializer()
        {
            return new GzipJsonMessageDeserializer(SystemTextJsonMessageSerializer.Instance);
        }

        public ContentType ContentType => GzipJsonMessageSerializer.JsonGzipContentType;
    }
}