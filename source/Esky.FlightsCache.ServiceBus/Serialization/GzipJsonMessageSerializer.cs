using MassTransit;
using System.IO;
using System.IO.Compression;
using System.Net.Mime;

namespace Esky.FlightsCache.ServiceBus.Serialization
{
    internal class GzipJsonMessageSerializer : IMessageSerializer
    {
        public static readonly ContentType JsonGzipContentType = new("application/vnd.masstransit.gzip+json");

        private readonly IMessageSerializer _decorated;

        public GzipJsonMessageSerializer(IMessageSerializer decorated)
        {
            _decorated = decorated;
        }

        public MessageBody GetMessageBody<T>(SendContext<T> context) where T : class
        {
            var body = _decorated.GetMessageBody(context);

            using var bodyStream = body.GetStream();
            using var compressed = new MemoryStream();
            using var gzip = new GZipStream(compressed, CompressionMode.Compress);

            bodyStream.CopyTo(gzip);
            gzip.Flush();

            return new StreamMessageBodyWrapper(compressed);
        }

        public ContentType ContentType => JsonGzipContentType;
    }
}