using MassTransit;
using Newtonsoft.Json;
using System.Text.Json.Serialization;

namespace Esky.FlightsCache.ServiceBus.Serialization
{
    public static class RegistrationExtensions
    {
        public static void ConfigureSerializer(this IBusFactoryConfigurator configurator, BusSettings settings)
        {
            switch (settings.SerializationProvider)
            {
                case SerializationProvider.NewtonsoftJson:
                    configurator.ConfigureNewtonsoftJsonSerializer(settings);
                    break;
                case SerializationProvider.SystemTextJson:
                default:
                    configurator.ConfigureSystemTextJsonSerializer(settings);
                    break;
            }
        }

        private static void ConfigureNewtonsoftJsonSerializer(this IBusFactoryConfigurator configurator,
            BusSettings settings)
        {
            switch (settings.SerializationMode)
            {
                case SerializationMode.JsonGzip:
                    configurator.UseNewtonsoftJsonGzipSerializer();
                    break;
                case SerializationMode.Json:
                default:
                    configurator.UseNewtonsoftJsonSerializer();
                    break;
            }

            configurator.ConfigureNewtonsoftJsonSerializer(s =>
            {
                s.DefaultValueHandling = DefaultValueHandling.Ignore;
                return s;
            });
        }

        private static void ConfigureSystemTextJsonSerializer(this IBusFactoryConfigurator configurator,
            BusSettings settings)
        {
            if (settings.SerializationMode == SerializationMode.JsonGzip)
            {
                configurator.UseSystemTextJsonGzipSerializer();
            }

            configurator.ConfigureJsonSerializerOptions(options =>
            {
                options.DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingDefault;
                return options;
            });
        }

        /// <summary>
        ///     Add the Newtonsoft.Json serializer/deserializer to the bus wrapped with gzip compression
        /// </summary>
        /// <param name="configurator"></param>
        private static IBusFactoryConfigurator UseNewtonsoftJsonGzipSerializer(
            this IBusFactoryConfigurator configurator)
        {
            var factory = new NewtonsoftJsonGzipSerializerFactory();

            configurator.AddSerializer(factory);
            configurator.AddDeserializer(factory, true);

            return configurator;
        }

        /// <summary>
        ///     Add the System.Text.Json serializer/deserializer to the bus wrapped with gzip compression
        /// </summary>
        /// <param name="configurator"></param>
        private static IBusFactoryConfigurator UseSystemTextJsonGzipSerializer(
            this IBusFactoryConfigurator configurator)
        {
            var factory = new SystemTextJsonGzipSerializerFactory();

            configurator.AddSerializer(factory);
            configurator.AddDeserializer(factory, true);

            return configurator;
        }
    }
}