using MassTransit;
using System.IO;
using System.Text;

namespace Esky.FlightsCache.ServiceBus.Serialization
{
    internal class StreamMessageBodyWrapper : MessageBody
    {
        private readonly byte[] _bytes;
        private string _string;

        public StreamMessageBodyWrapper(MemoryStream stream)
        {
            _bytes = stream.ToArray();
        }

        public Stream GetStream()
        {
            return new MemoryStream(_bytes);
        }

        public byte[] GetBytes()
        {
            return _bytes;
        }

        public string GetString()
        {
            return _string ??= Encoding.UTF8.GetString(GetBytes());
        }

        public long? Length => _bytes.Length;
    }
}