using MassTransit;
using MassTransit.Serialization;
using System;
using System.IO;
using System.IO.Compression;
using System.Net.Mime;

namespace Esky.FlightsCache.ServiceBus.Serialization
{
    internal class GzipJsonMessageDeserializer : IMessageDeserializer
    {
        private readonly IMessageDeserializer _decorated;

        public GzipJsonMessageDeserializer(IMessageDeserializer decorated)
        {
            _decorated = decorated;
        }

        public void Probe(ProbeContext context)
        {
            var scope = context.CreateScope("json.gzip");
            scope.Add("contentType", GzipJsonMessageSerializer.JsonGzipContentType.MediaType);
            _decorated.Probe(context);
        }

        public ConsumeContext Deserialize(ReceiveContext receiveContext)
        {
            return new BodyConsumeContext(
                receiveContext,
                Deserialize(
                    receiveContext.Body,
                    receiveContext.TransportHeaders,
                    receiveContext.InputAddress
                )
            );
        }

        public SerializerContext Deserialize(MessageBody body, Headers headers, Uri destinationAddress = null)
        {
            using var decompressed = new MemoryStream();
            using var stream = body.GetStream();
            using var gzip = new GZipStream(stream, CompressionMode.Decompress);

            gzip.CopyTo(decompressed);

            return _decorated.Deserialize(new StreamMessageBodyWrapper(decompressed), headers, destinationAddress);
        }

        public MessageBody GetMessageBody(string text)
        {
            return _decorated.GetMessageBody(text);
        }

        public ContentType ContentType => GzipJsonMessageSerializer.JsonGzipContentType;
    }
}