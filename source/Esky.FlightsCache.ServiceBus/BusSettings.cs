using Esky.FlightsCache.ServiceBus.Serialization;

namespace Esky.FlightsCache.ServiceBus
{
    public class BusSettings
    {
        public string Url { get; set; }
        public string Login { get; set; }
        public string Password { get; set; }
        public bool UseScheduler { get; set; }
        public string SchedulerUrl { get; set; }
        public bool IsDebug { get; set; }

        public SerializationProvider SerializationProvider { get; set; }
        public SerializationMode SerializationMode { get; set; }

        public string[] ClusterMembers { get; set; }
        public bool UseSsl { get; set; }

        public ushort? PrefetchCount { get; set; }
        public ushort? ConcurrencyLimit { get; set; }

        public RabbitMqBatchPublishSettings RabbitMqBatchPublishSettings { get; set; }
    }
}