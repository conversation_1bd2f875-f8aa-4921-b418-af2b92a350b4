using Esky.FlightsCache.ServiceBus.Serialization;
using MassTransit;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Linq;
using System.Net.Security;

namespace Esky.FlightsCache.ServiceBus
{
    public static class MassTransitBus
    {
        public static IServiceCollection AddBus(
            this IServiceCollection services,
            BusSettings settings,
            Action<IBusRegistrationConfigurator> busConfigurator = null,
            Action<IBusRegistrationContext, IRabbitMqBusFactoryConfigurator> configure = null)
        {
            return services.AddMassTransit(bus =>
            {
                busConfigurator?.Invoke(bus);

                bus.UsingRabbitMq((context, configurator) =>
                {
                    configurator.Host(
                        new Uri(settings.Url),
                        host =>
                        {
                            host.Username(settings.Login);
                            host.Password(settings.Password);

                            if (settings.RabbitMqBatchPublishSettings?.Enabled ?? false)
                            {
                                host.ConfigureBatchPublish(batchPublishConfigurator =>
                                {
                                    batchPublishConfigurator.Enabled = true;
                                    var publishSettings = settings.RabbitMqBatchPublishSettings;

                                    if (publishSettings.MessageLimit.HasValue)
                                    {
                                        batchPublishConfigurator.MessageLimit = publishSettings.MessageLimit.Value;
                                    }

                                    if (publishSettings.SizeLimit.HasValue)
                                    {
                                        batchPublishConfigurator.SizeLimit = publishSettings.SizeLimit.Value;
                                    }

                                    if (publishSettings.Timeout.HasValue)
                                    {
                                        batchPublishConfigurator.Timeout = publishSettings.Timeout.Value;
                                    }
                                });
                            }

                            if (settings.IsDebug)
                            {
                                return;
                            }

                            if (settings.ClusterMembers?.Any() ?? false)
                            {
                                host.UseCluster(cluster =>
                                {
                                    settings.ClusterMembers.ToList().ForEach(cluster.Node);
                                });
                            }

                            if (settings.UseSsl)
                            {
                                host.UseSsl(ssl => ssl.AllowPolicyErrors(SslPolicyErrors.None));
                            }
                        }
                    );

                    configurator.AutoDelete = true;
                    configurator.PurgeOnStartup = false;
                    configurator.Durable = true;

                    configurator.UseCircuitBreaker(cfg =>
                    {
                        cfg.ActiveThreshold = 50;
                        cfg.TrackingPeriod = TimeSpan.FromMinutes(1);
                        cfg.TripThreshold = 50;
                    });

                    var prefetchCount = settings.PrefetchCount;
                    if (prefetchCount.HasValue)
                    {
                        configurator.PrefetchCount = prefetchCount.Value;
                    }

                    var concurrencyLimit = settings.ConcurrencyLimit;
                    if (concurrencyLimit.HasValue)
                    {
                        configurator.UseConcurrencyLimit(concurrencyLimit.Value);
                    }

                    configurator.UseRetry(cfg =>
                    {
                        cfg.Incremental(10, TimeSpan.FromSeconds(30), TimeSpan.FromMinutes(1));
                    });

                    if (settings.UseScheduler)
                    {
                        configurator.UseMessageScheduler(new Uri(settings.SchedulerUrl));
                    }

                    configurator.ConfigureSerializer(settings);

                    configure?.Invoke(context, configurator);
                });
            });
        }

        /// <summary>
        ///     Adds bus with metrics instrumentation using the built-in .NET Meter class
        /// </summary>
        public static IServiceCollection AddBusWithInstrumentation(
            this IServiceCollection services,
            BusSettings settings,
            Action<IBusRegistrationConfigurator> busConfigurator = null,
            Action<IBusRegistrationContext, IRabbitMqBusFactoryConfigurator> configure = null)
        {
            void ConfigureAction(IBusRegistrationContext context, IRabbitMqBusFactoryConfigurator configurator)
            {
                configure?.Invoke(context, configurator);
                configurator.UseInstrumentation();
            }

            services.AddBus(settings, busConfigurator, ConfigureAction);

            return services;
        }
    }
}