using Esky.FlightsCache.MessageContract;
using Esky.FlightsCache.ResultFilters.AirportCode;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Esky.FlightsCache.Processing.Helpers;

namespace Esky.FlightsCache.Processing
{
    public class SearchCodesCacheRequestFlightsDecorator : ICacheRequestFlightsDecorator
    {
        private readonly IAirportsRepository _airportsRepository;

        public SearchCodesCacheRequestFlightsDecorator(IAirportsRepository airportsRepository)
        {
            _airportsRepository = airportsRepository;
        }

        public async Task Decorate(IEnumerable<FlightCache> flights)
        {
            var airports = await _airportsRepository.GetAirportsAsync();
            foreach (var leg in flights.SelectMany(f => f.Legs))
            {
                var departureHash = leg.DepartureCode.GetAirportHashCode();
                var arrivalHash = leg.ArrivalCode.GetAirportHashCode();

                leg.DepartureSearchCodes = airports.GetSearchCodes(departureHash);
                leg.ArrivalSearchCodes = airports.GetSearchCodes(arrivalHash);
                leg.DepartureAirportDetails = airports.GetAirportDetails(departureHash);
                leg.ArrivalAirportDetails = airports.GetAirportDetails(arrivalHash);
            }
        }
    }
}