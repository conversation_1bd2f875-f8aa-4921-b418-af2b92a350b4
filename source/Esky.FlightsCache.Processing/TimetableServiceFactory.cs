using Esky.Framework.PartnerSettings.Enums;
using Microsoft.Extensions.DependencyInjection;
using System;

namespace Esky.FlightsCache.Processing
{
    public interface ITimetableServiceFactory
    {
        ITimetableService Create(ProviderCodeEnum providerCode);
    }
    
    public class TimetableServiceFactory : ITimetableServiceFactory
    {
        private readonly IServiceProvider _serviceProvider;

        public TimetableServiceFactory(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
        }

        public ITimetableService Create(ProviderCodeEnum providerCode)
        {
            return providerCode switch
            {
                ProviderCodeEnum.TravelFusion => _serviceProvider.GetRequiredService<TravelFusionTimetableService>(),
                _ => _serviceProvider.GetRequiredService<DisabledTimetableService>()
            };
        }
    }
}