using Esky.FlightsCache.Common.Model;
using Esky.FlightsCache.MessageContract;
using Esky.FlightsCache.Processing.Helpers;
using System;
using System.Collections.Generic;
using System.Text;

namespace Esky.FlightsCache.Processing;

public static class FlightCacheExtensions
{
    public static List<FlightWrapper> SplitFlights(this IEnumerable<FlightCache> flights, long? sourceId = null)
    {
        var result = new List<FlightWrapper>();
        var uniqueLegs = new HashSet<string>();

        foreach (var flight in flights)
        {
            if (flight.IsRoundTrip())
            {
                result.Add(
                    new FlightWrapper
                    {
                        SourceId = sourceId,
                        ExpirationDate = flight.ExpirationDate,
                        Legs = flight.Legs,
                        LegsCanBeUseSeparately = flight.LegsCanBeUseSeparately,
                        ProviderCode = flight.ProviderCode,
                        SearchDate = flight.SearchDate,
                        SearchId = flight.SearchId,
                        SessionId = flight.SessionId,
                        Supplier = flight.Supplier,
                        Id = flight.Id,
                        ValidatingCarrier = flight.ValidatingCarrier,
                        ReadProviderCode = flight.ReadProviderCode
                    }
                );
                continue;
            }

            foreach (var leg in flight.Legs)
            {
                var hash = GetUniqueHash(
                    flight.ProviderCode,
                    leg.GetSegmentsHash(),
                    leg.DepartureCode,
                    leg.ArrivalCode,
                    leg.DepartureDate,
                    leg.ArrivalDate,
                    leg.SeparationOptions
                );

                if (!uniqueLegs.Add(hash) || leg.SeparationOptions == null)
                {
                    continue;
                }

                result.Add(
                    new FlightWrapper
                    {
                        SourceId = sourceId,
                        ExpirationDate = flight.ExpirationDate,
                        Legs = [leg],
                        LegsCanBeUseSeparately = flight.LegsCanBeUseSeparately,
                        ProviderCode = flight.ProviderCode,
                        SearchDate = flight.SearchDate,
                        SearchId = flight.SearchId,
                        SessionId = flight.SessionId,
                        Supplier = flight.Supplier,
                        ValidatingCarrier = flight.ValidatingCarrier,
                        ReadProviderCode = flight.ReadProviderCode
                    }
                );
            }
        }

        return result;
    }

    private static string GetUniqueHash(
        int providerCode,
        string segmentsHash,
        string departureCode,
        string arrivalCode,
        DateTime departureDate,
        DateTime arrivalDate,
        SeparationOptions separationOptions)
    {
        var sb = new StringBuilder();
        sb.Append(providerCode);
        sb.Append(segmentsHash);
        sb.Append(departureCode);
        sb.Append(arrivalCode);
        sb.Append(departureDate);
        sb.Append(arrivalDate);
        if (separationOptions != null)
        {
            sb.Append(separationOptions.Options);
            sb.Append(separationOptions.MinStay);
        }

        return sb.ToString();
    }
}