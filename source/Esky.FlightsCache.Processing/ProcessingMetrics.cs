using System;
using System.Collections.Generic;
using System.Diagnostics.Metrics;

namespace Esky.FlightsCache.Processing
{
    public static class ProcessingMetrics
    {
        private static readonly Meter _meter = new("FlightsCache.Processing");

        private static readonly Counter<int> _providers =
            _meter.CreateCounter<int>("flights-providers", "row", "Number of flights per provider");

        private static readonly Counter<int> _airline =
            _meter.CreateCounter<int>("flights-airlines", "row", "Number of flights per airline");

        private static readonly Counter<int> _flightsBySource = _meter.CreateCounter<int>("flights-by-source", "row", "Number of flights per CacheRequest source");

        public static readonly Counter<int> FlightsCount =
            _meter.CreateCounter<int>("flights-count", "row", "Number of flights to store");

        public static readonly Counter<int> FlightsFiltered =
            _meter.CreateCounter<int>("flights-filtered-out", "row", "Number of flights removed by filters");

        public static readonly Counter<int> SqlFlightsSkipped = _meter.CreateCounter<int>("flights-skipped-sql", "row",
            "Number of flights not saved to SQL due to request options");

        public static readonly Histogram<int> StoreFlightsTotalTime =
            _meter.CreateHistogram<int>("store-flights-total-time", "ms", "Total time needed to complete operation");

        public static void ProviderCounter(int code, int count)
        {
            _providers.Add(count, new KeyValuePair<string, object>("Provider", code));
        }

        public static void AirlineCounter(string code, int count)
        {
            _airline.Add(count, new KeyValuePair<string, object>("Airline", code));
        }

        public static void AddSourceFlightsCount(string source, int count)
        {
            _flightsBySource.Add(count, new KeyValuePair<string, object>("Source", source));
        }
    }
}