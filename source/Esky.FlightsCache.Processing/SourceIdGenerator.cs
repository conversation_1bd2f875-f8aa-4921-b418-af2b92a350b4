using Esky.FlightsCache.Database;
using IdGen;
using System;
using System.Diagnostics;
using System.Security.Cryptography;

namespace Esky.FlightsCache.Processing;

public interface ISourceIdGenerator
{
    long GenerateSourceId();
}

public class SourceIdGenerator : ISourceIdGenerator
{
    // 41 bits for timestamp (69 years since epoch)
    private const byte _timestampBits = 41;
    // 12 bits for generator-id (max 4096 pods)
    private const byte _generatorIdBits = 12;
    // 10 bits for sequence (1024 id/ms per pod)
    private const byte _sequenceBits = 10;
    
    private const long _maxGeneratorId = (1L << _generatorIdBits) - 1;
    
    private static readonly DateTime _epoch = new(2025, 7, 1, 0, 0, 0, DateTimeKind.Utc);
    
    private readonly IdGenerator _generator;

    public SourceIdGenerator()
    {
        var podIdentifier = Environment.GetEnvironmentVariable("POD_UID") ?? Guid.NewGuid().ToString();

        var structure = new IdStructure(_timestampBits, _generatorIdBits, _sequenceBits);

        var options = new IdGeneratorOptions(structure, new DefaultTimeSource(_epoch));

        var hashBytes = MD5.HashData(System.Text.Encoding.UTF8.GetBytes(podIdentifier));
        var generatorId = (int)Math.Abs(BitConverter.ToInt64(hashBytes, 0) % (_maxGeneratorId + 1)); 

        _generator = new IdGenerator(generatorId, options);
    }

    public long GenerateSourceId()
    {
        using var activity = Activities.Source.StartActivity();
        var startTimestamp = Stopwatch.GetTimestamp();

        var sourceId = _generator.CreateId();

        DatabaseMetrics.GenerateSourceIdTime.Record((int)Stopwatch.GetElapsedTime(startTimestamp).TotalMilliseconds);
        
        return sourceId;
    }
}