using Esky.FlightsCache.Database.Timetables;
using Esky.FlightsCache.MessageContract;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Esky.FlightsCache.Processing
{
    public class TravelFusionTimetableService(ITimetableRepository timetableRepository) : ITimetableService
    {
        public Task GenerateAsync(List<FlightCache> flights)
        {
            var items = flights
                .GroupBy(CreateTimetableItemKey)
                .Select(f => new TimetableItem
                {
                    Id = f.Key,
                    Suppliers = f.Select(x => x.Supplier).Distinct().ToArray(),
                    Timestamp = DateTime.UtcNow
                })
                .ToList();

            return timetableRepository.UpsertTimetableItemsAsync(items);
        }

        private static TimetableItemKey CreateTimetableItemKey(FlightCache flight)
        {
            var firstLeg = flight.Legs.First();
            return TimetableItemKey.Create(firstLeg.DepartureCode, firstLeg.ArrivalCode, firstLeg.DepartureDate);
        }
    }
}