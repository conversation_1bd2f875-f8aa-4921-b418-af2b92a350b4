using Esky.FlightsCache.MessageContract;
using Esky.Framework.PartnerSettings.Enums;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Esky.FlightsCache.Processing
{
    public class OfficeIdAsSupplierFlightsDecorator : ICacheRequestFlightsDecorator
    {
        private readonly HashSet<int> _includedProviders =
        [
            (int)ProviderCodeEnum.Amadeus,
            (int)ProviderCodeEnum.AmadeusNdc,
            (int)ProviderCodeEnum.LatamNdc
        ];

        public Task Decorate(IEnumerable<FlightCache> flights)
        {
            foreach (var flight in flights.Where(x => _includedProviders.Contains(x.ProviderCode)))
            {
                flight.Supplier = flight.Legs.FirstOrDefault()?.Segments.FirstOrDefault()?.FareDetails?.OfficeId;
            }

            return Task.CompletedTask;
        }
    }
}