using Esky.FlightsCache.MessageContract;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Esky.FlightsCache.Processing.PriceDecorators;

public class CustomFeeDecorator : ICacheRequestFlightsDecorator
{
    private readonly ILookup<string, IFeeCalculator> _calculators;

    public CustomFeeDecorator(IEnumerable<IFeeCalculator> calculators)
    {
        _calculators = calculators
            .SelectMany(
                calculator => calculator.AirlineCodes,
                (calculator, airline) => new { Calculator = calculator, Airline = airline }
            )
            .OrderBy(x => x.Airline)
            .ThenBy(x => x.Calculator.Order)
            .ToLookup(e => e.Airline, e => e.Calculator);
    }

    public Task Decorate(IEnumerable<FlightCache> flights)
    {
        foreach (var flight in flights)
        {
            var airline = flight.Legs[0].AirlineCode;
            foreach (var calc in _calculators[airline])
            {
                calc.Apply(flight);
            }
        }

        return Task.CompletedTask;
    }
}