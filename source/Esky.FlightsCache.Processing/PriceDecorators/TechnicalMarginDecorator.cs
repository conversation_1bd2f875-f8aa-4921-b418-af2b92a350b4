using Esky.FlightsCache.MessageContract;
using Esky.FlightsCache.Processing.TechnicalMargin;
using Esky.FlightsCache.ProviderMapping;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Esky.FlightsCache.Processing.PriceDecorators;

public class TechnicalMarginDecorator(ITechnicalMarginCalculator technicalMarginCalculator)
    : ICacheRequestFlightsDecorator
{
    public Task Decorate(IEnumerable<FlightCache> flights)
    {
        foreach (var flight in flights)
        {
            ApplyTechnicalMargin(flight);
        }

        return Task.CompletedTask;
    }
    
    private void ApplyTechnicalMargin(FlightCache flight)
    {
        var airlines = flight.Legs
            .SelectMany(leg => leg.Segments.Select(segment => segment.AirlineCode))
            .ToHashSet();

        var totalSeatsLeft = flight.Legs.Min(l => l.TotalFaresLeft);
            
            
        foreach (var leg in flight.Legs)
        {
            var context = new PriceMarginContext(
                flight.OriginalProviderCode,
                flight.Supplier, 
                airlines, 
                totalSeatsLeft,
                leg.CurrencyCode);
                
            var fallbackPrices = leg.AdultPrices;
            
            leg.AdultPrices = technicalMarginCalculator.GetPricesWithMargin(
                context with { passengerType = PassengerType.Adult }, leg.AdultPrices);
                
            leg.YouthPrices = technicalMarginCalculator.GetPricesWithMargin(
                context with { passengerType = PassengerType.Youth }, leg.YouthPrices, fallbackPrices);
                
            leg.ChildPrices = technicalMarginCalculator.GetPricesWithMargin(
                context with { passengerType = PassengerType.Child }, leg.ChildPrices, fallbackPrices);
                
            leg.InfantPrices = technicalMarginCalculator.GetPricesWithMargin(
                context with { passengerType = PassengerType.Infant }, leg.InfantPrices, fallbackPrices);
        }
    }
}