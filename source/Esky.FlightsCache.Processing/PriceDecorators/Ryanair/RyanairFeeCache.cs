using System.Collections.Generic;

namespace Esky.FlightsCache.Processing.PriceDecorators.Ryanair;

public class RyanairFeeCache : IRyanairFeeCache
{
    private readonly Dictionary<Key, Fee> _cache = [];

    public Fee? Get(string currency, Route route)
    {
        var fee = _cache.TryGetValue(new Key(currency, route.Departure, route.Arrival), out var cached)
            ? cached
            : (Fee?)null;
        return fee;
    }

    public void ReplaceAll(IEnumerable<RyanairFee> fees)
    {
        _cache.Clear();

        foreach (var (currency, route, fee) in fees)
        {
            _cache[new Key(currency, route.Departure, route.Arrival)] = fee;
        }
    }

    private record struct Key(string Currency, string Departure, string Arrival);
}