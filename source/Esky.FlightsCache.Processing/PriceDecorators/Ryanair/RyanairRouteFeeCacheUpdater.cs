using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace Esky.FlightsCache.Processing.PriceDecorators.Ryanair;

public class RyanairRouteFeeCacheUpdater : BackgroundService
{
    private readonly ILogger<RyanairRouteFeeCacheUpdater> _logger;
    private readonly IRyanairFeeStorage _feeStorage;
    private readonly IRyanairFeeCache _feeCache;
    private readonly PeriodicTimer _timer;

    public RyanairRouteFeeCacheUpdater(ILogger<RyanairRouteFeeCacheUpdater> logger, IRyanairFeeStorage feeStorage, IRyanairFeeCache feeCache)
    {
        _logger = logger;
        _feeStorage = feeStorage;
        _feeCache = feeCache;
        _timer = new PeriodicTimer(TimeSpan.FromHours(3));
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        do
        {
            var fees = await _feeStorage.GetAll(stoppingToken);
            _feeCache.ReplaceAll(fees);
            _logger.LogInformation("Updated RyanairFeeCache: {Count}", fees.Count);
        } while (await _timer.WaitForNextTickAsync(stoppingToken));
    }
}