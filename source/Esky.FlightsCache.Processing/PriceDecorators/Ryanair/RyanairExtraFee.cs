using Esky.FlightsCache.CurrencyProvider;
using System.Collections.Generic;

namespace Esky.FlightsCache.Processing.PriceDecorators.Ryanair;

public class RyanairExtraFee3Pax(ICurrencyRatioProvider currencyRatioProvider) : BaseExtraFee(currencyRatioProvider)
{
    protected override int ExtraFee => 1;
    protected override string ExtraFeeCurrency => "EUR";
    protected override int NumberOfPaxesThreshold => 3;

    public override int Order => 0;
    public override IEnumerable<string> AirlineCodes => RyanairFeeSettings.Airlines;
}

public class RyanairExtraFee5Pax(ICurrencyRatioProvider currencyRatioProvider) : BaseExtraFee(currencyRatioProvider)
{
    protected override int ExtraFee => 1;
    protected override string ExtraFeeCurrency => "EUR";
    protected override int NumberOfPaxesThreshold => 5;

    public override int Order => 1;
    public override IEnumerable<string> AirlineCodes => RyanairFeeSettings.Airlines;
}