using Esky.FlightsCache.CurrencyProvider;
using Esky.FlightsCache.MessageContract;
using System.Collections.Generic;
using System.Linq;

namespace Esky.FlightsCache.Processing.PriceDecorators.Ryanair;

public class RyanairInfantFee : IFeeCalculator
{
    private readonly ICurrencyRatioProvider _currencyRatioProvider;
    private readonly IReadOnlyDictionary<string, decimal> _staticInfantPrices;
    private readonly IRyanairFeeCache _feeCache;

    public RyanairInfantFee(RyanairFeeSettings settings, ICurrencyRatioProvider currencyRatioProvider, IRyanairFeeCache feeCache)
    {
        _staticInfantPrices = settings.Infant;
        _currencyRatioProvider = currencyRatioProvider;
        _feeCache = feeCache;
    }

    public IEnumerable<string> AirlineCodes => RyanairFeeSettings.Airlines;

    public void Apply(FlightCache flight)
    {
        var currency = flight.Legs[0].CurrencyCode;
        var route = new Route { Departure = flight.Legs[0].DepartureCode, Arrival = flight.Legs[0].ArrivalCode };
        var fees = _feeCache.Get(currency, route);

        var infantPrice = fees?.InfantPrice ?? _staticInfantPrices.GetEstimatedPrice(currency, _currencyRatioProvider);
        if (infantPrice is null)
        {
            return;
        }

        foreach (var leg in flight.Legs.Where(leg => leg.InfantPrices.IsNullOrEmpty()))
        {
            leg.InfantPrices ??= [];
            leg.InfantPrices.Add(new PriceCacheEntry(infantPrice.Value, 0));
        }
    }
}