using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using MongoDB.Driver;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Esky.FlightsCache.Processing.PriceDecorators.Ryanair;

public class MongoRyanairFeeStorage : IRyanairFeeStorage
{
    private readonly IMongoCollection<RouteFee> _feeCollection;

    public MongoRyanairFeeStorage(string connectionString, string collectionName)
    {
        var url = MongoUrl.Create(connectionString);
        _feeCollection = new MongoClient(url)
            .GetDatabase(url.DatabaseName)
            .GetCollection<RouteFee>(collectionName);
    }

    public async Task<IReadOnlyCollection<RyanairFee>> GetAll(CancellationToken cancellationToken)
    {
        var routeFees = await _feeCollection
            .Find(FilterDefinition<RouteFee>.Empty)
            .ToListAsync(cancellationToken);

        return routeFees
            .Select(e =>
                new RyanairFee(
                    e.Route.Currency,
                    new Route { Departure = e.Route.Departure, Arrival = e.Route.Arrival },
                    new Ryanair.Fee(e.Fee.MandatorySeatFee, e.Fee.ChildDiscount, e.Fee.InfantFee)
                )
            ).ToList();
    }

    [BsonIgnoreExtraElements]
    private record RouteFee
    {
        [BsonId] public required Key Route { get; init; }
        [BsonIgnoreIfDefault] public required Fee Fee { get; init; }
    }

    private record Key(string Departure, string Arrival, string Currency);

    [BsonIgnoreExtraElements]
    private record Fee
    {
        [BsonRepresentation(BsonType.Decimal128)]
        public decimal? InfantFee { get; init; }
        [BsonRepresentation(BsonType.Decimal128)]
        public decimal? MandatorySeatFee { get; init; }
        [BsonRepresentation(BsonType.Decimal128)]
        public decimal? ChildDiscount { get; init; }
    }
}