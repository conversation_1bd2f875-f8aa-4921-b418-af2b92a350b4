using Esky.FlightsCache.CurrencyProvider;
using Esky.FlightsCache.MessageContract;
using System.Collections.Generic;

namespace Esky.FlightsCache.Processing.PriceDecorators.Ryanair;

public class RyanairChildFee : IFeeCalculator
{
    private readonly ICurrencyRatioProvider _currencyRatioProvider;
    private readonly IReadOnlyDictionary<string, decimal> _staticChildAdditionalPrices;
    private readonly IRyanairFeeCache _feeCache;

    public RyanairChildFee(RyanairFeeSettings settings, ICurrencyRatioProvider currencyRatioProvider, IRyanairFeeCache feeCache)
    {
        _staticChildAdditionalPrices = settings.Child;
        _currencyRatioProvider = currencyRatioProvider;
        _feeCache = feeCache;
    }

    public IEnumerable<string> AirlineCodes => RyanairFeeSettings.Airlines;

    public void Apply(FlightCache flight)
    {
        flight.Legs.ForEach(Apply);
    }

    private void Apply(FlightCacheLeg leg)
    {
        var currency = leg.CurrencyCode;
        var route = new Route { Departure = leg.DepartureCode, Arrival = leg.ArrivalCode };
        var fees = _feeCache.Get(currency, route);

        var mandatorySeatFee = fees?.MandatorySeatFee ?? _staticChildAdditionalPrices.GetEstimatedPrice(currency, _currencyRatioProvider);
        if (mandatorySeatFee is null)
        {
            return;
        }

        leg.RawPriceComponents ??= [];
        if (mandatorySeatFee > decimal.Zero)
        {
            leg.RawPriceComponents["_guardianSeatFee"] = new PriceCacheEntry(mandatorySeatFee.Value, 0);
        }
        if (fees?.ChildDiscount > decimal.Zero)
        {
            var childDiscount = fees?.ChildDiscount.Value ?? 0m;
            leg.RawPriceComponents["_childDiscount"] = new PriceCacheEntry(childDiscount, 0);
        }
    }
}