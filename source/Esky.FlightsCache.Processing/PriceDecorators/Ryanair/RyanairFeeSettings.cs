using System.Collections.Generic;

namespace Esky.FlightsCache.Processing.PriceDecorators.Ryanair;

public class RyanairFeeSettings
{
    public static readonly string[] Airlines = { "FR", "RK" };

    public IReadOnlyDictionary<string, decimal> Child = new Dictionary<string, decimal>()
    {
        { "PLN", 36 },
        { "EUR", 8 }
    };

    public IReadOnlyDictionary<string, decimal> Infant = new Dictionary<string, decimal>()
    {
        { "PLN", 120 },
        { "EUR", 25 },
        { "GBP", 25 },
        { "CHF", 30 },
        { "NOK", 250 },
        { "SEK", 275 },
        { "CZK", 750 },
        { "HUF", 8750 },
        { "DKK", 200 },
        { "MAD", 300 }
    };
}