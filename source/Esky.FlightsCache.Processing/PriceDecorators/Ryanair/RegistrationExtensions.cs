using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace Esky.FlightsCache.Processing.PriceDecorators.Ryanair;


public static class RegistrationExtensions
{
    public static IServiceCollection AddRyanairFee(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddSingleton<RyanairFeeSettings>();
        services.AddSingleton<IFeeCalculator, RyanairChildFee>();
        services.AddSingleton<IFeeCalculator, RyanairInfantFee>();
        services.AddSingleton<IFeeCalculator, RyanairExtraFee3Pax>();
        services.AddSingleton<IFeeCalculator, RyanairExtraFee5Pax>();
        var cs = configuration.GetValue<string>("FlightsCacheConfiguration:ConnectionUrl");
        var collection = configuration.GetValue<string>("FlightsCacheConfiguration:RyanairRouteFeeCollectionName");
        services.AddSingleton<IRyanairFeeStorage>(_ => new MongoRyanairFeeStorage(cs, collection));
        services.AddSingleton<IRyanairFeeCache, RyanairFeeCache>();
        services.AddHostedService<RyanairRouteFeeCacheUpdater>();
        return services;
    }
}