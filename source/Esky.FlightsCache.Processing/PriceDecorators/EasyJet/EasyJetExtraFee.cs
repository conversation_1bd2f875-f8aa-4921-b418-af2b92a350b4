using Esky.FlightsCache.CurrencyProvider;
using System.Collections.Generic;

namespace Esky.FlightsCache.Processing.PriceDecorators.EasyJet;

public class EasyJetExtraFee5Pax(ICurrencyRatioProvider currencyRatioProvider) : BaseExtraFee(currencyRatioProvider)
{
    protected override int ExtraFee => 5;
    protected override string ExtraFeeCurrency => "EUR";
    protected override int NumberOfPaxesThreshold => 5;

    public override int Order => 1;
    public override IEnumerable<string> AirlineCodes => EasyJet.Airlines;
}

public class EasyJetExtraFee3Pax(ICurrencyRatioProvider currencyRatioProvider) : BaseExtraFee(currencyRatioProvider)
{
    protected override int ExtraFee => 2;
    protected override string ExtraFeeCurrency => "EUR";
    protected override int NumberOfPaxesThreshold => 3;

    public override int Order => 0;
    public override IEnumerable<string> AirlineCodes => EasyJet.Airlines;
}