using Esky.FlightsCache.Processing.PriceDecorators.AirlineFees;
using System.Collections.Generic;

namespace Esky.FlightsCache.Processing.PriceDecorators.EasyJet;

public class EasyJetUpdateInfantFee : AirlineUpdateInfantFee, IFeeCalculator
{
    public EasyJetUpdateInfantFee(IAirlineFeeCacheUpdater feeUpdater) : base(feeUpdater)
    {
    }

    public override IEnumerable<string> AirlineCodes => EasyJet.Airlines;
}