using Esky.FlightsCache.MessageContract;
using System.Collections.Generic;
using System.Linq;

namespace Esky.FlightsCache.Processing.PriceDecorators.EasyJet;

public class EasyJetChildFee : IFeeCalculator
{
    public IEnumerable<string> AirlineCodes => EasyJet.Airlines;

    public void Apply(FlightCache flight)
    {
        var leg = flight.Legs.First();

        if (leg.DepartureAirportDetails.CountryCode != "GB") return;

        var adultPricesWithoutAirPassengerDuty = leg.AdultPrices.Select(p => new PriceCacheEntry(p.BasePrice, taxPrice: 0, p.MinimumNumberOfPaxes));

        if (leg.ChildPrices.IsNullOrEmpty())
        {
            leg.ChildPrices = adultPricesWithoutAirPassengerDuty.ToList();
        }
    }
}