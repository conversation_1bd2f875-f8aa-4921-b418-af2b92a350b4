using Esky.FlightsCache.CurrencyProvider;
using Esky.FlightsCache.Processing.PriceDecorators.AirlineFees;
using System.Collections.Generic;

namespace Esky.FlightsCache.Processing.PriceDecorators.EasyJet;

public class EasyJetInfantFee : AirlineInfant<PERSON><PERSON>, IFeeCalculator
{
    public EasyJetInfantFee(ICurrencyRatioProvider currencyRatioProvider, IAirlineFeeCache feeCache)
        : base(currencyRatioProvider, feeCache)
    {
    }

    public override IEnumerable<string> AirlineCodes =>  EasyJet.Airlines;
}
