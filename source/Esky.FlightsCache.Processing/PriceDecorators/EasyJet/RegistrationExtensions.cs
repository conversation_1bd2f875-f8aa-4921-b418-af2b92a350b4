using Microsoft.Extensions.DependencyInjection;

namespace Esky.FlightsCache.Processing.PriceDecorators.EasyJet;

public static class RegistrationExtensions
{
    public static IServiceCollection AddEasyJetFee(this IServiceCollection services)
    {
        services.AddSingleton<IFeeCalculator, EasyJetExtraFee3Pax>();
        services.AddSingleton<IFeeCalculator, EasyJetExtraFee5Pax>();
        services.AddSingleton<IFeeCalculator, EasyJetInfantFee>();
        services.AddSingleton<IFeeCalculator, EasyJetChildFee>();
        services.AddSingleton<IFeeCalculator, EasyJetUpdateInfantFee>();
        return services;
    }
}