using Microsoft.Extensions.DependencyInjection;

namespace Esky.FlightsCache.Processing.PriceDecorators.Lufthansa;

public static class RegistrationExtensions
{
    public static IServiceCollection AddLufthansaFee(this IServiceCollection services)
    {
        services.AddSingleton<IFeeCalculator, LufthansaChildFee>();
        services.AddSingleton<IFeeCalculator, LufthansaInfantFee>();
        return services;
    }
}