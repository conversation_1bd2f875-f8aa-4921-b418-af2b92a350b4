using Esky.FlightsCache.MessageContract;
using Esky.FlightsCache.ObjectDirectory;
using System.Collections.Generic;
using System.Linq;

namespace Esky.FlightsCache.Processing.PriceDecorators.Lufthansa;

public class LufthansaInfantFee : IFeeCalculator
{
    public IEnumerable<string> AirlineCodes => Lufthansa.Airlines;
    private readonly IObjectDirectoryService _objectDirectory;

    public LufthansaInfantFee(IObjectDirectoryService objectDirectory)
    {
        _objectDirectory = objectDirectory;
    }

    public void Apply(FlightCache flight)
    {
        if (flight.Legs.Any(leg => !leg.InfantPrices.IsNullOrEmpty()))
            return;
        
        foreach (var leg in flight.Legs)
        {
            leg.InfantPrices ??= [];
            if (IsDomestic(leg, "DE"))
            {
                // German domestic flights for infant are free of charge
                leg.InfantPrices.Add(new PriceCacheEntry(0, 0));
            }
            else
            {
                var adult = leg.AdultPrices?.MinBy(x => x.MinimumNumberOfPaxes);
                if (adult is null) continue;
                leg.InfantPrices.Add(new PriceCacheEntry(adult.BasePrice * 0.1M, 0));
            }
        }
    }

    private bool IsDomestic(FlightCacheLeg leg, string countryCode)
    {
        return _objectDirectory.IsAirport(leg.DepartureCode)
            && _objectDirectory.GetCountryCodeByDestination(leg.DepartureCode) == countryCode
            && _objectDirectory.IsAirport(leg.ArrivalCode)
            && _objectDirectory.GetCountryCodeByDestination(leg.ArrivalCode) == countryCode;
    }
}