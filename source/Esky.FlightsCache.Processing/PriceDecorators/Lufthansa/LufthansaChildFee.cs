using Esky.FlightsCache.MessageContract;
using System.Collections.Generic;
using System.Linq;

namespace Esky.FlightsCache.Processing.PriceDecorators.Lufthansa;

public class LufthansaChildFee : IFeeCalculator
{
    public IEnumerable<string> AirlineCodes => Lufthansa.Airlines;

    public void Apply(FlightCache flight)
    {
        if (flight.Legs.Any(leg => !leg.ChildPrices.IsNullOrEmpty()))
            return;
        
        foreach (var leg in flight.Legs)
        {
            leg.ChildPrices ??= [];
            var adult = leg.AdultPrices?.MinBy(x => x.MinimumNumberOfPaxes);
            if (adult is null) continue;
            leg.ChildPrices.Add(new PriceCacheEntry(adult.BasePrice * 0.75M, adult.TaxPrice));
        }
    }
}