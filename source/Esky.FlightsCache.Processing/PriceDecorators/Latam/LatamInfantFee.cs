using Esky.FlightsCache.MessageContract;
using System.Collections.Generic;
using System.Linq;

namespace Esky.FlightsCache.Processing.PriceDecorators.Latam;

public class LatamInfantFee : IFeeCalculator
{
    public IEnumerable<string> AirlineCodes => Latam.Airlines;
    //source: https://www.latamairlines.com/us/en/experience/prepare-your-trip/traveling-with-infants-and-children
    public void Apply(FlightCache flight)
    {
        if (flight.Legs.Any(leg => !leg.InfantPrices.IsNullOrEmpty()))
            return;

        foreach (var leg in flight.Legs)
        {
            leg.InfantPrices ??= [];
            var adult = leg.AdultPrices?.MinBy(x => x.MinimumNumberOfPaxes);
            if (adult is null) continue;
            var (infBase, infTax) = IsDomesticFlight(flight) || flight.Legs[0].DepartureAirportDetails.CountryCode == "MX"
                ? (0M, 0M)
                : (adult.BasePrice * 0.1M, adult.TaxPrice);
            leg.InfantPrices.Add(new PriceCacheEntry(infBase, infTax));
        }
    }

    private bool IsDomesticFlight(FlightCache flight)
    {
        return flight.Legs.All(l => l.DepartureAirportDetails.CountryCode == l.ArrivalAirportDetails.CountryCode);
    }
}
