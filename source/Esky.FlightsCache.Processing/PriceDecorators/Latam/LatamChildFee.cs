using Esky.FlightsCache.MessageContract;
using System.Collections.Generic;
using System.Linq;

namespace Esky.FlightsCache.Processing.PriceDecorators.Latam;

public class LatamChildFee : IFeeCalculator
{
    public IEnumerable<string> AirlineCodes => Latam.Airlines;
    //source: https://www.latamairlines.com/us/en/experience/prepare-your-trip/traveling-with-infants-and-children
    public void Apply(FlightCache flight)
    {
        if (flight.Legs.Any(leg => !leg.ChildPrices.IsNullOrEmpty()))
            return;

        if (IsDomesticFlightInColombia(flight))
        {
            foreach (var leg in flight.Legs)
            {
                leg.ChildPrices ??= [];
                var adult = leg.AdultPrices?.MinBy(x => x.MinimumNumberOfPaxes);
                if (adult is null) continue;
                leg.ChildPrices.Add(new PriceCacheEntry(adult.BasePrice * 0.67M, adult.TaxPrice));
            }
        }
    }

    private bool IsDomesticFlightInColombia(FlightCache flight)
    {
        const string Colombia = "CO";
        return flight.Legs.All(l => l.DepartureAirportDetails.CountryCode == Colombia && l.ArrivalAirportDetails.CountryCode == Colombia);
    }
}
