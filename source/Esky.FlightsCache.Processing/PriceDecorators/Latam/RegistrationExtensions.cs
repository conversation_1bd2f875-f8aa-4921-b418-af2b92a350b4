using Microsoft.Extensions.DependencyInjection;

namespace Esky.FlightsCache.Processing.PriceDecorators.Latam;
public static class RegistrationExtensions
{
    public static IServiceCollection AddLatamFee(this IServiceCollection services)
    {
        services.AddSingleton<IFeeCalculator, LatamChildFee>();
        services.AddSingleton<IFeeCalculator, LatamInfantFee>();
        return services;
    }
}