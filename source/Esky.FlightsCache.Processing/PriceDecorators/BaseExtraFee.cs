using Esky.FlightsCache.CurrencyProvider;
using Esky.FlightsCache.MessageContract;
using System.Collections.Generic;
using System.Linq;

namespace Esky.FlightsCache.Processing.PriceDecorators;

//todo mb: remove with all inheritors - takeover the functionality by technical margins
public abstract class BaseExtraFee(ICurrencyRatioProvider currencyRatioProvider) : IFeeCalculator
{
    protected abstract int ExtraFee { get; }
    protected abstract string ExtraFeeCurrency { get; }
    protected abstract int NumberOfPaxesThreshold { get; }
    
    public virtual int Order => 0;
    public abstract IEnumerable<string> AirlineCodes { get; }

    public void Apply(FlightCache flight)
    {
        foreach (var leg in flight.Legs)
        {
            var ratio = currencyRatioProvider.GetRatio(ExtraFeeCurrency, leg.CurrencyCode);
            var extraFee = ExtraFee * ratio;

            ApplyExtraFee(leg.AdultPrices, extraFee);
            ApplyExtraFee(leg.YouthPrices, extraFee);
            ApplyExtraFee(leg.ChildPrices, extraFee);
        }
    }

    private void ApplyExtraFee(List<PriceCacheEntry> prices, decimal extraFee)
    {
        if (prices is null || 
            prices.Count == 0 || 
            prices.Any(x => x.MinimumNumberOfPaxes >= NumberOfPaxesThreshold))
            return;
        
        var price = prices.Where(x => !x.IsGenerated).MaxBy(x => x.MinimumNumberOfPaxes);
        prices.Add(new PriceCacheEntry(price.BasePrice + extraFee, price.TaxPrice, NumberOfPaxesThreshold){IsGenerated = true});
    }
}