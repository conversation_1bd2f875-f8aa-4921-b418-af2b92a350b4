using Esky.FlightsCache.MessageContract;
using Esky.FlightsCache.Processing.Helpers;
using System.Collections.Generic;
using System.Linq;

namespace Esky.FlightsCache.Processing.PriceDecorators.AirlineFees;

public abstract class AirlineUpdateInfantFee : IFeeCalculator
{
    private readonly IAirlineFeeCacheUpdater _feeUpdater;

    protected AirlineUpdateInfantFee(IAirlineFeeCacheUpdater feeUpdater)
    {
        _feeUpdater = feeUpdater;
    }

    public abstract IEnumerable<string> AirlineCodes { get; }

    public void Apply(FlightCache flight)
    {
        decimal? totalBasePrice = 0;
        decimal? totalTaxPrice = 0;
        foreach (var l in flight.Legs)
        {
            var price = l.InfantPrices?.FirstOrDefault(p => p.MinimumNumberOfPaxes == 1);
            totalBasePrice += price?.BasePrice;
            totalTaxPrice += price?.TaxPrice;
        }
        
        if (totalBasePrice is null)
        {
            return;
        }

        var airline = flight.Legs[0].AirlineCode;
        var currency = flight.Legs[0].CurrencyCode;
        var divisor = flight.IsRoundTrip() ? 2 : 1;
        var basePrice = totalBasePrice / divisor;
        var taxPrice = totalTaxPrice / divisor;

        _feeUpdater.UpdateInfantFee(airline, currency, flight.ProviderCode, basePrice, taxPrice);
    }
}