using Esky.FlightsCache.MessageContract;
using Microsoft.Extensions.Logging;
using System.Collections.Generic;
using System.Linq;

namespace Esky.FlightsCache.Processing.PriceDecorators.AirlineFees;

public interface IAirlineFeeCache
{
    PriceCacheEntry GetCheapestInfantFee(string airline, string currency);
    PriceCacheEntry GetInfantFee(string airline, string currency, int provider);
    void ReplaceAll(IEnumerable<AirlineFeeModel> fees);
}

public class AirlineFeeCache : IAirlineFeeCache
{
    private readonly ILogger<AirlineFeeCache> _logger;
    private readonly Dictionary<(string airline, string currency), AirlineFeeModel> _fees = [];

    public AirlineFeeCache(ILogger<AirlineFeeCache> logger)
    {
        _logger = logger;
    }

    public PriceCacheEntry GetCheapestInfantFee(string airline, string currency)
    {
        if (!_fees.TryGetValue((airline, currency), out var model))
        {
            return null;
        }

        var fallback = model.ProviderFees?
            .MinBy(p => p.Value.InfantBasePrice + p.Value.InfantTaxPrice)
            .Value;
        
        if (fallback is null) return null;
        
        return fallback.HasAnyValue
            ? new PriceCacheEntry(fallback.InfantBasePrice ?? 0, fallback.InfantTaxPrice ?? 0)
            : null;
    }

    public PriceCacheEntry GetInfantFee(string airline, string currency, int provider)
    {
        if (!_fees.TryGetValue((airline, currency), out var model))
        {
            return null;
        }

        if (!model.ProviderFees.TryGetValue(provider, out var fees) || !fees.HasAnyValue)
        {
            return null;
        }

        return new PriceCacheEntry(fees.InfantBasePrice ?? 0, fees.InfantTaxPrice ?? 0);
    }

    public void ReplaceAll(IEnumerable<AirlineFeeModel> fees)
    {
        _fees.Clear();
        
        foreach (var fee in fees)
        {
            _fees[(fee.Airline, fee.Currency)] = fee;
        }
    }
}