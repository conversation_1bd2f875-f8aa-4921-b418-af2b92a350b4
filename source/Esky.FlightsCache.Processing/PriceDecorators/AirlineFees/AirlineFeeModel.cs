using System;
using System.Collections.Generic;

namespace Esky.FlightsCache.Processing.PriceDecorators.AirlineFees;

public record AirlineFeeModel(string Airline, string Currency)
{
    public Dictionary<int, ProviderFee> ProviderFees { get; init; } = [];
    
    public record ProviderFee
    {
        public decimal? InfantBasePrice { get; init; }
        public decimal? InfantTaxPrice { get; init; }
        
        public DateTime UpdatedAt { get; set; }

        public bool HasAnyValue => InfantBasePrice.HasValue || InfantTaxPrice.HasValue;
    }
}
