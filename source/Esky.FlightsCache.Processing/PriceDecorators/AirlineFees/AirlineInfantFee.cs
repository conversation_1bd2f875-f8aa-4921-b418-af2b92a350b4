using Esky.FlightsCache.CurrencyProvider;
using Esky.FlightsCache.MessageContract;
using System.Collections.Generic;
using System.Linq;

namespace Esky.FlightsCache.Processing.PriceDecorators.AirlineFees;

public abstract class AirlineInfantFee : IFeeCalculator
{
    private const string _fallbackCurrency = "EUR";
    private readonly ICurrencyRatioProvider _currencyRatioProvider;
    private readonly IAirlineFeeCache _feeCache;

    protected AirlineInfantFee(ICurrencyRatioProvider currencyRatioProvider, IAirlineFeeCache feeCache)
    {
        _currencyRatioProvider = currencyRatioProvider;
        _feeCache = feeCache;
    }

    public abstract IEnumerable<string> AirlineCodes { get; }

    public void Apply(FlightCache flight)
    {
        if (flight.Legs.Any(leg => !leg.InfantPrices.IsNullOrEmpty()))
            return;

        var airline = flight.Legs[0].AirlineCode;
        var currency = flight.Legs[0].CurrencyCode;
        var infantPrice = _feeCache.GetInfantFee(airline, currency, flight.ProviderCode)
                          ?? _feeCache.GetCheapestInfantFee(airline, currency)
                          ?? GetFallbackCurrencyPrice(currency, _fallbackCurrency);

        if (infantPrice is null)
            return;

        foreach (var leg in flight.Legs)
        {
            leg.InfantPrices ??= [];
            leg.InfantPrices.Add(infantPrice);
        }

        return;

        PriceCacheEntry GetFallbackCurrencyPrice(string targetCurrency, string fallbackCurrency)
        {
            if (targetCurrency == fallbackCurrency) return null;
            
            var infantPriceInEur = _feeCache.GetInfantFee(airline, fallbackCurrency, flight.ProviderCode) ?? _feeCache.GetCheapestInfantFee(airline, fallbackCurrency);
            if (infantPriceInEur is null) return null;
            
            var ratio = _currencyRatioProvider.GetRatio(fallbackCurrency, targetCurrency);
            return new PriceCacheEntry(infantPriceInEur.BasePrice * ratio, infantPriceInEur.TaxPrice * ratio);
        }
    }
}