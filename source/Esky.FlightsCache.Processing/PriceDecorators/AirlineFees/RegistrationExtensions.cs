using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace Esky.FlightsCache.Processing.PriceDecorators.AirlineFees;


public static class RegistrationExtensions
{
    public static IServiceCollection AddAirlineFees(this IServiceCollection services, IConfiguration configuration)
    {
        var cs = configuration.GetValue<string>("FlightsCacheConfiguration:ConnectionUrl");
        var collection = configuration.GetValue<string>("FlightsCacheConfiguration:AirlineFeesCollectionName");
        services.AddSingleton<IAirlineFeesStorage>(_ => new MongoAirlineFeesStorage(cs, collection));
        services.AddSingleton<IAirlineFeeCache, AirlineFeeCache>();
        services.AddSingleton<AirlineFeeCacheUpdater>();
        services.AddSingleton<IAirlineFeeCacheUpdater>(sp => sp.GetRequiredService<AirlineFeeCacheUpdater>());
        services.AddHostedService(sp => sp.GetRequiredService<AirlineFeeCacheUpdater>());
        
        return services;
    }
}