using MongoDB.Bson.Serialization.Attributes;
using MongoDB.Driver;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Esky.FlightsCache.Processing.PriceDecorators.AirlineFees;

public interface IAirlineFeesStorage
{
    Task<IReadOnlyCollection<AirlineFeeModel>> GetAll(CancellationToken cancellationToken);
    Task Update(IEnumerable<AirlineFeeModel> prices, CancellationToken cancellationToken);
}

public class MongoAirlineFeesStorage : IAirlineFeesStorage
{
    private readonly IMongoCollection<AirlineFee> _feeCollection;

    public MongoAirlineFeesStorage(string connectionString, string collectionName)
    {
        var url = MongoUrl.Create(connectionString);
        _feeCollection = new MongoClient(url)
            .GetDatabase(url.DatabaseName)
            .GetCollection<AirlineFee>(collectionName);
    }
    
    public async Task<IReadOnlyCollection<AirlineFeeModel>> GetAll(CancellationToken cancellationToken)
    {
        var fees = await _feeCollection
            .Find(FilterDefinition<AirlineFee>.Empty)
            .ToListAsync(cancellationToken);

        return fees
            .Select(
                e => new AirlineFeeModel(e.Id.Airline, e.Id.Currency)
                {
                    ProviderFees = e.ProviderFees.ToDictionary(
                        p => p.Key,
                        p => new AirlineFeeModel.ProviderFee
                        {
                            UpdatedAt = p.Value.UpdatedAt,
                            InfantBasePrice = p.Value.InfantBasePrice,
                            InfantTaxPrice = p.Value.InfantTaxPrice
                        }
                    )
                }
            )
            .ToList();
    }

    public async Task Update(IEnumerable<AirlineFeeModel> prices, CancellationToken cancellationToken)
    {
        var updates = prices.Select(
            m =>
            {
                var updatedAt = m.ProviderFees.Max(p => p.Value.UpdatedAt);
                var filter = Builders<AirlineFee>.Filter.Eq(e => e.Id.Currency, m.Currency)
                             & Builders<AirlineFee>.Filter.Eq(e => e.Id.Airline, m.Airline)
                             & Builders<AirlineFee>.Filter.Lt(e => e.UpdatedAt, updatedAt);
                var update = Builders<AirlineFee>.Update.Set(e => e.UpdatedAt, updatedAt);

                foreach (var (provider, fee) in m.ProviderFees)
                {
                    FieldDefinition<AirlineFee, ProviderFee> providerFeeField = $"{nameof(AirlineFee.ProviderFees)}.{provider}";
                    if (fee.HasAnyValue)
                    {
                        var providerFee = new ProviderFee
                        {
                            InfantBasePrice = fee.InfantBasePrice ?? 0,
                            InfantTaxPrice = fee.InfantTaxPrice ?? 0,
                            UpdatedAt = fee.UpdatedAt
                        };
                        update = update.Set(providerFeeField, providerFee);
                    }
                    else
                    {
                        update = update.Unset(providerFeeField);
                    }
                }

                return new UpdateOneModel<AirlineFee>(filter, update) { IsUpsert = true };
            }
        );

        try
        {
            await _feeCollection.BulkWriteAsync(updates, new BulkWriteOptions { IsOrdered = false }, cancellationToken);
        }
        catch (MongoBulkWriteException ex) when (ex.WriteErrors.All(e => e.Category == ServerErrorCategory.DuplicateKey))
        {
            // we can discard because other instance applied its changes
        }
    }
    
    [BsonIgnoreExtraElements]
    private record AirlineFee
    {
        [BsonId] public required Id Id { get; init; }
        public Dictionary<int, ProviderFee> ProviderFees { get; set; }
        public DateTime UpdatedAt { get; set; }
    }
    
    [BsonIgnoreExtraElements]
    private record Id
    {
        [BsonIgnoreIfDefault]
        public string Currency { get; init; }
        public string Airline { get; set; }
    }

    [BsonIgnoreExtraElements]
    private record ProviderFee
    {
        [BsonIgnoreIfDefault]
        public decimal InfantBasePrice { get; init; }
        [BsonIgnoreIfDefault]
        public decimal InfantTaxPrice { get; init; }
        public DateTime UpdatedAt { get; set; }
    }
}