using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Concurrent;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Esky.FlightsCache.Processing.PriceDecorators.AirlineFees;

public interface IAirlineFeeCacheUpdater
{
    void UpdateInfantFee(string airline, string currency, int provider, decimal? basePrice, decimal? taxPrice);
}

public class AirlineFeeCacheUpdater : BackgroundService, IAirlineFeeCacheUpdater
{
    private readonly ILogger<AirlineFeeCacheUpdater> _logger;
    private readonly IAirlineFeesStorage _feeStorage;
    private readonly IAirlineFeeCache _feeCache;
    private readonly PeriodicTimer _timer;
    private readonly ConcurrentDictionary<Key, Value> _infantPrices = new();

    public AirlineFeeCacheUpdater(ILogger<AirlineFeeCacheUpdater> logger, IAirlineFeesStorage feeStorage, IAirlineFeeCache feeCache)
    {
        _logger = logger;
        _feeStorage = feeStorage;
        _feeCache = feeCache;
        _timer = new PeriodicTimer(TimeSpan.FromHours(3));
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        do
        {
            await FlushLocalFees(stoppingToken);
            var fees = await _feeStorage.GetAll(stoppingToken);
            _feeCache.ReplaceAll(fees);
            _logger.LogInformation("Updated AirlineFeeCache: {Count}", fees.Sum(f => f.ProviderFees.Keys.Count));
        } while (await _timer.WaitForNextTickAsync(stoppingToken));
    }
    
    public void UpdateInfantFee(string airline, string currency, int provider, decimal? basePrice, decimal? taxPrice)
    {
        _infantPrices[new Key(airline, currency, provider)] = new Value(basePrice, taxPrice, DateTime.UtcNow);
    }

    public override async Task StopAsync(CancellationToken cancellationToken)
    {
        await FlushLocalFees(CancellationToken.None);
        await base.StopAsync(cancellationToken);
    }

    private async Task FlushLocalFees(CancellationToken cancellationToken)
    {
        if (_infantPrices.IsEmpty) return;

        try
        {
            var fees = _infantPrices
                .GroupBy(e => e.Key)
                .Select(
                    group => new AirlineFeeModel(group.Key.Airline, group.Key.Currency)
                    {
                        ProviderFees = group.ToDictionary(
                            e => e.Key.Provider,
                            e => new AirlineFeeModel.ProviderFee
                            {
                                UpdatedAt = e.Value.UpdatedAt,
                                InfantBasePrice = e.Value.InfantBasePrice,
                                InfantTaxPrice = e.Value.InfantTaxPrice,
                            }
                        )
                    }
                );
            await _feeStorage.Update(fees, cancellationToken);
            _infantPrices.Clear();
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Could not save fees");
        }
    }

    private readonly record struct Key(string Airline, string Currency, int Provider);
    private readonly record struct Value(decimal? InfantBasePrice, decimal? InfantTaxPrice, DateTime UpdatedAt);
}