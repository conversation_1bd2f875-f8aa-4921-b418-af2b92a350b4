using Esky.FlightsCache.CurrencyProvider;
using Esky.FlightsCache.Processing.PriceDecorators.AirlineFees;
using System.Collections.Generic;

namespace Esky.FlightsCache.Processing.PriceDecorators.Wizzair;

public class WizzairInfantFee : AirlineInfantFee, IFeeCalculator
{
    public WizzairInfantFee(ICurrencyRatioProvider currencyRatioProvider, IAirlineFeeCache feeCache)
        : base(currencyRatioProvider, feeCache)
    {
    }

    public override IEnumerable<string> AirlineCodes => Wizzair.Airlines;
}