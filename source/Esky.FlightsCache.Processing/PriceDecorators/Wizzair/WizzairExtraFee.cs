using Esky.FlightsCache.CurrencyProvider;
using System.Collections.Generic;
using System.Linq;

namespace Esky.FlightsCache.Processing.PriceDecorators.Wizzair;

public class WizzairExtraFee(ICurrencyRatioProvider currencyRatioProvider) : BaseExtraFee(currencyRatioProvider)
{
    protected override int ExtraFee => 3;
    protected override string ExtraFeeCurrency => "EUR";
    protected override int NumberOfPaxesThreshold => 3;

    public override IEnumerable<string> AirlineCodes => Wizzair.Airlines.Except(["5W"]);
}