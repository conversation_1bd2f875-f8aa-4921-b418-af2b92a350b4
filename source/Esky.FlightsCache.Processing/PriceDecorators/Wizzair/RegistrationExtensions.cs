using Microsoft.Extensions.DependencyInjection;

namespace Esky.FlightsCache.Processing.PriceDecorators.Wizzair;

public static class RegistrationExtensions
{
    public static IServiceCollection AddWizzairFee(this IServiceCollection services)
    {
        services.AddSingleton<IFeeCalculator, WizzairUpdateInfantFee>();
        services.AddSingleton<IFeeCalculator, WizzairInfantFee>();
        services.AddSingleton<IFeeCalculator, WizzairExtraFee>();
        return services;
    }
}