using Esky.FlightsCache.Processing.PriceDecorators.AirlineFees;
using System.Collections.Generic;

namespace Esky.FlightsCache.Processing.PriceDecorators.Wizzair;

public class WizzairUpdateInfantFee : AirlineUpdateInfantFee, IFeeCalculator
{
    public WizzairUpdateInfantFee(IAirlineFeeCacheUpdater feeUpdater) : base(feeUpdater)
    {
    }

    public override IEnumerable<string> AirlineCodes => Wizzair.Airlines;
}