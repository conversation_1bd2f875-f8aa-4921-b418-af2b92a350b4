using Esky.FlightsCache.CurrencyProvider;
using Esky.FlightsCache.Processing.PriceDecorators.AirlineFees;
using System.Collections.Generic;

namespace Esky.FlightsCache.Processing.PriceDecorators.Jet2;

public class Jet2InfantFee : AirlineInfant<PERSON><PERSON>, IFeeCalculator
{
    public Jet2InfantFee(ICurrencyRatioProvider currencyRatioProvider, IAirlineFeeCache feeCache)
        : base(currencyRatioProvider, feeCache)
    {
    }

    public override IEnumerable<string> AirlineCodes =>  Jet2.Airlines;
}
