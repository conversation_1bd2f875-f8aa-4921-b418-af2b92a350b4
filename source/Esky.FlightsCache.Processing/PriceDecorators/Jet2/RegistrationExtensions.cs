using Microsoft.Extensions.DependencyInjection;

namespace Esky.FlightsCache.Processing.PriceDecorators.Jet2;

public static class RegistrationExtensions
{
    public static IServiceCollection AddJet2Fee(this IServiceCollection services)
    {
        services.AddSingleton<IFeeCalculator, Jet2UpdateInfantFee>();
        services.AddSingleton<IFeeCalculator, Jet2ExtraFee>();
        services.AddSingleton<IFeeCalculator, Jet2InfantFee>();
        return services;
    }
}