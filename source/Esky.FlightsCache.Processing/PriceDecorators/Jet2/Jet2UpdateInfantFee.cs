using Esky.FlightsCache.Processing.PriceDecorators.AirlineFees;
using System.Collections.Generic;

namespace Esky.FlightsCache.Processing.PriceDecorators.Jet2;

public class Jet2UpdateInfantFee : AirlineUpdateInfantFee, IFeeCalculator
{
    public Jet2UpdateInfantFee(IAirlineFeeCacheUpdater feeUpdater) : base(feeUpdater)
    {
    }

    public override IEnumerable<string> AirlineCodes => Jet2.Airlines;
}