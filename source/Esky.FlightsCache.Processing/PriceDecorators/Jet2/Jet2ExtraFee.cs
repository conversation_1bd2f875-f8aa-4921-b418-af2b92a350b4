using Esky.FlightsCache.CurrencyProvider;
using System.Collections.Generic;

namespace Esky.FlightsCache.Processing.PriceDecorators.Jet2
{
    public class Jet2ExtraFee(ICurrencyRatioProvider currencyRatioProvider) : BaseExtraFee(currencyRatioProvider)
    {
        protected override int ExtraFee => 5;
        protected override string ExtraFeeCurrency => "GBP";
        protected override int NumberOfPaxesThreshold => 4;

        public override IEnumerable<string> AirlineCodes => Jet2.Airlines;
    }
}
