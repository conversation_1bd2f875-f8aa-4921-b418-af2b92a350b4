<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
    </PropertyGroup>


    <ItemGroup>
        <PackageReference Include="Google.Cloud.AIPlatform.V1" Version="2.14.0" />
        <PackageReference Include="Google.Cloud.BigQuery.V2" Version="3.4.0" />
        <PackageReference Include="IdGen" Version="3.0.7" />
        <PackageReference Include="system.reflection.metadata" Version="8.0.0" />
        <PackageReference Include="Microsoft.Extensions.DependencyModel" Version="8.0.0" />
        <PackageReference Include="Microsoft.Extensions.Caching.Abstractions" Version="8.0.0" />
        <PackageReference Include="Microsoft.Extensions.Options.ConfigurationExtensions" Version="8.0.0" />
        <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
        <PackageReference Include="Scrutor" Version="4.1.0" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\Esky.FlightsCache.CurrencyProvider\Esky.FlightsCache.CurrencyProvider.csproj" />
        <ProjectReference Include="..\Esky.FlightsCache.Database\Esky.FlightsCache.Database.csproj" />
        <ProjectReference Include="..\Esky.FlightsCache.MessageContract\Esky.FlightsCache.MessageContract.csproj" />
        <ProjectReference Include="..\Esky.FlightsCache.PartnerSettings\Esky.FlightsCache.PartnerSettings.csproj" />
        <ProjectReference Include="..\Esky.FlightsCache.ProviderMapping\Esky.FlightsCache.ProviderMapping.csproj" />
    </ItemGroup>

</Project>
