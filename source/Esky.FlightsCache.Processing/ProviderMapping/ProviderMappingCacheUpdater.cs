using Esky.FlightsCache.Database.Configuration;
using Esky.FlightsCache.ProviderMapping;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace Esky.FlightsCache.Processing.ProviderMapping
{
    public class ProviderMappingCacheUpdater : IHostedService
    {
        private readonly IFlightsCacheProviderConverter _fpcConverter;
        private readonly ILogger<ProviderMappingCacheUpdater> _logger;
        private readonly IConfigurationDatabase _providerMappingDatabase;
        private Timer _timer;

        public ProviderMappingCacheUpdater(
            ILogger<ProviderMappingCacheUpdater> logger,
            IFlightsCacheProviderConverter fpcConverter,
            IConfigurationDatabase providerMappingDatabase
        )
        {
            _logger = logger;
            _fpcConverter = fpcConverter;
            _providerMappingDatabase = providerMappingDatabase;
        }

        public Task StartAsync(CancellationToken cancellationToken)
        {
            _logger.LogInformation("ProviderCodeMappingCacheUpdater Hosted Service running.");

            _timer = new Timer(UpdateCache, null, TimeSpan.Zero, TimeSpan.FromMinutes(10));

            return Task.CompletedTask;
        }

        public Task StopAsync(CancellationToken cancellationToken)
        {
            _logger.LogInformation("ProviderCodeMappingCacheUpdater is stopping.");
            _timer?.Change(Timeout.Infinite, 0);
            return Task.CompletedTask;
        }

        private async void UpdateCache(object state)
        {
            try
            {
                _fpcConverter.UpdateInternalMapping(await _providerMappingDatabase.GetList());
                _logger.LogInformation("ProviderCodeMapping cache updated.");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Unable to update ProviderCodeMapping cache: {Message}", ex.Message);
            }
        }
    }
}