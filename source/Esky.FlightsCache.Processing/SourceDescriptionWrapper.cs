using Esky.FlightsCache.MessageContract;
using Esky.FlightsCache.ResultFilters.Contract;

namespace Esky.FlightsCache.Processing
{
    internal class SourceDescriptionWrapper : IFlightResultContext
    {
        private const string _searchSourceName = "flightsearch-api";
        private readonly SourceDescription _sourceDescription;

        public SourceDescriptionWrapper(SourceDescription sourceDescription)
        {
            _sourceDescription = sourceDescription;
        }

        string IFlightResultContext.DepartureCode => _sourceDescription.SearchDepartureCode;

        string IFlightResultContext.ArrivalCode => _sourceDescription.SearchArrivalCode;

        string IFlightResultContext.PaxConfiguration => _sourceDescription.PaxConfiguration;

        public bool IsFromRobots => !_sourceDescription.Name.StartsWith(_searchSourceName);
    }
}