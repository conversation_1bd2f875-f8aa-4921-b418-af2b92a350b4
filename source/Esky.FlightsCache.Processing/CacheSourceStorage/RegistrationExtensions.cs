using Esky.FlightsCache.Processing.BQ;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace Esky.FlightsCache.Processing.CacheSourceStorage
{
    public static class RegistrationExtensions
    {
        public static void ConfigureCacheSourceStorage(this IServiceCollection services, IConfiguration configuration)
        {
            services.Configure<BigQueryOptions>(configuration.GetSection(nameof(BigQueryOptions)))
                .AddSingleton<ICacheSourceStorage, CacheSourceStorage>();
        }
    }
}