using Esky.FlightsCache.Database;
using Esky.FlightsCache.MessageContract;
using Esky.FlightsCache.Processing.BQ;
using Google.Apis.Auth.OAuth2;
using Google.Apis.Bigquery.v2.Data;
using Google.Cloud.BigQuery.V2;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using System.IO;
using System.Threading.Tasks;

namespace Esky.FlightsCache.Processing.CacheSourceStorage
{
    public interface ICacheSourceStorage
    {
        Task AddSource(CacheRequest request, long sourceId);
    }

    public class CacheSourceStorage : ICacheSourceStorage
    {
        private readonly ILogger<CacheSourceStorage> _logger;
        private readonly Lazy<BigQueryTable> _table;

        public CacheSourceStorage(IOptions<BigQueryOptions> options,
            ILogger<CacheSourceStorage> logger)
        {
            _logger = logger;
            _table = new Lazy<BigQueryTable>(() =>
            {
                var credentials = File.ReadAllText(options.Value.CredentialsPath);
                var client = BigQueryClient.Create(options.Value.ProjectId, GoogleCredential.FromJson(credentials));
                var dataset = client.GetOrCreateDataset("PriceTrace");
                return dataset.GetOrCreateTable("CacheSources", new Table());
            });
        }

        public async Task AddSource(CacheRequest request, long sourceId)
        {
            using var activity = Activities.Source.StartActivity();
            try
            {
                var row = new BigQueryInsertRow
                {
                    { "Id", sourceId },
                    { "Name", request.SourceDescription.Name },
                    { "DepartureCode", request.SourceDescription.SearchDepartureCode },
                    { "ArrivalCode", request.SourceDescription.SearchArrivalCode },
                    { "DepartureDay", request.SourceDescription.SearchDepartureDate.ToString("yyyy-MM-dd")},
                    { "SendDate", request.SourceDescription.SendDate?.ToString("yyyy-MM-dd HH:mm:ss.fff") },
                    { "MachineName", request.SourceDescription.MachineName },
                    { "Provider", request.SourceDescription.Provider },
                    { "PartnerCode", request.SourceDescription.PartnerCode },
                    { "PaxConfiguration", request.SourceDescription.PaxConfiguration },
                    { "GroupName", request.CommandOptions.GroupName },
                    { "SessionId", request.SourceDescription.SessionId },
                    { "Flex", request.SourceDescription.Flex },
                    { "CreationDate", DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss.fff") }
                };

                if (request.SourceDescription.SearchReturnDepartureDate.HasValue
                    && request.SourceDescription.SearchReturnDepartureDate.Value != DateTime.MinValue)
                {
                    row.Add("ReturnDepartureDay",
                        request.SourceDescription.SearchReturnDepartureDate.Value.ToString("yyyy-MM-dd"));
                }

                await _table.Value.InsertRowAsync(row);
            }
            catch (Exception ex)
            {
                _logger.LogError(0, ex, "[{SourceId}] Exception message: {Message}", sourceId, ex.Message);
            }
        }
    }
}