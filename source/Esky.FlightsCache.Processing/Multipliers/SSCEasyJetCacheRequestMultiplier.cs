using Esky.FlightsCache.CurrencyProvider;
using Esky.FlightsCache.CurrencyProvider.Types;
using Esky.FlightsCache.MessageContract;
using Esky.Framework.PartnerSettings.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;

namespace Esky.FlightsCache.Processing.Multipliers
{
    public class SSCEasyJetCacheRequestMultiplier : ICacheRequestMultiplier
    {
        private const int _atlasDaysForward = 90;
        private const string _supplierName = "easyjet";
        private static readonly IReadOnlyDictionary<string, Money> _travelFusionCustomMarkup = new Money[]
        {
            new(12.00m, "GBP"),
            new(14.28m, "EUR"),
            new(162.2m, "SEK"),
            new(154.19m, "MAD"),
            new(61.05m, "PLN"),
            new(13.56m, "CHF"),
            new(358.18m, "CZK"),
            new(106.49m, "DKK"),
            new(5635m, "HUF")
        }.ToDictionary(x => x.CurrencyCode);

        private readonly ICurrencyRatioProvider _currencyRatioProvider;

        public SSCEasyJetCacheRequestMultiplier(ICurrencyRatioProvider currencyRatioProvider)
        {
            _currencyRatioProvider = currencyRatioProvider;
        }

        public Task<CacheRequest[]> Map(CacheRequest request)
        {
            var serialized = JsonSerializer.Serialize(request);

            //Atlas
            var atlasRequest = CreateCacheRequest(serialized, ProviderCodeEnum.Atlas);
            atlasRequest.Flights = atlasRequest.Flights?.Where(x => x.Legs[0].DepartureDate.Date <= DateTime.Today.AddDays(_atlasDaysForward)).ToList();

            //TravelFusion
            var travelFusionRequest = CreateCacheRequest(serialized, ProviderCodeEnum.TravelFusion);

            foreach (var flight in travelFusionRequest.Flights)
            {
                foreach (var leg in flight.Legs)
                {
                    Money customMarkup = TryGetMarkupInCurrency(leg.CurrencyCode);
                    customMarkup = Convert(customMarkup, leg.CurrencyCode);

                    AddMarkup(leg.AdultPrices, customMarkup);
                    AddMarkup(leg.YouthPrices, customMarkup);
                    AddMarkup(leg.ChildPrices, customMarkup);
                }
            }
            var result = new CacheRequest[] { atlasRequest, travelFusionRequest };

            return Task.FromResult(result);
        }

        private static Money TryGetMarkupInCurrency(string currencyCode)
        {
            if(!_travelFusionCustomMarkup.ContainsKey(currencyCode))
            {
                currencyCode = "GBP";
            }
            return _travelFusionCustomMarkup[currencyCode];
        }

        private static CacheRequest CreateCacheRequest(string serialized, ProviderCodeEnum provider)
        {
            var result = JsonSerializer.Deserialize<CacheRequest>(serialized);
            result.CommandOptions.DeleteOptions.ProviderCodes = [(int)provider];
            result.SourceDescription.Provider = ((int)provider).ToString();
            result.SourceDescription.Supplier = _supplierName;

            foreach (var flight in result.Flights)
            {
                flight.ProviderCode = (int)provider;
                flight.Supplier = _supplierName;
            }

            return result;
        }

        private void AddMarkup(List<PriceCacheEntry> prices, Money customMarkup)
        {
            if (prices is null) return;
            foreach (var price in prices)
            {
                price.BasePrice = price.BasePrice + customMarkup.Amount;
            }
        }

        private Money Convert(Money source, string targetCurrency)
        {
            if (source.CurrencyCode.Equals(targetCurrency, StringComparison.OrdinalIgnoreCase))
            {
                return source;
            }
            var exchangeRate = _currencyRatioProvider.GetRatio(source.CurrencyCode, targetCurrency);
            var targetAmount = source.Amount * exchangeRate;

            return new Money(targetAmount, targetCurrency);
        }
    }
}