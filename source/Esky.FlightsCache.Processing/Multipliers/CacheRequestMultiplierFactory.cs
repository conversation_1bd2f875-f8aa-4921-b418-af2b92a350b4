using Microsoft.Extensions.DependencyInjection;
using System;

namespace Esky.FlightsCache.Processing.Multipliers;

public interface ICacheRequestMultiplierFactory
{
    ICacheRequestMultiplier Create(string providerCode, string supplier);
}

public class CacheRequestMultiplierFactory : ICacheRequestMultiplierFactory
{
    private readonly IServiceProvider _serviceProvider;

    public CacheRequestMultiplierFactory(IServiceProvider serviceProvider)
    {
        _serviceProvider = serviceProvider;
    }

    public ICacheRequestMultiplier Create(string providerCode, string supplier)
    {
        return (providerCode, supplier) switch
        {
            ("6", "easyjet") => _serviceProvider.GetRequiredService<SSCEasyJetCacheRequestMultiplier>(),
            _ => _serviceProvider.GetRequiredService<DisabledCacheRequestMultiplier>()
        };
    }
}