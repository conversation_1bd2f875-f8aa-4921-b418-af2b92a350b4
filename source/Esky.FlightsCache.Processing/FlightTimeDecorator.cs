using Esky.FlightsCache.MessageContract;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System;

namespace Esky.FlightsCache.Processing;

public class FlightTimeDecorator : ICacheRequestFlightsDecorator
{
    private const string _utcTimeZoneName = "UTC";
    private const string _defaultTimeZone = "Central European Standard Time";

    public Task Decorate(IEnumerable<FlightCache> flights)
    {
        foreach (var leg in flights.SelectMany(x => x.Legs))
        {
            leg.FlightTime = CalculateFlightTime(
                leg.DepartureAirportDetails.TimeZoneId ?? _defaultTimeZone,
                leg.DepartureDate,
                leg.ArrivalAirportDetails.TimeZoneId ?? _defaultTimeZone,
                leg.ArrivalDate);
        }

        return Task.CompletedTask;
    }

    private static TimeSpan CalculateFlightTime(string departureTimeZoneId, DateTime departureLocalTime, string arrivalTimeZoneId, DateTime arrivalLocalTime)
    {
        if (departureLocalTime.TimeOfDay == TimeSpan.Zero && arrivalLocalTime.TimeOfDay == TimeSpan.Zero)
        {
            return TimeSpan.Zero;
        }

        departureLocalTime = DateTime.SpecifyKind(departureLocalTime, DateTimeKind.Unspecified);
        arrivalLocalTime = DateTime.SpecifyKind(arrivalLocalTime, DateTimeKind.Unspecified);

        try
        {
            var arrivalDateInUtc = TimeZoneInfo.ConvertTimeBySystemTimeZoneId(arrivalLocalTime, arrivalTimeZoneId, _utcTimeZoneName);
            var departureDateInUtc = TimeZoneInfo.ConvertTimeBySystemTimeZoneId(departureLocalTime, departureTimeZoneId, _utcTimeZoneName);

            return arrivalDateInUtc - departureDateInUtc;
        }
        catch
        {
            return arrivalLocalTime - departureLocalTime;
        }
    }
}