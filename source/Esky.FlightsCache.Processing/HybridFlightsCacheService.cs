using Esky.FlightsCache.Database;
using Esky.FlightsCache.Database.FlightOffers;
using Esky.FlightsCache.Database.Sql;
using Esky.FlightsCache.MessageContract;
using Esky.FlightsCache.Processing.CacheSourceStorage;
using Esky.FlightsCache.ProviderMapping;
using Esky.FlightsCache.ResultFilters.FlightListFilters.Global;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;

namespace Esky.FlightsCache.Processing
{
    public class HybridFlightsCacheService(
        IFlightOffersRepository flightOffersRepository,
        IFlightsCacheDatabase flightsCacheDatabase,
        IEnumerable<IFlightListFilter> flightListFilters,
        ILogger<HybridFlightsCacheService> logger,
        IFlightsCacheProviderConverter providerConverter,
        ICacheSourceStorage cacheSourceStorage,
        ISourceIdGenerator sourceIdGenerator)
        : IFlightsCacheService
    {
        public async Task StoreFlights(CacheRequest cacheRequest)
        {
            using var activity = Activities.Source.StartActivity();
            var startTimestamp = Stopwatch.GetTimestamp();
            //uncomment when PAT uses sources from BQ instead of SQL
            //var sourceId = sourceIdGenerator.GenerateSourceId();
            var sourceId = await flightsCacheDatabase.GenerateSourceIdAsync(cacheRequest);
            _ = cacheSourceStorage.AddSource(cacheRequest, sourceId);

            try
            {
                var context = new SourceDescriptionWrapper(cacheRequest.SourceDescription);
                var totalSkipped = 0;

                foreach (var filter in flightListFilters.Where(f =>
                             !cacheRequest.CommandOptions.SkipDataFiltering || f is AirportCodeFlightListFilter))
                {
                    using (var filterActivity = Activities.Source.StartActivity(filter.GetType().Name))
                    {
                        var filterResult = await filter.Filter(cacheRequest.Flights, context);

                        int skipped = cacheRequest.Flights.Count - filterResult.Flights.Count;
                        filterActivity?.SetTag("skipped", skipped);
                        totalSkipped += skipped;
                        cacheRequest.Flights = filterResult.Flights;

                        if (!string.IsNullOrEmpty(filterResult.StatisticsMessage))
                        {
                           logger.LogDebug("[{SourceId}] Filter skip messages: {Message}", sourceId, filterResult.StatisticsMessage);
                        }

                        if (!string.IsNullOrEmpty(filterResult.ErrorMessage))
                        {
                            logger.LogInformation("[{SourceId}] Filter error messages: {Message}", sourceId,
                                filterResult.ErrorMessage);
                        }
                    }
                }

                var flights = cacheRequest.Flights.SplitFlights();

                var isDateRangeFeed = cacheRequest.CommandOptions.DeleteOptions.IsEnabled && cacheRequest.CommandOptions.DeleteOptions.Type == TypeOfDisposalEnum.DateRange;

                await flightOffersRepository.SaveFlightOffersAsync(flights, sourceId, cacheRequest);
                
                if (cacheRequest.CommandOptions.SkipSQLSave)
                {
                    ProcessingMetrics.SqlFlightsSkipped.Add(flights.Count);
                }
                else
                {
                    await flightsCacheDatabase.StoreFlights(flights.Select(x => x as FlightCache).ToList(), sourceId, isDateRangeFeed);

                    if (cacheRequest.CommandOptions.DeleteOptions.IsEnabled)
                    {
                        var providerCodesToDelete = cacheRequest.CommandOptions.DeleteOptions.ProviderCodes ?? flights.Select(f => f.ProviderCode);
                        var cacheProviderCodesToDelete = providerConverter.ToFCProviderCodes(
                            providerCodesToDelete.Distinct().ToList(),
                            cacheRequest.CommandOptions.DeleteOptions.AirlineCodes
                        );

                        try
                        {
                            await DeleteOldCacheRecordsAsync(cacheRequest, sourceId, cacheProviderCodesToDelete);
                        }
                        catch (Exception ex)
                        {
                            logger.LogWarning(ex, "Could not delete old cache records");
                        }
                    }
                }

                ProcessingMetrics.AddSourceFlightsCount(cacheRequest.SourceDescription.Name, cacheRequest.Flights.Count);
                ProcessingMetrics.FlightsCount.Add(flights.Count);
                ProcessingMetrics.FlightsFiltered.Add(totalSkipped);
                ProcessingMetrics.StoreFlightsTotalTime.Record((int)Stopwatch.GetElapsedTime(startTimestamp).TotalMilliseconds);

                foreach (var g in flights.GroupBy(f => f.ProviderCode))
                {
                    ProcessingMetrics.ProviderCounter(g.Key, g.Count());
                }

                foreach (var g in flights.GroupBy(f => f.Legs.First().Segments.First().AirlineCode))
                {
                    ProcessingMetrics.AirlineCounter(g.Key, g.Count());
                }
            }
            catch (InvalidPriceException ex)
            {
                logger.LogWarning(ex, "[{SourceId}] Invalid price", sourceId);
            }
            catch (Exception ex)
            {
                logger.LogError(0, ex, "[{SourceId}] Exception message: {Message}", sourceId, ex.Message);
            }
        }

        public Task<long> RemoveFromCacheBySourceId(List<long> sourceIds)
        {
            return flightOffersRepository.RemoveBySourceIds(sourceIds);
        }

        private async Task DeleteOldCacheRecordsAsync(CacheRequest cacheRequest, long sourceId, List<int> providerCodes)
        {
            using var activity = Activities.Source.StartActivity();
            var commandOptions = cacheRequest.CommandOptions;
            var sourceDescription = cacheRequest.SourceDescription;
            var isOW = cacheRequest.Flights.FirstOrDefault()?.Legs.First().SeparationOptions.Options.HasFlag(SeparationOptionEnum.OneWay) ?? true;

            _ = commandOptions.DeleteOptions.Type switch
            {
                TypeOfDisposalEnum.DateRange => await flightsCacheDatabase.DeleteOldCacheRecordsAsync(sourceDescription, commandOptions, providerCodes, sourceId, isOW),
                TypeOfDisposalEnum.EachFlight => throw new NotSupportedException(nameof(TypeOfDisposalEnum.EachFlight)),
                _ => throw new ArgumentOutOfRangeException(nameof(TypeOfDisposalEnum))
            };
        }
    }
}