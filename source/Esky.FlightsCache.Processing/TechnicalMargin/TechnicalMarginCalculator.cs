#nullable enable
using Esky.FlightsCache.CurrencyProvider;
using Esky.FlightsCache.MessageContract;
using Esky.FlightsCache.Processing.PriceDecorators;
using Esky.FlightsCache.ProviderMapping;
using System.Collections.Generic;
using System.Linq;

namespace Esky.FlightsCache.Processing.TechnicalMargin;

public interface IMarginApplicationStrategy
{
    void ApplyMargin(PriceCacheEntry price, string priceCurrency, TechnicalMarginConfiguration margin);
}

public class BaseMarginStrategy(ICurrencyRatioProvider currencyRatioProvider) : IMarginApplicationStrategy
{
    public void ApplyMargin(PriceCacheEntry price, string priceCurrency, TechnicalMarginConfiguration margin)
    {
        var marginAmount = 0m;
        if (margin.Margin.MarginType == MarginType.Absolute)
        {
            price.BasePrice += marginAmount = margin.Margin.Amount * currencyRatioProvider.GetRatio(margin.Margin.Currency, priceCurrency);
        }
        else if (margin.Margin.MarginType == MarginType.Relative)
        {
            var scaledMarginAmount = margin.Margin.Amount / 100;
            var basePriceMargin = price.BasePrice * scaledMarginAmount;
            var taxPriceMargin = price.TaxPrice * scaledMarginAmount;
            
            price.BasePrice += basePriceMargin;
            price.TaxPrice += taxPriceMargin;
            marginAmount = basePriceMargin + taxPriceMargin;
        }
        price.MarginIncluded = (price.MarginIncluded ?? 0) + marginAmount;
    }
}

public class PackageMarginStrategy(ICurrencyRatioProvider currencyRatioProvider) : IMarginApplicationStrategy
{
    public void ApplyMargin(PriceCacheEntry price, string priceCurrency, TechnicalMarginConfiguration margin)
    {
        if (margin.Margin.MarginType == MarginType.Absolute)
        {
            var convertedMargin = margin.Margin.Amount * currencyRatioProvider.GetRatio(margin.Margin.Currency, priceCurrency);
            price.PackagesAdditionalMargin = (price.PackagesAdditionalMargin ?? 0) + convertedMargin;
        }
        else if (margin.Margin.MarginType == MarginType.Relative)
        {
            var scaledMarginAmount = margin.Margin.Amount / 100;
            // Package margins include already accumulated package margins in the calculation
            var totalMargin = (price.BasePrice + price.TaxPrice + (price.PackagesAdditionalMargin ?? 0)) * scaledMarginAmount;
            price.PackagesAdditionalMargin = (price.PackagesAdditionalMargin ?? 0) + totalMargin;
        }
    }
}

public static class PriceDictionaryManager
{
    public static Dictionary<int, PriceCacheEntry> CreateMissingPriceEntries(
        Dictionary<int, PriceCacheEntry> prices,
        TechnicalMarginConfiguration[] margins)
    {
        var result = new Dictionary<int, PriceCacheEntry>(prices);
        var passengerThresholds = margins
            .Select(x => x.FromNumberOfPassengers)
            .Distinct()
            .OrderBy(x => x);

        foreach (var threshold in passengerThresholds)
        {
            if (result.ContainsKey(threshold))
                continue;

            var basePriceEntry = FindClosestLowerPassengerCount(result, threshold);
            if (basePriceEntry.Key == 0)
                continue;

            result[threshold] = CreatePriceEntryForThreshold(basePriceEntry.Value, threshold);
        }

        return result;
    }

    private static KeyValuePair<int, PriceCacheEntry> FindClosestLowerPassengerCount(
        Dictionary<int, PriceCacheEntry> prices,
        int threshold)
    {
        return prices
            .Where(kvp => kvp.Key < threshold)
            .OrderByDescending(kvp => kvp.Key)
            .FirstOrDefault();
    }

    private static PriceCacheEntry CreatePriceEntryForThreshold(PriceCacheEntry baseEntry, int threshold)
    {
        return new PriceCacheEntry(
            basePrice: baseEntry.BasePrice,
            taxPrice: baseEntry.TaxPrice,
            minimumNumberOPaxes: threshold);
    }
}

public record struct PriceMarginContext(
    int providerCode,
    string? supplier,
    HashSet<string> airlines,
    int? totalSeatsLeft,
    string currencyCode,
    PassengerType passengerType = PassengerType.Adult);
    
public interface ITechnicalMarginCalculator
{
    List<PriceCacheEntry> GetPricesWithMargin(PriceMarginContext context, List<PriceCacheEntry> prices, List<PriceCacheEntry>? fallbackPrices = null);
}

public class TechnicalMarginCalculator : ITechnicalMarginCalculator
{
    private readonly ITechnicalMarginService _technicalMarginService;
    private readonly Dictionary<MarginContext, IMarginApplicationStrategy> _marginStrategies;

    public TechnicalMarginCalculator(
        ITechnicalMarginService technicalMarginService,
        ICurrencyRatioProvider currencyRatioProvider)
    {
        _technicalMarginService = technicalMarginService;
        _marginStrategies = new Dictionary<MarginContext, IMarginApplicationStrategy>
        {
            { MarginContext.Base, new BaseMarginStrategy(currencyRatioProvider) },
            { MarginContext.Packages, new PackageMarginStrategy(currencyRatioProvider) }
        };
    }
    
    public List<PriceCacheEntry> GetPricesWithMargin(PriceMarginContext context, List<PriceCacheEntry> prices, List<PriceCacheEntry>? fallbackPrices = null)
    {
        var margins = GetTechnicalMargins(context);
        prices = prices.Clone();
        if (margins.Length == 0)
            return prices;
        
        if (prices.IsNullOrEmpty() && !fallbackPrices.IsNullOrEmpty() && margins.Any(x => x.Passenger == context.passengerType))
        {
            prices = fallbackPrices?.Clone() ?? [];
        }

        var pricesDict = prices.ToDictionary(x => x.MinimumNumberOfPaxes, x => x);
        pricesDict = PriceDictionaryManager.CreateMissingPriceEntries(pricesDict, margins);

        ApplyMarginsInOrder(pricesDict, context.currencyCode, margins);

        return pricesDict.Values.ToList();
    }

    private TechnicalMarginConfiguration[] GetTechnicalMargins(PriceMarginContext context)
    {
        return _technicalMarginService.GetTechnicalMargins(
            context.providerCode,
            context.supplier,
            context.airlines,
            context.totalSeatsLeft,
            context.passengerType);
    }

    private void ApplyMarginsInOrder(
        Dictionary<int, PriceCacheEntry> pricesDict,
        string currencyCode,
        TechnicalMarginConfiguration[] margins)
    {
        var marginsByContext = margins
            .GroupBy(m => m.Context)
            .OrderBy(g => g.Key); // Base (1) before Packages (2)

        foreach (var contextGroup in marginsByContext)
        {
            var strategy = _marginStrategies[contextGroup.Key];
            var orderedMargins = contextGroup
                .OrderBy(x => x.FromNumberOfPassengers)
                .ThenBy(x => x.Passenger)  // All (0) before the rest
                .ThenBy(x => x.ProviderCode ?? int.MinValue)
                .ThenBy(x => x.Supplier == "*" ? 0 : 1)
                .ThenBy(x => x.AirlineCodes.Contains("*") ? 0 : 1);

            foreach (var margin in orderedMargins)
            {
                ApplyMarginToEligiblePrices(pricesDict, currencyCode, margin, strategy);
            }
        }
    }

    private static void ApplyMarginToEligiblePrices(
        Dictionary<int, PriceCacheEntry> pricesDict,
        string currencyCode,
        TechnicalMarginConfiguration margin,
        IMarginApplicationStrategy strategy)
    {
        var eligiblePassengerCounts = pricesDict.Keys
            .Where(k => k >= margin.FromNumberOfPassengers);

        foreach (var passengerCount in eligiblePassengerCounts)
        {
            var price = pricesDict[passengerCount];
            strategy.ApplyMargin(price, currencyCode, margin);
        }
    }
}