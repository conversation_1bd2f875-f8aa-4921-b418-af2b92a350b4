using Esky.FlightsCache.CurrencyProvider;
using Esky.FlightsCache.MessageContract;
using Esky.FlightsCache.Processing.TechnicalMargin;
using Esky.FlightsCache.Processing.Utils;
using Esky.FlightsCache.ProviderMapping;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Esky.FlightsCache.Processing
{
    public class ProviderCodeCacheRequestFlightsDecorator : ICacheRequestFlightsDecorator
    {
        private readonly Dictionary<MarginType, IPriceUpdateStrategy> _priceUpdateStrategies;
        private readonly IFlightsCacheProviderConverter _providerConverter;
        private readonly IMarginConfigurationService _marginConfigurationService;
        
        private readonly ITechnicalMarginCalculator _technicalMarginCalculator;

        public ProviderCodeCacheRequestFlightsDecorator(
            IFlightsCacheProviderConverter providerConverter,
            ICurrencyRatioProvider currencyRatioProvider,
            IMarginConfigurationService marginConfigurationService,
            ITechnicalMarginCalculator technicalMarginCalculator)
        {
            _providerConverter = providerConverter;
            _marginConfigurationService = marginConfigurationService;

            _priceUpdateStrategies = new Dictionary<MarginType, IPriceUpdateStrategy>
            {
                { MarginType.Absolute, new AbsolutePriceUpdateStrategy(currencyRatioProvider) },
                { MarginType.Relative, new RelativePriceUpdateStrategy() }
            };
            _technicalMarginCalculator = technicalMarginCalculator;
        }

        public Task Decorate(IEnumerable<FlightCache> flights)
        {
            foreach (var flight in flights)
            {
                var airlines = flight.Legs
                    .SelectMany(leg => leg.Segments.Select(segment => segment.AirlineCode))
                    .ToArray();

                //todo mb: remove
                {
                    // var margin =
                    //     _marginConfigurationService.GetMarginConfiguration(flight.ProviderCode, flight.Supplier,
                    //         airlines);
                    // ApplyMargin(flight, margin);
                }
                
                var configuration = _providerConverter.GetWriteConfiguration(flight.ProviderCode, airlines);
                flight.OriginalProviderCode = flight.ProviderCode; // store original provider code to get correct technical margins later
                if (configuration != null)
                {
                    flight.ProviderCode = configuration.CacheProviderCode;
                    flight.ReadProviderCode = configuration.ReadProviderCode;
                    flight.Supplier ??= configuration.Supplier;
                }
                else
                {
                    flight.ReadProviderCode = flight.ProviderCode;
                }
            }

            return Task.CompletedTask;
        }

        private void ApplyMargin(FlightCache flight, MarginConfiguration margin)
        {
            if (margin == null || margin.Amount == 0)
            {
                return;
            }

            foreach (var leg in flight.Legs)
            {
                _priceUpdateStrategies[margin.MarginType].UpdatePrices(leg, margin);
            }
        }
    }
}