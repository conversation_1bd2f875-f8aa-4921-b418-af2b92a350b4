using Esky.FlightsCache.Common;
using Esky.FlightsCache.Database;
using Esky.FlightsCache.MessageContract;
using Esky.FlightsCache.Processing.Multipliers;
using Esky.Framework.PartnerSettings.Enums;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Esky.FlightsCache.Processing
{
    public class SourceSettings
    {
        public Dictionary<string, bool> SourceDiscard { get; init; }

        public bool ShouldDiscard(string sourceName)
        {
            return sourceName is null || SourceDiscard?.GetValueOrDefault(sourceName) is true;
        }
    }

    public class CacheRequestProcessor : ICacheRequestProcessor
    {
        private readonly IEnumerable<ICacheRequestFlightsDecorator> _cacheRequestDecorators;
        private readonly CacheRequestProcessorConfiguration _configuration;
        private readonly IFlightsCacheService _flightsCacheService;
        private readonly ILogger<CacheRequestProcessor> _logger;
        private readonly ITimetableServiceFactory _timetableServiceFactory;
        private readonly ICacheRequestMultiplierFactory _cacheRequestMultiplierFactory;
        private readonly SourceSettings _sourceSettings;

        public CacheRequestProcessor(
            ILogger<CacheRequestProcessor> logger,
            IOptions<CacheRequestProcessorConfiguration> configuration,
            IEnumerable<ICacheRequestFlightsDecorator> cacheRequestDecorators,
            IFlightsCacheService flightsCacheService,
            ITimetableServiceFactory timetableServiceFactory,
            ICacheRequestMultiplierFactory cacheRequestMultiplierFactory,
            IOptions<SourceSettings> sourceSettings)
        {
            _logger = logger;
            _cacheRequestDecorators = cacheRequestDecorators;
            _configuration = configuration.Value;
            _flightsCacheService = flightsCacheService;
            _timetableServiceFactory = timetableServiceFactory;
            _cacheRequestMultiplierFactory = cacheRequestMultiplierFactory;
            _sourceSettings = sourceSettings.Value;
        }

        public async Task ProcessMessageAsync(CacheRequest data)
        {
            using var activity = Activities.Source.StartActivity();
            activity?.SetTag("FlightsCount", data.Flights.Count);
            activity?.SetTag("GroupName", data.CommandOptions.GroupName);
            activity?.SetTag("SourceName", data.SourceDescription.Name);
            activity?.SetTag("Provider", data.SourceDescription.Provider);

            if (_sourceSettings.ShouldDiscard(data.SourceDescription.Name))
            {
                activity?.SetTag("Discarded", true);
                return;
            }

            _logger.LogInformation(
                "Processing CacheRequest. Route: {SearchDepartureCode}-{SearchArrivalCode}, Date: {SearchDepartureDate}-{SearchReturnDepartureDate}, ID: {GroupName} / {PortionId}",
                data.SourceDescription.SearchDepartureCode,
                data.SourceDescription.SearchArrivalCode,
                data.SourceDescription.SearchDepartureDate.ToString("dd.MM"),
                data.SourceDescription.SearchReturnDepartureDate?.ToString("dd.MM"),
                data.CommandOptions.GroupName, data.CommandOptions.PortionId
            );

            await GenerateTimetable(data);

            var requests = await MultiplyRequest(data);

            foreach (var request in requests)
            {
                foreach (var flightsDecorator in _cacheRequestDecorators)
                {
                    using (Activities.Source.StartActivity(flightsDecorator.GetType().Name))
                    {
                        await flightsDecorator.Decorate(request.Flights);
                    }
                }

                if (!string.IsNullOrWhiteSpace(_configuration.TargetDbConnectionString))
                {
                    if (_logger.IsEnabled(LogLevel.Debug))
                    {
                        _logger.LogDebug("Saving to DB: {GroupName}", request.CommandOptions.GroupName);
                    }

                    await StoreFlightsInDbAsync(request);
                }
            }

            if (_logger.IsEnabled(LogLevel.Debug))
            {
                _logger.LogDebug("Processing finished: {GroupName}", data.CommandOptions.GroupName);
            }
        }

        private async Task<CacheRequest[]> MultiplyRequest(CacheRequest request)
        {
            var providerCode = request.SourceDescription.Provider;
            var supplier = request.SourceDescription.Supplier ?? request.Flights.FirstOrDefault()?.Supplier;

            var multiplierService = _cacheRequestMultiplierFactory.Create(providerCode, supplier);
            return await multiplierService.Map(request);
        }

        private async Task GenerateTimetable(CacheRequest cacheRequest)
        {
            using var activity = Activities.Source.StartActivity();

            try
            {
                var generateTimetableTasks = cacheRequest.Flights
                    .GroupBy(f => f.ProviderCode)
                    .Where(f => Enum.IsDefined(typeof(ProviderCodeEnum), f.Key))
                    .Select(f =>
                    {
                        var timetableService = _timetableServiceFactory.Create((ProviderCodeEnum)f.Key);
                        return timetableService.GenerateAsync(f.ToList());
                    });

                await Task.WhenAll(generateTimetableTasks);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error while generating timetable for cache request: {SessionId}.",
                    cacheRequest.SourceDescription.SessionId);
            }
        }

        private async Task StoreFlightsInDbAsync(CacheRequest cacheRequest)
        {
            try
            {
                await _flightsCacheService.StoreFlights(cacheRequest);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error while saving FlightsCache to DB: {Message}{Exception}{InnerException}",
                    ex.Message, ex, ex.InnerException);

                throw;
            }
        }
    }
}