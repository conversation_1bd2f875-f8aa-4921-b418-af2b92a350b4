using Esky.FlightsCache.MessageContract;
using Esky.FlightsCache.ProviderMapping;
using System.Collections.Generic;

namespace Esky.FlightsCache.Processing.Utils
{
    internal class RelativePriceUpdateStrategy : IPriceUpdateStrategy
    {
        public void UpdatePrices(FlightCacheLeg leg, MarginConfiguration margin)
        {
            var scaledMargin = margin.Amount / 100;

            UpdatePrices(leg.AdultPrices, scaledMargin);
            UpdatePrices(leg.ChildPrices, scaledMargin);
            UpdatePrices(leg.YouthPrices, scaledMargin);
            UpdatePrices(leg.InfantPrices, scaledMargin);
        }

        private void UpdatePrices(List<PriceCacheEntry> paxPrices, decimal scaledMargin)
        {
            if (paxPrices != null)
            {
                foreach (var price in paxPrices)
                {
                    price.BasePrice += price.BasePrice * scaledMargin;
                    price.TaxPrice += price.TaxPrice * scaledMargin;
                }
            }
        }
    }
}