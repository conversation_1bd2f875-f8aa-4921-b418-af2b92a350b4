using Esky.FlightsCache.CurrencyProvider;
using Esky.FlightsCache.MessageContract;
using Esky.FlightsCache.ProviderMapping;
using System.Collections.Generic;

namespace Esky.FlightsCache.Processing.Utils
{
    internal class AbsolutePriceUpdateStrategy : IPriceUpdateStrategy
    {
        private readonly ICurrencyRatioProvider _currencyRatioProvider;

        public AbsolutePriceUpdateStrategy(ICurrencyRatioProvider currencyRatioProvider)
        {
            _currencyRatioProvider = currencyRatioProvider;
        }

        public void UpdatePrices(FlightCacheLeg leg, MarginConfiguration margin)
        {
            var marginAmountInProviderCurrency =
                margin.Amount * _currencyRatioProvider.GetRatio(margin.Currency, leg.CurrencyCode);

            UpdatePrices(leg.AdultPrices, marginAmountInProviderCurrency);
            UpdatePrices(leg.ChildPrices, marginAmountInProviderCurrency);
            UpdatePrices(leg.YouthPrices, marginAmountInProviderCurrency);
            UpdatePrices(leg.InfantPrices, marginAmountInProviderCurrency);
        }


        private void UpdatePrices(List<PriceCacheEntry> paxPrices, decimal marginAmountInProviderCurrency)
        {
            if (paxPrices != null)
            {
                foreach (var price in paxPrices)
                {
                    price.BasePrice += marginAmountInProviderCurrency;
                }
            }
        }
    }
}