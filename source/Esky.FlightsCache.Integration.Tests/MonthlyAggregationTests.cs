using Esky.FlightsCache.Database.Aggregations;
using Esky.FlightsCache.Database.Aggregations.Model;
using Esky.FlightsCache.Database.Airports;
using Esky.FlightsCache.Database.FlightOffers.Model;
using Esky.FlightsCache.Database.Routes;
using FluentAssertions;
using MassTransit;
using Microsoft.Extensions.DependencyInjection;
using MongoDB.Bson;
using MongoDB.Bson.Serialization;
using MongoDB.Driver;
using Xunit.Abstractions;

namespace Esky.FlightsCache.Integration.Tests;

[Collection(CacheAppTestCollection.Name)]
public class MonthlyAggregationTests : IDisposable
{
    private readonly ITestOutputHelper _output;
    private readonly MonthlyAggregatorApp _monthlyAggregatorApp;
    private readonly CacheRequestConsumerApp _cacheRequestConsumerApp;
    private readonly IBus _bus;
    private readonly IRouteAggregationService _routeAggregationsService;

    private readonly IMongoCollection<Airport> _airports;
    private readonly IMongoCollection<ObjectDirectoryAirport> _odAirports;
    private readonly IMongoCollection<ObjectDirectoryAirline> _odAirlines;
    private readonly IMongoCollection<FlightOfferOneWay> _flightOfferOneWays;
    private readonly IMongoCollection<RouteDefinition> _routeDefinition;
    private readonly IMongoCollection<MonthlyRoundTripFlightOffer> _monthlyRt;

    public MonthlyAggregationTests(ITestOutputHelper output, Infrastructure infrastructure)
    {
        _output = output;

        var flightsPromoCacheUrl = new MongoUrl(infrastructure.FlightOffers);
        var mongoClient = new MongoClient(infrastructure.FlightOffers);
        _airports = mongoClient.GetDatabase(flightsPromoCacheUrl.DatabaseName).GetCollection<Airport>("airports");
        _flightOfferOneWays = mongoClient.GetDatabase(flightsPromoCacheUrl.DatabaseName).GetCollection<FlightOfferOneWay>("flightOffersOW");
        _routeDefinition = mongoClient.GetDatabase(flightsPromoCacheUrl.DatabaseName).GetCollection<RouteDefinition>("routes");
        _monthlyRt = mongoClient.GetDatabase(flightsPromoCacheUrl.DatabaseName).GetCollection<MonthlyRoundTripFlightOffer>("monthlyRT");

        var mongoAirports = new StreamReader("Resources/mongoAirports.json").ReadToEnd();
        var bsonAirports = BsonSerializer.Deserialize<IEnumerable<Airport>>(mongoAirports);
        _airports.InsertMany(bsonAirports);

        var mongoRoutes = new StreamReader("Resources/mongoRoutes.json").ReadToEnd();
        var bsonRoutes = BsonSerializer.Deserialize<IEnumerable<RouteDefinition>>(mongoRoutes);
        _routeDefinition.InsertMany(bsonRoutes);


        var objectDirectoryUrl = new MongoUrl(infrastructure.ObjectDirectory);
        var mongoClientOb = new MongoClient(infrastructure.ObjectDirectory);
        _odAirports = mongoClientOb.GetDatabase(objectDirectoryUrl.DatabaseName).GetCollection<ObjectDirectoryAirport>("airports");
        var odAirports = new StreamReader("Resources/objectDirectoryAirports.json").ReadToEnd();
        var bsonOdAirports = BsonSerializer.Deserialize<IEnumerable<ObjectDirectoryAirport>>(odAirports);
        _odAirports.InsertMany(bsonOdAirports);

        var odAirline = new ObjectDirectoryAirline()
        {
            Code = "W6",
            OfficalName = "Wizz Air",
            IsLowCost = true,
            Attributes = [],
            Details = []
        };
        _odAirlines = mongoClientOb.GetDatabase(objectDirectoryUrl.DatabaseName).GetCollection<ObjectDirectoryAirline>("airlines");
        _odAirlines.InsertOne(odAirline);

        _monthlyAggregatorApp = infrastructure.CreateMonthlyAggregatorApp();
        _cacheRequestConsumerApp = infrastructure.CreateCacheRequestConsumerApp();

        _bus = _cacheRequestConsumerApp.Services.GetRequiredService<IBus>();
        _routeAggregationsService = _monthlyAggregatorApp.Services.GetRequiredService<IRouteAggregationService>();
    }
    public void Dispose()
    {
        _monthlyAggregatorApp?.Dispose();
        _cacheRequestConsumerApp?.Dispose();
        _flightOfferOneWays.DeleteMany(new BsonDocument());
        _odAirports.DeleteMany(new BsonDocument());
        _airports.DeleteMany(new BsonDocument());
        _odAirlines?.DeleteMany(new BsonDocument());
        _airports.DeleteMany(new BsonDocument());
        _routeDefinition.DeleteMany(new BsonDocument());
        _monthlyRt.DeleteMany(new BsonDocument());
    }

    [Fact]
    public async Task MonthlyAggregate_builtFrom_OB_and_IB()
    {
        // Arrange
        var provider = 58;
        var route = new RouteForTest(DepartureAirportCode: "EIN",
            DepartureMultiportCode: "EINM",
            ArrivalAirportCode: "KRK",
            ArrivalMultiportCode: "KRK");
        var departureDate = DateTime.UtcNow.AddDays(10);
        var returnDepartureDate = departureDate.AddDays(7);
        
        var owCellOb = FlightOffersFactory.CreateFlightOfferOneWay(provider, route, departureDate);
        var owCellIb = FlightOffersFactory.CreateFlightOfferOneWay(provider, route.Reverse(), returnDepartureDate, minStay: 7);
        
        await _flightOfferOneWays.InsertManyAsync([owCellOb, owCellIb]);
        
        var eventString = route.EventString + $"{departureDate:yyMM}{provider}";
        var eventString2 = route.EventString + $"{returnDepartureDate:yyMM}{provider}";
        
        // Act
        RouteUpdateEvent[] events = [RouteUpdateEvent.Parse(eventString), RouteUpdateEvent.Parse(eventString2)];

        await _routeAggregationsService.RecalculateMonthlyAggregates(events);

        // Assert
        var rtDocuments = await _monthlyRt.Find(new BsonDocument()).ToListAsync();
        rtDocuments.Count.Should().Be(3);
        rtDocuments.Should().AllSatisfy(x => x.RefPrice.Should().Be(22000));
    }
}

public sealed record RouteForTest(
    string DepartureAirportCode,
    string DepartureMultiportCode,
    string ArrivalAirportCode,
    string ArrivalMultiportCode)
{
    public RouteForTest Reverse() => new (ArrivalAirportCode, ArrivalMultiportCode, DepartureAirportCode,
        DepartureMultiportCode);

    public string EventString => $"{DepartureMultiportCode}-{ArrivalMultiportCode}" +
                                   $"{DepartureAirportCode}{ArrivalAirportCode}";
}