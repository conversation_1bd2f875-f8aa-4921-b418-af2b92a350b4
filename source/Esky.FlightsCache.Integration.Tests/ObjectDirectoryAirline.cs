namespace Esky.FlightsCache.Integration.Tests;

public class ObjectDirectoryAirline
{
    public _Id? _id { get; set; }
    public string? Code { get; set; }
    public Attribute[]? Attributes { get; set; }
    public Detail1[]? Details { get; set; }
    public string? OfficalName { get; set; }
    public bool IsLowCost { get; set; }
}

public class _Id
{
    public required string oid { get; set; }
}

public class Attribute
{
    public required string Code { get; set; }
    public object[]? Attributes { get; set; }
    public Detail[]? Details { get; set; }
    public object? ObjectCode { get; set; }
    public int Type { get; set; }
    public int ObjectType { get; set; }
}

public class Detail
{
    public string? LanguageCode { get; set; }
    public string? Name { get; set; }
    public string? Value { get; set; }
}

public class Detail1
{
    public string? LanguageCode { get; set; }
    public string? Name { get; set; }
    public Alliance? Alliance { get; set; }
}

public class Alliance
{
    public object? Code { get; set; }
    public object? Name { get; set; }
}
