using Esky.FlightsCache.Database.Airports;
using Esky.FlightsCache.Database.FlightOffers.Model;
using Esky.FlightsCache.MessageContract;
using FluentAssertions;
using MassTransit;
using Microsoft.Extensions.DependencyInjection;
using MongoDB.Bson;
using MongoDB.Bson.Serialization;
using MongoDB.Driver;
using System.Data.SqlClient;
using System.Text.Json;
using Xunit.Abstractions;

namespace Esky.FlightsCache.Integration.Tests;

[Collection(CacheAppTestCollection.Name)]
public class CacheRequestConsumerTests : IDisposable
{
    private readonly ITestOutputHelper _output;
    private readonly CacheRequestConsumerApp _cacheRequestConsumerApp;
    private readonly IBus _bus;
    private readonly IMongoCollection<Airport> _airports;
    private readonly IMongoCollection<ObjectDirectoryAirport> _odAirports;
    private readonly IMongoCollection<ObjectDirectoryAirline> _odAirlines;
    private readonly IMongoCollection<FlightOfferOneWay> _flightOffersOneWay;
    private readonly CacheRequest _message;
    private readonly string _departure;
    private readonly string _arrival;
    private readonly string _airlineCode;
    private readonly DateTime _departureDate;
    private readonly int _providerCode;
    private readonly decimal _basePrice;
    private SqlConnection _sqlConnection;

    public CacheRequestConsumerTests(ITestOutputHelper output, Infrastructure infrastructure)
    {
        _output = output;

        var flightsPromoCacheUrl = new MongoUrl(infrastructure.FlightOffers);
        var mongoClient = new MongoClient(infrastructure.FlightOffers);
        _airports = mongoClient.GetDatabase(flightsPromoCacheUrl.DatabaseName).GetCollection<Airport>("airports");
        _flightOffersOneWay = mongoClient.GetDatabase(flightsPromoCacheUrl.DatabaseName).GetCollection<FlightOfferOneWay>("flightOffersOW");
        var mongoAirports = new StreamReader("Resources/mongoAirports.json").ReadToEnd();
        var bsonAirports = BsonSerializer.Deserialize<IEnumerable<Airport>>(mongoAirports);
        _airports.InsertMany(bsonAirports);

        var objectDirectoryUrl = new MongoUrl(infrastructure.ObjectDirectory);
        var mongoClientOb = new MongoClient(infrastructure.ObjectDirectory);
        _odAirports = mongoClientOb.GetDatabase(objectDirectoryUrl.DatabaseName).GetCollection<ObjectDirectoryAirport>("airports");
        var odAirports = new StreamReader("Resources/objectDirectoryAirports.json").ReadToEnd();
        var bsonOdAirports = BsonSerializer.Deserialize<IEnumerable<ObjectDirectoryAirport>>(odAirports);
        _odAirports.InsertMany(bsonOdAirports);

        var odAirline = new ObjectDirectoryAirline()
        {
            Code = "W6",
            OfficalName = "Wizz Air",
            IsLowCost = true,
            Attributes = [],
            Details = []
        };
        _odAirlines = mongoClientOb.GetDatabase(objectDirectoryUrl.DatabaseName).GetCollection<ObjectDirectoryAirline>("airlines");
        _odAirlines.InsertOne(odAirline);

        _cacheRequestConsumerApp = infrastructure.CreateCacheRequestConsumerApp();
        _bus = _cacheRequestConsumerApp.Services.GetRequiredService<IBus>();
        _cacheRequestConsumerApp.CreateClient();

        _sqlConnection = new SqlConnection(infrastructure.SqlConnectionString);
        _sqlConnection.Open();

        _departure = "EIN";
        _arrival = "KRK";
        _airlineCode = "W6";
        _departureDate = DateTime.UtcNow.AddDays(10);
        _providerCode = 58;
        _basePrice = 99;

        _message = new CacheRequest()
        {
            Flights =
                [
                    new FlightCache()
                    {
                        Id = 1,
                        ProviderCode = _providerCode,
                        ValidatingCarrier = "W6",
                        SessionId = "sessionid",
                        SearchDate = DateTime.UtcNow,
                        LegsCanBeUseSeparately = false,
                        ExpirationDate = DateTime.UtcNow.AddDays(3),
                        Supplier = "wizzairxml",
                        Legs =
                        [
                            new FlightCacheLeg()
                            {
                                DepartureCode = _departure,
                                ArrivalCode = _arrival,
                                DepartureDate = _departureDate,
                                ArrivalDate = _departureDate.AddHours(10),
                                AirlineCode = _airlineCode,
                                AdultPrices = [new PriceCacheEntry { BasePrice = _basePrice, TaxPrice = 0, MinimumNumberOfPaxes = 1 }],
                                ChildPrices = [new PriceCacheEntry { BasePrice = 105, TaxPrice = 5, MinimumNumberOfPaxes = 1 }],
                                InfantPrices = [new PriceCacheEntry { BasePrice = 105, TaxPrice = 5, MinimumNumberOfPaxes = 1 }],
                                CurrencyCode = "EUR",
                                ConversionRatioToReferenceCurrency = 1,
                                Segments =
                                [
                                    new FlightCacheSegment()
                                    {
                                        DepartureDate = _departureDate,
                                        DepartureCode = _departure,
                                        ArrivalCode = _arrival,
                                        ArrivalDate = _departureDate.AddHours(10),
                                        FlightNumber = "1111",
                                        AirlineCode = _airlineCode,
                                        OperatingAirlineCode = _airlineCode,
                                        OperatingFlightNumber = "1111",
                                        FareDetails = new MessageContract.FareDetails()
                                        {
                                            OfficeId = "eskytest",
                                            OfferId =
                                                "[ZPNY61AUD7178QNO|A27DV23QU0DEP4OA|E8UIFW8U7XMSIYFG|*******|wizzairxml]"
                                        },
                                        BookingClass = "Basic"
                                    }
                                ],
                               SeparationOptions = new SeparationOptions()
                               {
                                   Options = SeparationOptionEnum.OneWay
                               },
                               DepartureSearchCodes = new[] { _departure },
                               ArrivalSearchCodes = new[] { _arrival },
                               DepartureAirportDetails = new AirportDetails() { },
                               ArrivalAirportDetails = new AirportDetails() { },
                               DataTimestamp = DateTime.UtcNow,
                           }
                       ]
                    }
                ],
            CommandOptions = new CommandDataOptions()
            {
                GroupName = "testgroup",
                DeleteOptions = new MessageContract.DeleteOptions()
                {
                    IsEnabled = true,
                    Type = TypeOfDisposalEnum.DateRange,
                    DeleteDepartureDayFrom = _departureDate,
                    DeleteDepartureDayTo = _departureDate,
                    DeleteReturnDepartureDayFrom = null,
                    DeleteReturnDepartureDayTo = null
                },
                SkipSQLSave = true
            },
            SourceDescription = new SourceDescription()
            {
                Name = "testname",
                MachineName = "testmachine",
                SearchDepartureCode = _departure,
                SearchArrivalCode = _arrival,
                SearchDepartureDate = _departureDate,
                SendDate = DateTime.UtcNow,
                Provider = "58",
                PartnerCode = "ADMIN",
                PaxConfiguration = "*******",
                SessionId = "",
            }
        };
    }
    public void Dispose()
    {
        _cacheRequestConsumerApp?.Dispose();
        _flightOffersOneWay.DeleteMany(new BsonDocument());
        _airports.DeleteMany(new BsonDocument());
        _odAirlines?.DeleteMany(new BsonDocument());
        _airports.DeleteMany(new BsonDocument());
    }

    [Fact]
    public async Task Message_from_queue_generates_record_in_db()
    {
        // Act
        await _bus.Publish(_message);

        int i = 0;
        while (await _flightOffersOneWay.CountDocumentsAsync(new BsonDocument()) < 1)
        {
            await Task.Delay(1000);
            if (i >= 10)
                break;
            i++;
        }
        _flightOffersOneWay.CountDocuments(new BsonDocument()).Should().Be(1);
        var document = await _flightOffersOneWay.Find(new BsonDocument()).FirstOrDefaultAsync();

        // Assert
        _output.WriteLine(JsonSerializer.Serialize(document));
        var segment = document.OutboundLeg.Segments[0];
        segment.DepartureCode.Should().Be(_departure);
        segment.ArrivalCode.Should().Be(_arrival);
        segment.DepartureDate.Date.Should().Be(_departureDate.Date);
        segment.Airline.Should().Be(_airlineCode);
    }

    [Theory]
    [InlineData(true)]
    [InlineData(false)]
    public async Task Generated_record_removes_correct_flights_from_db(bool isInbound)
    {
        // Arrange
        var arrivalDate = _departureDate.AddHours(4);
        var route = new Route
        {
            Departure = "EIN",
            Arrival = "KRK",
            MultiportDeparture = "EINM",
            MultiportArrival = "KRK"
        };
        
        var refPrice = Convert.ToInt32(_basePrice) * 100;

        var flightOffer = FlightOfferOneWay.Empty with
        {
            Id = $"EINKRK{_departureDate:yyMMdd}58.1||W61111",
            Provider = _providerCode,
            ReadProvider = _providerCode,
            DepartureDate = _departureDate,
            ArrivalDate = arrivalDate,
            Route = route,
            RefPrice = refPrice,
            Supplier = "wizzairxml",
            IsOneWay = true,
            IsOutbound = true,
            IsInbound = isInbound,
            OutboundLeg = new Leg
            {
                Segments =
                [
                    new Segment
                    {
                        DepartureCode = route.Departure,
                        ArrivalCode = route.Arrival,
                        DepartureDate = _departureDate,
                        ArrivalDate = arrivalDate,
                        Airline = _airlineCode,
                        FlightNumber = "1234",
                        BookingClass = "Y",
                    }
                ]
            },
            AirlineGroup = _airlineCode,
            PriceElements = new Dictionary<string, PriceElement> { ["A"] = new() { Base = _basePrice, Tax = 0 } },
            Currency = "PLN",
            Source = new Source { Id = 10, Name = "source" },
            ExpirationDate = DateTime.MaxValue
        };

        await _flightOffersOneWay.InsertOneAsync(flightOffer);

        // Act
        await _bus.Publish(_message);

        int i = 0;
        
        while (_flightOffersOneWay.Find(x => x.RefPrice == refPrice).ToListAsync().Result.Count == 0)
        {
            await Task.Delay(1000);
            if (i >= 10)
                break;
            i++;
        }
        
        _flightOffersOneWay.CountDocuments(new BsonDocument()).Should().Be(1);
        var document = await _flightOffersOneWay.Find(new BsonDocument()).FirstOrDefaultAsync();

        // Assert
        _output.WriteLine(JsonSerializer.Serialize(document));
        var segment = document.OutboundLeg.Segments.First();
        segment.DepartureCode.Should().Be(_departure);
        segment.ArrivalCode.Should().Be(_arrival);
        segment.DepartureDate.Date.Should().Be(_departureDate.Date);
        segment.Airline.Should().Be(_airlineCode);
        document.RefPrice.Should().Be(refPrice);
    }

    [Fact]
    public async Task Message_from_queue_generates_record_in_sql()
    {
        // Arrange 
        _message.CommandOptions.SkipSQLSave = false;

        // Act
        await _bus.Publish(_message);

        int i = 0;
        while (await _flightOffersOneWay.CountDocumentsAsync(new BsonDocument()) < 1)
        {
            await Task.Delay(1000);
            if (i >= 10)
                break;
            i++;
        }
        _flightOffersOneWay.CountDocuments(new BsonDocument()).Should().Be(1);

        var command = new SqlCommand("SELECT ProviderCode, RefPrice, DepartureDate FROM [IBE_FlightsPromoCache].[cache].[FlightOW]", _sqlConnection);
        using var reader = await command.ExecuteReaderAsync();
        await reader.ReadAsync();

        // Assert
        reader["ProviderCode"].Should().Be(58);
        reader["RefPrice"].Should().Be(_basePrice*100); //eurocents
        Convert.ToDateTime(reader["DepartureDate"]).Date.Should().Be(_departureDate.Date);
    }
}