using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Extensions.Configuration;
using Esky.FlightsCache.MonthlyAggregator;

namespace Esky.FlightsCache.Integration.Tests;

public class MonthlyAggregatorApp : WebApplicationFactory<MonthlyAggregationHostedService>
{
    private readonly string _rabbitConnectionString;
    private readonly string _objectDirectory;
    private readonly string _sqlServerConnection;
    private readonly string _flightsCacheConfiguration;
    private readonly string _redis;
    private readonly string _flightOffers;

    public MonthlyAggregatorApp(string rabbitConnectionString, string objectDirectory, string flightsCacheConfiguration,  string sqlServerConnection, string redis, string flightOffers)
    {
        _rabbitConnectionString = rabbitConnectionString;
        _objectDirectory = objectDirectory;
        _sqlServerConnection = sqlServerConnection;
        _flightsCacheConfiguration = flightsCacheConfiguration;
        _redis = redis;
        _flightOffers = flightOffers;
    }

    protected override void ConfigureWebHost(IWebHostBuilder builder)
    {
        var config = new Dictionary<string, string?>
        {
            { "CacheRequestProcessor:TargetDbConnectionString", _sqlServerConnection },
            { "FlightOffersConfiguration:ConnectionString", _flightOffers },
            { "MongoObjectDirectory:ConnectionString", _objectDirectory },
            { "FlightsCacheConfiguration:ConnectionUrl", _flightsCacheConfiguration },
            { "FlightsCache:ServiceBus:Url", _rabbitConnectionString },
            { "RedisCaching:ConnectionString", _redis }
        };
        var configuration = new ConfigurationBuilder()
            .AddInMemoryCollection(config)
            .Build();
        builder.UseConfiguration(configuration);
    }
}