using Esky.FlightsCache.PartnerSettings;

namespace Esky.FlightsCache.OfferTracker.Integration.Tests;

internal class PartnerSettingsMock : IPartnerSettingsService
{
    public Task<IEnumerable<int>> GetActiveProvidersAsync(string partnerCode) => Task.FromResult((IEnumerable<int>)[19,35,58,80]);

    public Task<PartnerSettingsModel> GetPartnerSettingsAsync(string partnerCode)
    {
        
        return Task.FromResult(
                new PartnerSettingsModel
                {
                    Providers = new Dictionary<string, ProviderModel>() { { "TravelFusion", new ProviderModel() { IsActive = true, ProviderCode = 58 } } },
                    DefaultCurrency = "PLN",
                    SpecialOccasionsSettings = new()
                    {
                        
                    }
                }
             );
    }
}
