using Esky.FlightsCache.Database.FlightOffers.Model;
using System.Text;

namespace Esky.FlightsCache.Integration.Tests;

public static class FlightOffersFactory
{
    public static FlightOfferOneWay CreateFlightOfferOneWay(int provider, RouteForTest routeForTest, DateTime? departureDate = null, int minStay = 7)
    {
        departureDate ??= DateTime.UtcNow.AddDays(10);

        var flightOffer = new FlightOfferOneWay
        {
            Id = ConstructKey(departureDate.Value,provider,routeForTest, "1"),
            Provider = provider,
            DepartureDate = departureDate.Value,
            ArrivalDate = departureDate.Value.AddHours(10),
            Route = new Route
            {
                Departure = routeForTest.DepartureAirportCode,
                Arrival = routeForTest.ArrivalAirportCode,
                MultiportDeparture = routeForTest.DepartureMultiportCode,
                MultiportArrival = routeForTest.ArrivalMultiportCode
            },
            RefPrice = 11000,
            Supplier = "1",
            IsOneWay = true,
            IsOutbound = true,
            IsInbound = true, // Corrected to match old logic
            OutboundLeg = new Leg
            {
                Segments = new List<Segment>
                {
                    new Segment
                    {
                        DepartureCode = routeForTest.DepartureAirportCode,
                        ArrivalCode = routeForTest.ArrivalAirportCode,
                        DepartureDate = departureDate.Value,
                        ArrivalDate = departureDate.Value.AddHours(10),
                        Airline = "W6",
                        FlightNumber = "1111",
                        BookingClass = "Basic"
                    }
                }
            },
            PriceElements = new Dictionary<string, PriceElement>
            {
                ["A"] = new()
                {
                    Base = 105,
                    Tax = 5,
                }
            },
            Currency = "EUR",
            Source = new Source
            {
                Id = 1,
                Name = "source"
            },
            ExpirationDate = DateTime.MaxValue,
            ReadProvider = 0,
            AirlineGroup = null,
            PriceHash = 0,
            ModificationDate = default,
            RefreshDate = default,
            SendDate = default
        };

        return flightOffer;
    }

    private static string ConstructKey(DateTime departureDate, int provider, RouteForTest routeForTest, string? supplier)
    {
        StringBuilder key = new();
        const string supplierSeparator = ".";

        var id = $"{routeForTest.DepartureAirportCode}{routeForTest.ArrivalAirportCode}";
        var year = departureDate.Year.ToString().Substring(2, 2);
        var month = departureDate.Month.ToString("00");
        var day = departureDate.Day.ToString("00");

        if (supplier is not null)
            key.Append(id + year + month + day + provider + supplierSeparator + supplier);
        else
            key.Append(id + year + month + day + provider);

        return key.ToString();
    }
}