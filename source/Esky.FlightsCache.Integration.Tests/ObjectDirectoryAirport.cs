namespace Esky.FlightsCache.Integration.Tests;
public class ObjectDirectoryAirport
{
    public required string Code { get; set; }
    public object[]? Attributes { get; set; }
    public object[]? Details { get; set; }
    public string? CityCode { get; set; }
    public string? CountryCode { get; set; }
    public object[]? MultiportCodes { get; set; }
    public string? ContinentCode { get; set; }
    public string? TimeZoneId { get; set; }
    public string? Longitude { get; set; }
    public string? Latitude { get; set; }
    public int AirportType { get; set; }
    public string[]? NearbyAirports { get; set; }
    public object[]? NearbyAirportDetails { get; set; }
}
