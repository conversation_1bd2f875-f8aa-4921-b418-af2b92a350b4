using DotNet.Testcontainers.Builders;
using DotNet.Testcontainers.Configurations;
using DotNet.Testcontainers.Containers;
using DotNet.Testcontainers.Networks;
using System.Data.SqlClient;
using Testcontainers.MongoDb;
using Testcontainers.MsSql;
using Testcontainers.RabbitMq;
using Testcontainers.Redis;

namespace Esky.FlightsCache.Integration.Tests;

public sealed class Infrastructure : IAsyncLifetime
{
    private const string _database = "IBE_FlightsPromoCache";
    private const string _password = "S3creT_pAss!";
    private static readonly string _sqlServerName = $"sqlServer_{Guid.NewGuid()}";
    private readonly INetwork _network;
    private readonly MsSqlContainer _sqlContainer;
    private readonly MongoDbContainer _mongoDb;
    private readonly RabbitMqContainer _rabbitMq;
    private readonly RedisContainer _redis;

    public Infrastructure()
    {
        _network = new NetworkBuilder().Build();

        _mongoDb = new MongoDbBuilder()
             .WithImage("mongo:7.0")
             .WithUsername(null)
             .WithPassword(null)
             .Build();

        _sqlContainer = new MsSqlBuilder()
            .WithImage("europe-docker.pkg.dev/esky-common/esky-docker-virtual/mssql/server:2022-CU13-ubuntu-22.04")
            .WithPassword(_password)
            .WithName(_sqlServerName)
            .WithNetwork(_network)
            .Build();

        _rabbitMq = new RabbitMqBuilder()
            .WithUsername("cacheuser")
            .WithPassword("cachepassword")
            .WithImage("masstransit/rabbitmq")
            .Build();

        _redis = new RedisBuilder()
            .WithImage("redis:6.2-alpine")
            .Build();
    }

    public string ObjectDirectory => new UriBuilder(_mongoDb.GetConnectionString()) { Path = "ObjectDirectory" }.ToString();
    public string FlightOffers => new UriBuilder(_mongoDb.GetConnectionString()) { Path = "FlightOffers" }.ToString();
    public string FlightsCacheConfiguration => new UriBuilder(_mongoDb.GetConnectionString()) { Path = "FlightsCacheConfiguration" }.ToString();
    public string RabbitConnectionString => _rabbitMq.GetConnectionString();
    public string SqlConnectionString => _sqlContainer.GetConnectionString().Replace("master", _database);
    private string SqlInternalConnectionString => $"Server={_sqlServerName},1433;Database={_database};User Id=sa;Password={_password};Encrypt=false;TrustServerCertificate=True";
    private string RedisConnectionString => _redis.GetConnectionString();


    public CacheRequestConsumerApp CreateCacheRequestConsumerApp() => 
        new(RabbitConnectionString, ObjectDirectory, FlightsCacheConfiguration, SqlConnectionString, RedisConnectionString, FlightOffers);

    public MonthlyAggregatorApp CreateMonthlyAggregatorApp() =>
      new (RabbitConnectionString, ObjectDirectory, FlightsCacheConfiguration, SqlConnectionString, RedisConnectionString, FlightOffers);

    public async Task InitializeAsync()
    {
        Console.WriteLine("Starting containers");
        await Task.WhenAll(_mongoDb.StartAsync(), _sqlContainer.StartAsync(), _rabbitMq.StartAsync(), _redis.StartAsync());
        await new ContainerBuilder()
            .WithImage("eu.gcr.io/esky-ets-flightscontent-pro/esky.flightscache.sqldatabaseupgrader:stable")
            .WithEnvironment("ConnectionStrings__FlightsPromoCache", SqlInternalConnectionString)
            .WithEnvironment("ConnectionStrings__FlightsPromoCacheStaging", SqlInternalConnectionString.Replace(_database, _database + "_Staging"))
            .WithWaitStrategy(Wait.ForUnixContainer().AddCustomWaitStrategy(new ExitCodeWaitStrategy(exitCode: 0)))
            .WithNetwork(_network)
            .DependsOn(_sqlContainer)
            .Build()
            .StartAsync();

        string sqlString = @$"CREATE SEQUENCE [dbo].[FlightIdSequence] 
 AS [int]
 START WITH -2147483648
 INCREMENT BY 1
 MINVALUE -2147483648
 MAXVALUE 2147483647
 CYCLE 
 CACHE";
        using SqlConnection connection = new(SqlConnectionString);
        using SqlCommand command = connection.CreateCommand();
        connection.Open();
        command.CommandText = sqlString;
        command.ExecuteNonQuery();
    }

    public Task DisposeAsync()
    {
        _ = Task.WhenAll(_rabbitMq.StopAsync(), _mongoDb.StopAsync(), _sqlContainer.StopAsync(), _redis.StopAsync());
        return Task.CompletedTask;
    }

    private class ExitCodeWaitStrategy : IWaitUntil
    {
        private readonly int _exitCode;

        public ExitCodeWaitStrategy(int exitCode)
        {
            _exitCode = exitCode;
        }

        public async Task<bool> UntilAsync(IContainer container)
        {
            var exitCode = await container.GetExitCodeAsync();

            if (exitCode != _exitCode)
            {
                throw new Exception($"App exited with code: {exitCode}, which is different than expected: {_exitCode}");
            }

            return true;
        }
    }
}

