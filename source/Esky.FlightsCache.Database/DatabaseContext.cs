using Esky.FlightsCache.Database.Aggregations.Model;
using Esky.FlightsCache.Database.Airports;
using Esky.FlightsCache.Database.FlightOffers.Model;
using Esky.FlightsCache.Database.Helpers;
using Esky.FlightsCache.Database.Routes;
using Esky.FlightsCache.Database.Timetables;
using MongoDB.Bson;
using MongoDB.Bson.Serialization;
using MongoDB.Bson.Serialization.Conventions;
using MongoDB.Bson.Serialization.Serializers;
using MongoDB.Driver;
using System;

namespace Esky.FlightsCache.Database;

public class DatabaseContext
{
    public IMongoCollection<Airport> AirportsCollection { get; }
    public IMongoCollection<TimetableItem> TimetableCollection { get; }
    public IMongoCollection<FlightOfferOneWay> FlightOffersOneWayCollection { get; }
    public IMongoCollection<FlightOfferOneWayStats> FlightOffersOneWayStatsCollection { get; }
    public IMongoCollection<FlightOfferRoundTrip> FlightOffersRoundTripCollection { get; }
    public IMongoCollection<FlightOfferRoundTripStats> FlightOffersRoundTripStatsCollection { get; }
    public IMongoCollection<MonthlyRoundTripFlightOffer> MonthlyRoundTripAggregationCollection { get; }
    public IMongoCollection<RouteDefinition> RouteDefinitions { get; }

    internal string ServerName { get; }
    
    public static void RegisterSerializersAndConventions()
    {
        BsonSerializer.RegisterSerializationProvider(new DecimalSerializationProvider());
        RegisterConventions();
    }

    public DatabaseContext(string connectionString)
    {
        var dbName = MongoUrl.Create(connectionString).DatabaseName;
        var database = new MongoClient(connectionString).GetDatabase(dbName);
        ServerName = $"{database.Client.Settings.Server}/{dbName}";

        FlightOffersOneWayCollection = database
            .GetCollection<FlightOfferOneWay>("flightOffersOW")
            .WithWriteConcern(WriteConcern.W1)
            .EnsureTtlIndex(x => x.ExpirationDate, "TTL", TimeSpan.FromHours(72))
            .EnsureIndex("sourceId", idx => idx.Ascending(c => c.Source.Id));

        FlightOffersRoundTripCollection = database
            .GetCollection<FlightOfferRoundTrip>("flightOffersRT")
            .WithWriteConcern(WriteConcern.W1)
            .EnsureTtlIndex(x => x.ExpirationDate, "TTL", TimeSpan.FromHours(72))
            .EnsureIndex("sourceId", idx => idx.Ascending(c => c.Source.Id));

        MonthlyRoundTripAggregationCollection = database
            .GetCollection<MonthlyRoundTripFlightOffer>("monthlyRT")
            .WithWriteConcern(WriteConcern.W1)
            .EnsureTtlIndex(x => x.ExpirationDate, "TTL", TimeSpan.Zero);

        TimetableCollection = database
            .GetCollection<TimetableItem>("travelFusionTimetable")
            .WithWriteConcern(WriteConcern.W1)
            .EnsureTtlIndex(x => x.Id.DepartureDate, "TTL", TimeSpan.Zero);

        RouteDefinitions = database.GetCollection<RouteDefinition>("routes")
            .EnsureIndex("isSmartOfferRoute",
                idx => idx.Ascending(c => c.IsSmartOfferRoute).Ascending(c => c.WasSmartOfferRoute),
                partialFilter: x => x.IsSmartOfferRoute);

        FlightOffersOneWayStatsCollection = database
            .GetCollection<FlightOfferOneWayStats>("flightOffersOW.stats")
            .WithWriteConcern(WriteConcern.W1)
            .EnsureTtlIndex(x => x.ExpirationDate, "TTL", TimeSpan.FromHours(72))
            .EnsureIndex("EtsAdmin_Stats", idx => idx
                .Ascending(c => c.Provider)
                .Ascending(c => c.Supplier)
                .Ascending(c => c.DepartureDay)
                .Ascending(c => c.Source.Name)
            );

        FlightOffersRoundTripStatsCollection = database
            .GetCollection<FlightOfferRoundTripStats>("flightOffersRT.stats")
            .WithWriteConcern(WriteConcern.W1)
            .EnsureTtlIndex(x => x.ExpirationDate, "TTL", TimeSpan.FromHours(72))
            .EnsureIndex("EtsAdmin_Stats", idx => idx
                .Ascending(c => c.Provider)
                .Ascending(c => c.Supplier)
                .Ascending(c => c.DepartureDay)
                .Ascending(c => c.Source.Name)
            );
        
        RouteDefinitions = database.GetCollection<RouteDefinition>("routes")
            .EnsureIndex("isSmartOfferRoute",
                idx => idx.Ascending(c => c.IsSmartOfferRoute).Ascending(c => c.WasSmartOfferRoute),
                partialFilter: x => x.IsSmartOfferRoute);

        AirportsCollection = database.GetCollection<Airport>("airports");
    }

    private static void RegisterConventions()
    {
        var conventionPack = new ConventionPack { new IgnoreExtraElementsConvention(true) };
        ConventionRegistry.Register("IgnoreExtraElements", conventionPack, _ => true);
    }
}

public class DecimalSerializationProvider : IBsonSerializationProvider
{
    private static readonly DecimalSerializer _decimalSerializer = new(BsonType.Decimal128);

    private static readonly NullableSerializer<decimal> _nullableDecimalSerializer = new(_decimalSerializer);

    public IBsonSerializer GetSerializer(Type type)
    {
        if (type == typeof(decimal))
        {
            return _decimalSerializer;
        }

        if (type == typeof(decimal?))
        {
            return _nullableDecimalSerializer;
        }

        return null;
    }
}