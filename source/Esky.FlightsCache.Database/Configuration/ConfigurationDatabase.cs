using Esky.FlightsCache.Database.Helpers;
using Esky.FlightsCache.ProviderMapping;
using MongoDB.Driver;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Esky.FlightsCache.Database.Configuration;

public interface IConfigurationDatabase
{
    Task<List<CacheProviderConfiguration>> GetList();
    Task<List<SupplierMapping>> GetSupplierMappings();
    Task<bool> TryAddSupplierMapping(SupplierMapping supplierMapping);
    Task<List<ProviderMarginConfiguration>> GetMargins();
    Task<List<TechnicalMarginConfiguration>> GetTechnicalMargins();
}

public class ConfigurationDatabase : IConfigurationDatabase
{
    private readonly IMongoCollection<CacheProviderConfiguration> _providerConfiguration;
    private readonly IMongoCollection<SupplierMapping> _supplierMapping;
    private readonly IMongoCollection<ProviderMarginConfiguration> _providerMargins;
    private readonly IMongoCollection<TechnicalMarginConfiguration> _technicalMargins;

    public ConfigurationDatabase(Settings settings)
    {
        var url = MongoUrl.Create(settings.ConnectionUrl);
        var db = new MongoClient(url).GetDatabase(url.DatabaseName);
        _providerConfiguration = db.GetCollection<CacheProviderConfiguration>(settings.CollectionName);
        _supplierMapping = db.GetCollection<SupplierMapping>("supplierMapping")
            .EnsureIndex("supplier_IX", idx => idx.Ascending(c => c.Supplier), unique: true);
        _providerMargins = db.GetCollection<ProviderMarginConfiguration>("providerMargins");
        _technicalMargins = db.GetCollection<TechnicalMarginConfiguration>("technicalMargins");
    }

    public async Task<List<CacheProviderConfiguration>> GetList()
    {
        return await _providerConfiguration.AsQueryable().ToListAsync();
    }

    public Task<List<SupplierMapping>> GetSupplierMappings()
    {
        return _supplierMapping.AsQueryable().ToListAsync();
    }

    public async Task<bool> TryAddSupplierMapping(SupplierMapping supplierMapping)
    {
        try
        {
            await _supplierMapping.InsertOneAsync(supplierMapping);
            return true;
        }
        catch (MongoWriteException)
        {
            return false;
        }
    }
    
    public Task<List<ProviderMarginConfiguration>> GetMargins()
    {
        return _providerMargins.AsQueryable().ToListAsync();
    }
    public Task<List<TechnicalMarginConfiguration>> GetTechnicalMargins()
    {
        return _technicalMargins.AsQueryable().ToListAsync();
    }

    public class Settings
    {
        public string ConnectionUrl { get; set; }
        public string CollectionName { get; set; }
    }
}