using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Esky.FlightsCache.Database.Configuration;

public interface ISupplierStorage
{
    Task<int> GetSupplierId(string supplier);
}

public class SupplierStorage(IConfigurationDatabase database) : ISupplierStorage
{
    private Dictionary<string, int> _supplierMappings = [];
    private readonly SemaphoreSlim _semaphore = new(1);

    public async Task<int> GetSupplierId(string supplier)
    {
        var id = GetId(supplier);
        while (id is null)
        {
            await _semaphore.WaitAsync();
            try
            {
                if (_supplierMappings.Count == 0)
                {
                    _supplierMappings = await LoadMappings();
                }
                id = GetId(supplier);
                if (id is null)
                {
                    var maxId = _supplierMappings.Values.Count != 0 ? _supplierMappings.Values.Max() : 0;
                    await database.TryAddSupplierMapping(new ProviderMapping.SupplierMapping { Id = maxId + 1, Supplier = supplier });
                    _supplierMappings = await LoadMappings();
                    id = GetId(supplier);
                }
            }
            finally
            {
                _semaphore.Release();
            }
        }

        return id.Value;
    }
    
    private async Task<Dictionary<string, int>> LoadMappings()
    {
        var updatedMappings = await database.GetSupplierMappings();
        return updatedMappings.ToDictionary(x => x.Supplier, x => x.Id);
    }

    private int? GetId(string supplier)
    {
        return _supplierMappings.TryGetValue(supplier, out var x) ? x : null;
    }
}