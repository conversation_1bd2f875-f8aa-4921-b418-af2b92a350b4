using Esky.FlightsCache.Database.Aggregations;
using Esky.FlightsCache.Database.FlightOffers.Model;
using Esky.FlightsCache.ResultFilters.AirportCode;
using MongoDB.Bson;
using MongoDB.Driver;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Esky.FlightsCache.Database.FlightOffers;

public interface IFlightOffersStorage
{
    Task UpsertAndRemove(FlightOfferOneWay[] owFlightOffers, FlightOfferRoundTrip[] rtFlightOffers, RemoveOneWayEntity[] removeOneWayEntities, RemoveRoundTripEntity[] removeRtEntities, FlightOfferOneWayStats[] oneWayStats, FlightOfferRoundTripStats[] roundTripStats);
    Task<long> RemoveBySourceIds(IReadOnlyCollection<long> sourceIds);
    Task UpdateFlightPrices(FlightOfferOneWay ow, FlightOfferRoundTrip rt);
}

public class MultiFlightOfferStore(IEnumerable<IFlightOffersStorage> flightOfferStores) : IFlightOffersStorage
{
    public async Task UpsertAndRemove(FlightOfferOneWay[] owFlightOffers, FlightOfferRoundTrip[] rtFlightOffers, RemoveOneWayEntity[] removeOneWayEntities, RemoveRoundTripEntity[] removeRtEntities, FlightOfferOneWayStats[] oneWayStats, FlightOfferRoundTripStats[] roundTripStats)
    {
        await Task.WhenAll(flightOfferStores.Select(store => store.UpsertAndRemove(owFlightOffers, rtFlightOffers, removeOneWayEntities, removeRtEntities, oneWayStats, roundTripStats)));
    }

    public async Task<long> RemoveBySourceIds(IReadOnlyCollection<long> sourceIds)
    {
        var result = await Task.WhenAll(flightOfferStores.Select(store => store.RemoveBySourceIds(sourceIds)));
        return result[0];
    }

    public Task UpdateFlightPrices(FlightOfferOneWay ow, FlightOfferRoundTrip rt)
    {
        return Task.WhenAll(flightOfferStores.Select(store => store.UpdateFlightPrices(ow, rt)));
    }
}

public class FlightOffersStorage(DatabaseContext dbContext, IAirportsRepository airportsRepository, IMonthlyAggregationsService monthlyAggregationsService) : IFlightOffersStorage
{
    private readonly IMongoCollection<FlightOfferOneWay> _flightOffersOneWayCollection = dbContext.FlightOffersOneWayCollection;
    private readonly IMongoCollection<FlightOfferOneWayStats> _flightOffersOneWayStatsCollection = dbContext.FlightOffersOneWayStatsCollection;
    private readonly IMongoCollection<FlightOfferRoundTrip> _flightOffersRoundTripCollection = dbContext.FlightOffersRoundTripCollection;
    private readonly IMongoCollection<FlightOfferRoundTripStats> _flightOffersRoundTripStatsCollection = dbContext.FlightOffersRoundTripStatsCollection;

    public async Task UpsertAndRemove(
        FlightOfferOneWay[] owFlightOffers,
        FlightOfferRoundTrip[] rtFlightOffers,
        RemoveOneWayEntity[] removeOneWayEntities,
        RemoveRoundTripEntity[] removeRtEntities,
        FlightOfferOneWayStats[] oneWayStats,
        FlightOfferRoundTripStats[] roundTripStats)
    {
        using var activity = Activities.Source.StartActivity();

        var startTime = Stopwatch.GetTimestamp();

        await Task.WhenAll(
            UpsertFlightOffers(_flightOffersOneWayCollection, owFlightOffers),
            UpsertFlightOffers(_flightOffersRoundTripCollection, rtFlightOffers),
            UpsertStats(_flightOffersOneWayStatsCollection, oneWayStats),
            UpsertStats(_flightOffersRoundTripStatsCollection, roundTripStats)
        );

        await Task.WhenAll(RemoveFlightOffersOneWay(removeOneWayEntities), RemoveFlightOffersRoundTrip(removeRtEntities));

        var upsertTime = (int)Stopwatch.GetElapsedTime(startTime).TotalMilliseconds;
        DatabaseMetrics.RecordMongoUpsertTime(upsertTime, dbContext.ServerName);
        DatabaseMetrics.MongoAddedOw(owFlightOffers.Length, dbContext.ServerName);
        DatabaseMetrics.MongoAddedRt(rtFlightOffers.Length, dbContext.ServerName);
        
        await RecalculateMonthlyAggregates(owFlightOffers, rtFlightOffers);
    }

    private async Task UpsertStats<T>(IMongoCollection<T> collection, T[] stats) where T : FlightOfferStats
    {
        if (stats.Length == 0) return;

        using var activity = Activities.Source.StartActivity();
        activity?.SetTag("DocumentsCount", stats.Length);

        var bulkOps = stats
            .Select(document =>
                new ReplaceOneModel<T>(Builders<T>.Filter
                    .Where(x => x.Id.Equals(document.Id)), document) { IsUpsert = true });

        try
        {
            await collection.BulkWriteAsync(bulkOps, new BulkWriteOptions { IsOrdered = false });
        }
        catch (MongoBulkWriteException ex) when (ex.WriteErrors.All(e => e.Category == ServerErrorCategory.DuplicateKey))
        {
            // we can discard because other instance applied its changes
        }
    }

    public async Task<long> RemoveBySourceIds(IReadOnlyCollection<long> sourceIds)
    {
        var results = await Task.WhenAll(
            _flightOffersOneWayCollection.DeleteManyAsync(s => sourceIds.Contains(s.Source.Id)),
            _flightOffersRoundTripCollection.DeleteManyAsync(s => sourceIds.Contains(s.Source.Id))
        );
        return results.Sum(s => s.DeletedCount);
    }

    public async Task UpdateFlightPrices(FlightOfferOneWay ow, FlightOfferRoundTrip rt)
    {
        await Task.WhenAll(UpdateSingleFlightPrices(_flightOffersOneWayCollection, ow), UpdateSingleFlightPrices(_flightOffersRoundTripCollection, rt));
    }

    private async Task UpdateSingleFlightPrices<T>(IMongoCollection<T> collection, T flight) where T : FlightOfferBase
    {
        if (flight == null) return;
        using var activity = Activities.Source.StartActivity();
        
        var filterId = Builders<T>.Filter.Eq(flightOffer => flightOffer.Id, flight.Id);
        var filterMultiport = Builders<T>.Filter.Eq(e => e.Route.Multiport, flight.Route.Multiport);
        var filter = filterId & filterMultiport;
        
        var priceElements = flight
            .PriceElements
            .ToDictionary(e => $"PriceElements.{e.Key}", e => e.Value);
        
        var set = priceElements.ToBsonDocument();
        set["Source._id"] = flight.Source.Id;
        set["Source.Name"] = flight.Source.Name;
        set["PriceHash"] = flight.PriceHash;
        
        var update = new BsonDocument { ["$set"] = set };
        
        var result = await collection.UpdateOneAsync(filter, update, new UpdateOptions { IsUpsert = false });
        
        activity?.SetTag("updated", result.IsModifiedCountAvailable && result.ModifiedCount != 0);
    }

    private async Task RemoveFlightOffersOneWay(RemoveOneWayEntity[] removeOneWayEntities)
    {
        if (removeOneWayEntities.Length == 0) return;

        using var activity = Activities.Source.StartActivity();

        var filters = await CreateOneWayFilters<FlightOfferOneWay>(removeOneWayEntities);
        var statsFilters = await CreateOneWayFilters<FlightOfferOneWayStats>(removeOneWayEntities);

        await Task.WhenAll(
            _flightOffersOneWayCollection.DeleteManyAsync(Builders<FlightOfferOneWay>.Filter.Or(filters), CancellationToken.None),
            _flightOffersOneWayStatsCollection.DeleteManyAsync(Builders<FlightOfferOneWayStats>.Filter.Or(statsFilters), CancellationToken.None)
        );
    }

    private async Task RemoveFlightOffersRoundTrip(RemoveRoundTripEntity[] removeRtEntities)
    {
        if (removeRtEntities.Length == 0) return;

        using var activity = Activities.Source.StartActivity();

        var filters = await CreateRoundTripFilters<FlightOfferRoundTrip>(removeRtEntities);
        var statsFilters = await CreateRoundTripFilters<FlightOfferRoundTripStats>(removeRtEntities);

        await Task.WhenAll(
            _flightOffersRoundTripCollection.DeleteManyAsync(Builders<FlightOfferRoundTrip>.Filter.Or(filters), CancellationToken.None),
            _flightOffersRoundTripStatsCollection.DeleteManyAsync(Builders<FlightOfferRoundTripStats>.Filter.Or(statsFilters), CancellationToken.None)
        );
    }

    private async Task<FilterDefinition<T>[]> CreateOneWayFilters<T>(RemoveOneWayEntity[] removeOneWayEntities) where T : IRemovableFlightOfferOneWay
    {
        var filterBuilder = Builders<T>.Filter;

        var tasks = removeOneWayEntities.Select(async removeOneWayEntity =>
        {
            var multiportRoutes = (await Task.WhenAll(removeOneWayEntity.Routes.Select(e => RouteToClusterKey(e.Departure, e.Arrival)))).ToHashSet();

            var baseFilter = new ExpressionFilterDefinition<T>(
                e => multiportRoutes.Contains(e.Route.Multiport)
                     && removeOneWayEntity.RouteStrings.Contains(e.Route.Id)
                     && removeOneWayEntity.DepartureFrom <= e.DepartureDay
                     && e.DepartureDay <= removeOneWayEntity.DepartureTo
                     && e.ModificationDate < removeOneWayEntity.ModificationDateEarlierThan);

            var tripTypeFilters = removeOneWayEntity.TripTypes.Select(tripType => tripType switch
            {
                TripType.OneWay => new ExpressionFilterDefinition<T>(e => e.IsOneWay),
                TripType.Outbound => new ExpressionFilterDefinition<T>(e => e.IsOutbound),
                TripType.Inbound => new ExpressionFilterDefinition<T>(e => e.IsInbound),
                TripType.OneWay | TripType.Outbound => new ExpressionFilterDefinition<T>(e => e.IsOneWay && e.IsOutbound),
                TripType.OneWay | TripType.Inbound => new ExpressionFilterDefinition<T>(e => e.IsOneWay && e.IsInbound),
                TripType.Outbound | TripType.Inbound => new ExpressionFilterDefinition<T>(e => e.IsOutbound && e.IsInbound),
                _ => new ExpressionFilterDefinition<T>(e => e.IsOneWay && e.IsOutbound && e.IsInbound),
            });
            var tripTypeFilter = Builders<T>.Filter.Or(tripTypeFilters);

            var providerSupplierFilters = filterBuilder.Or(
                removeOneWayEntity.ProviderSuppliers.Select(e => filterBuilder.And(
                    filterBuilder.Eq(flightOffer => flightOffer.Provider, e.Provider),
                    filterBuilder.Eq(flightOffer => flightOffer.Supplier, e.Supplier))
                )
            );
            return filterBuilder.And(baseFilter, providerSupplierFilters, tripTypeFilter);
        });

        return await Task.WhenAll(tasks);
    }

    private async Task<FilterDefinition<T>[]> CreateRoundTripFilters<T>(RemoveRoundTripEntity[] removeRtEntities) where T : IRemovableFlightOfferRoundTrip
    {
        var filterBuilder = Builders<T>.Filter;

        var tasks = removeRtEntities.Select(async removeRoundTripEntity =>
        {
            var multiportRoutes = (await Task.WhenAll(removeRoundTripEntity.Routes.Select(e => RouteToClusterKey(e.Departure, e.Arrival)))).ToHashSet();

            var baseFilter = new ExpressionFilterDefinition<T>(
                e => multiportRoutes.Contains(e.Route.Multiport)
                     && removeRoundTripEntity.RouteStrings.Contains(e.Route.Id)
                     && removeRoundTripEntity.DepartureFrom <= e.DepartureDay
                     && e.DepartureDay <= removeRoundTripEntity.DepartureTo
                     && e.StayLength.DepartureToDeparture == removeRoundTripEntity.StayLength
                     && e.ModificationDate < removeRoundTripEntity.ModificationDateEarlierThan);

            var providerSupplierFilters = filterBuilder.Or(
                removeRoundTripEntity.ProviderSuppliers.Select(e => filterBuilder.And(
                    filterBuilder.Eq(flightOffer => flightOffer.Provider, e.Provider),
                    filterBuilder.Eq(flightOffer => flightOffer.Supplier, e.Supplier))
                )
            );

            return filterBuilder.And(baseFilter, providerSupplierFilters);
        });

        return await Task.WhenAll(tasks);
    }

    private async Task UpsertFlightOffers<T>(IMongoCollection<T> collection, T[] flightOffers) where T : FlightOfferBase
    {
        if (flightOffers.Length == 0) return;

        using var activity = Activities.Source.StartActivity();
        activity?.SetTag("DocumentsCount", flightOffers.Length);

        var bulkOps = flightOffers
            .Select(document =>
                new ReplaceOneModel<T>(Builders<T>.Filter
                    .Where(x => x.Id.Equals(document.Id) && x.Route.Multiport.Equals(document.Route.Multiport)), document) { IsUpsert = true });

        try
        {
            await collection.BulkWriteAsync(bulkOps, new BulkWriteOptions { IsOrdered = false });
        }
        catch (MongoBulkWriteException ex) when (ex.WriteErrors.All(e => e.Category == ServerErrorCategory.DuplicateKey))
        {
            // we can discard because other instance applied its changes
        }
    }

    private async Task<string> RouteToClusterKey(string departure, string arrival)
    {
        var airports = await airportsRepository.GetAirportsAsync();
        var departureMultiport = airports.GetAirport(departure)?.Multiport ?? departure;
        var arrivalMultiport = airports.GetAirport(arrival)?.Multiport ?? arrival;
        return $"{departureMultiport}-{arrivalMultiport}";
    }
    
    private async Task RecalculateMonthlyAggregates(FlightOfferOneWay[] owFlightOffers, FlightOfferRoundTrip[] rtFlightOffers)
    {
        using var activity = Activities.Source.StartActivity();
        await Task.WhenAll(
            monthlyAggregationsService.RecalculateMonthlyAggregates(owFlightOffers),
            monthlyAggregationsService.RecalculateMonthlyAggregates(rtFlightOffers)
        );
    }
}