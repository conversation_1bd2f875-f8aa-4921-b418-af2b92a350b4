using Esky.FlightsCache.Database.FlightOffers.Model;
using System;

namespace Esky.FlightsCache.Database.FlightOffers;

public interface IRemovableFlightOffer
{
    Route Route { get; }
    DateTime DepartureDay { get; }
    DateTime ModificationDate { get; }
    int Provider { get; }
    string? Supplier { get; }
}

public interface IRemovableFlightOfferOneWay : IRemovableFlightOffer
{
    bool IsOneWay { get; }

    bool IsOutbound { get; }

    bool IsInbound { get; }
}

public interface IRemovableFlightOfferRoundTrip : IRemovableFlightOffer
{
    StayLength StayLength { get; }
}