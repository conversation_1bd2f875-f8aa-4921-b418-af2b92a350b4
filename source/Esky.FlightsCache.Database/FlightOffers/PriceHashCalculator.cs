using System.Collections.Generic;
using System.Linq;

namespace Esky.FlightsCache.Database.FlightOffers
{
    public interface IPriceHashCalculator
    {
        int CalculateHash(int? totalFaresLeft, int? availableSeats, Dictionary<string, decimal> prices);
    }

    public class PriceHashCalculator : IPriceHashCalculator
    {
        public int CalculateHash(int? totalFaresLeft, int? availableSeats, Dictionary<string, decimal> prices)
        {
            const int seed = 487;
            const int modifier = 31;
            const int defaultAvailability = 9;
            var sequenceToHash = new[]
                {
                    totalFaresLeft < defaultAvailability ? totalFaresLeft.Value : defaultAvailability,
                    availableSeats < defaultAvailability ? availableSeats.Value : defaultAvailability
                }
                .Concat(prices.OrderBy(x => x.Key).SelectMany(x => new[] { x.Key[0], x.Key.Skip(1).FirstOrDefault(), (int)x.Value }));

            return sequenceToHash.Aggregate(seed, (current, item) => unchecked((current * modifier) + item));
        }
    }
}