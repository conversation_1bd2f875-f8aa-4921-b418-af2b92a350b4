using Esky.FlightsCache.ResultFilters.Contract;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using Microsoft.Extensions.Options;
using System.Collections.Generic;
using System.Text;

namespace Esky.FlightsCache.Database.FlightOffers;

public class ConfigurationRoundTripMergeOptions(IOptionsMonitor<ConfigurationRoundTripMergeOptions.Options> options) : IRoundTripMergeOptions
{
    private Dictionary<int, HashSet<string>> Supported => options.CurrentValue;

    public bool CanMergeIntoRoundTrip(int providerCode, string supplier)
    {
        return Supported.TryGetValue(providerCode, out var suppliers) && suppliers.Contains(supplier);
    }

    public string Print()
    {
        var sb = new StringBuilder();

        foreach (var (provider, suppliers) in Supported)
        {
            sb.Append(provider);
            sb.Append(':');
            sb.Append(string.Join(',', suppliers));
            sb.Append(' ');
        }

        return sb.ToString();
    }

    public class Options : Dictionary<int, HashSet<string>>;
}

public static class RegistrationExtensions
{
    public static void AddConfigurationRoundTripMergeOptions(this IServiceCollection services, IConfigurationSection configuration)
    {
        services.TryAddSingleton<IRoundTripMergeOptions, ConfigurationRoundTripMergeOptions>();
        services.Configure<ConfigurationRoundTripMergeOptions.Options>(configuration);
    }
}