using Esky.FlightsCache.Common.Extensions;
using Esky.FlightsCache.Common.Model;
using Esky.FlightsCache.Database.FlightOffers.Model;
using Esky.FlightsCache.MessageContract;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Esky.FlightsCache.Database.FlightOffers;

public interface IFlightOffersRepository
{
    Task SaveFlightOffersAsync(List<FlightWrapper> flights, long sourceId, CacheRequest cacheRequest);
    Task<long> RemoveBySourceIds(List<long> sourceIds);
    Task UpdateFlightPrices(List<FlightWrapper> flights, long sourceId, CacheRequest cacheRequest);
}

public class FlightOffersRepository : IFlightOffersRepository
{
    private const int _flightsPerGroup = 200;
    private const int _maxStayLength = 30;
    private const int _maxPriceMultiplier = 10;

    private readonly IFlightOffersStorage _flightOffersStorage;
    private readonly ILogger<FlightOffersRepository> _logger;
    private readonly FlightOffersConfiguration _configuration;
    private readonly IFlightOffersToDeleteGenerator _flightOffersToDeleteGenerator;
    private readonly IFlightOffersConverter _flightOffersConverter;

    public FlightOffersRepository(
        IFlightOffersStorage flightOffersStorage,
        ILogger<FlightOffersRepository> logger,
        FlightOffersConfiguration configuration,
        IFlightOffersToDeleteGenerator flightOffersToDeleteGenerator,
        IFlightOffersConverter flightOffersConverter)
    {
        _flightOffersStorage = flightOffersStorage;
        _logger = logger;
        _configuration = configuration;
        _flightOffersToDeleteGenerator = flightOffersToDeleteGenerator;
        _flightOffersConverter = flightOffersConverter;
    }

    public async Task SaveFlightOffersAsync(List<FlightWrapper> flights, long sourceId, CacheRequest cacheRequest)
    {
        using var activity = Activities.Source.StartActivity();

        if (cacheRequest.SourceDescription.SearchDepartureDate <= DateTime.Today.AddDays(_configuration.DaysForward))
        {
            try
            {
                var (owFlightOffers, rtFlightOffers) = await _flightOffersConverter.Convert(flights, sourceId, cacheRequest);

                owFlightOffers = Filter(owFlightOffers);
                rtFlightOffers = FilterRoundTrips(rtFlightOffers);

                var removeOwEntities = await _flightOffersToDeleteGenerator.GenerateFlightOffersToDeleteOneWay(owFlightOffers, cacheRequest);
                var removeRtEntities = await _flightOffersToDeleteGenerator.GenerateFlightOffersToDeleteRoundTrip(rtFlightOffers, cacheRequest);

                var oneWayStats = _flightOffersConverter.GetStats(owFlightOffers);
                var roundTripStats = _flightOffersConverter.GetStats(rtFlightOffers);

                await _flightOffersStorage.UpsertAndRemove(owFlightOffers, rtFlightOffers, removeOwEntities, removeRtEntities, oneWayStats, roundTripStats);
            }
            catch (Exception e)
            {
                _logger.LogError(e, "Error saving flight offers");
            }
        }
    }

    public Task<long> RemoveBySourceIds(List<long> sourceIds)
    {
        return _flightOffersStorage.RemoveBySourceIds(sourceIds);
    }
    
    public async Task UpdateFlightPrices(List<FlightWrapper> flights, long sourceId, CacheRequest cacheRequest)
    {
        try
        {
            var (owFlightOffers, rtFlightOffers) = await _flightOffersConverter.Convert(flights, sourceId, cacheRequest);

            var ow = owFlightOffers.MinBy(x => x.RefPrice);
            var rt = rtFlightOffers.MinBy(x => x.RefPrice);
        
            var paxCount = cacheRequest.SourceDescription.PaxConfiguration.Split('.').Take(3).Sum(int.Parse);
        
            UpdatePriceElementPaxCount(ow);
            UpdatePriceElementPaxCount(rt);

            await _flightOffersStorage.UpdateFlightPrices(ow, rt);
            return;

            void UpdatePriceElementPaxCount<T>(T? flight) where T : FlightOfferBase
            {
                if (flight is null || paxCount < 2)
                {
                    return;
                }

                flight.PriceElements = flight
                    .PriceElements
                    .ToDictionary(e => e.Key.IsRawPriceComponent() ? e.Key : $"{e.Key}{paxCount}", e => e.Value);
            }
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Error updating flight prices");
        }
    }

    private static FlightOfferRoundTrip[] FilterRoundTrips(FlightOfferRoundTrip[] flightOffers)
    {
        flightOffers = flightOffers
            .Where(x => x.StayLength.DepartureToDeparture <= _maxStayLength)
            .ToArray();

        return Filter(flightOffers);
    }

    private static T[] Filter<T>(T[] flightOffers) where T : FlightOfferBase
    {
        flightOffers = flightOffers.Where(x => x.ExpirationDate > DateTime.UtcNow).ToArray();

        if (flightOffers.Length == 0) return flightOffers;

        return flightOffers
            .GroupBy(x => x.Id)
            .Select(group => group.MinBy(x => x.RefPrice))
            .GroupBy(x => (x.DepartureDate.Date, x.Route, x.Provider, x.Supplier))
            .SelectMany(group =>
            {
                var minStops = group.Min(x => x.MaxStops);

                var flights = minStops is 0
                    ? group.Where(x => x.MaxStops <= 2).ToList()
                    : group.Where(x => x.MaxStops <= minStops + 1).ToList();

                var minPrice = flights.Min(x => x.RefPrice);

                return flights
                    .Where(x => x.RefPrice <= minPrice * _maxPriceMultiplier)
                    .OrderBy(x => x.RefPrice)
                    .Take(_flightsPerGroup);
            })
            .ToArray();
    }
}

public class FlightOffersConfiguration
{
    public int DaysForward { get; init; }
    public string ConnectionString { get; init; }
    public string[] AdditionalMongoConnectionStrings { get; init; } = [];

    public bool IsMainDatabase(string connectionString) => ConnectionString == connectionString;

    public IReadOnlyCollection<string> GetConnectionStrings()
    {
        string[] connectionStrings = [ConnectionString, ..AdditionalMongoConnectionStrings];
        return connectionStrings.Distinct().ToArray();
    }
}