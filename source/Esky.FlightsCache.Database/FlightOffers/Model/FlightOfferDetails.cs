using Esky.FlightsCache.Database.Helpers;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Esky.FlightsCache.Database.FlightOffers.Model;

public record FlightOfferBase
{
    [BsonId] public string Id { get; set; }

    [BsonElement("Provider")] public required int Provider { get; init; }
    
    [BsonElement("ReadProvider")] public required int ReadProvider { get; init; }

    [BsonIgnoreIfDefault]
    [BsonElement("Supplier")]
    public string? Supplier { get; init; }

    [BsonElement("DepartureDate")]
    [BsonDateTimeOptions(Kind = DateTimeKind.Utc)]
    public required DateTime DepartureDate { get; init; } //local
    
    [BsonElement("DepartureDay")]
    public DateTime DepartureDay => DepartureDate.Date;

    [BsonElement("ArrivalDate")]
    [BsonDateTimeOptions(Kind = DateTimeKind.Utc)]
    public required DateTime ArrivalDate { get; init; } //local

    [BsonElement("Route")] public required Route Route { get; init; }

    [BsonIgnore] public string Departure => Route.Departure;
    
    [BsonIgnore] public string Arrival => Route.Arrival;

    [BsonElement("OutboundLeg")] public required Leg OutboundLeg { get; init; }

    [BsonIgnore] public IEnumerable<(string Airline, string FlightNumber)> OutboundFlightNumbers => OutboundLeg.Segments.Select(s => (s.Airline, s.FlightNumber));

    [BsonElement("AirlineGroup")] public required string AirlineGroup { get; init; }

    [BsonIgnoreIfDefault]
    [BsonElement("ValidatingCarrier")]
    public string? ValidatingCarrier { get; init; }

    [BsonElement("MaxStops")]
    public int MaxStops { get; init; }

    [BsonElement("PriceElements")] public required Dictionary<string, PriceElement> PriceElements { get; set; }

    [BsonElement("Currency")] public required string Currency { get; init; }

    [BsonIgnoreIfDefault]
    [BsonElement("AvailableSeatsCount")]
    public int? AvailableSeatsCount { get; init; }

    [BsonIgnoreIfDefault]
    [BsonElement("TotalFaresLeft")]
    public int? TotalFaresLeft { get; init; }

    [BsonElement("RefPrice")] public required int RefPrice { get; init; }
    [BsonElement("PriceHash")] public required int PriceHash { get; init; }

    [BsonElement("Source")] public required Source Source { get; init; }

    [BsonElement("ModificationDate")] public required DateTime ModificationDate { get; init; } //UTC

    [BsonElement("RefreshDate")] public required DateTime RefreshDate { get; init; } //UTC
    
    [BsonElement("SendDate")] public required DateTime SendDate { get; init; } //UTC

    [BsonElement("ExpirationDate")] public required DateTime ExpirationDate { get; init; } //UTC
    
    [BsonIgnoreIfDefault]
    [BsonElement("Intercontinental")]
    public bool IsIntercontinental { get; init; }
}

[BsonIgnoreExtraElements]
public record Route
{
    [BsonElement("Id")] public string Id => $"{Departure}-{Arrival}";
    
    [BsonElement("Departure")] public required string Departure { get; init; }
    
    [BsonElement("Arrival")] public required string Arrival { get; init; }

    [BsonElement("Multiport")] public string Multiport => $"{MultiportDeparture}-{MultiportArrival}";
    
    [BsonElement("MultiportDeparture")] public required string MultiportDeparture { get; init; }
    
    [BsonElement("MultiportArrival")] public required string MultiportArrival { get; init; }
}

[BsonIgnoreExtraElements]
public record Leg
{
    [BsonIgnoreIfDefault]
    [BsonElement("FlightTime")]
    public int? FlightTime { get; init; }

    [BsonElement("Segments")] public required List<Segment> Segments { get; init; }
}

[BsonIgnoreExtraElements]
public record Segment
{
    [BsonElement("DepartureCode")] public required string DepartureCode { get; init; }

    [BsonElement("DepartureDate")] public required DateTime DepartureDate { get; init; } //local

    [BsonElement("ArrivalCode")] public required string ArrivalCode { get; init; }

    [BsonElement("ArrivalDate")] public required DateTime ArrivalDate { get; init; } //local

    [BsonElement("Airline")] public required string Airline { get; init; }

    [BsonElement("FlightNumber")] public required string FlightNumber { get; init; }

    [BsonElement("BookingClass")] public required string BookingClass { get; init; }

    [BsonIgnoreIfDefault]
    [BsonElement("IsBaggageIncludedInPrice")]
    public bool IsBaggageIncludedInPrice { get; init; }

    [BsonElement("FareDetails")]
    public FareDetails FareDetails { get; init; }

    [BsonIgnoreIfDefault]
    [BsonElement("OperatingAirlineCode")]
    public string? OperatingAirlineCode { get; init; }

    [BsonIgnoreIfDefault]
    [BsonElement("OperatingFlightNumber")]
    public string? OperatingFlightNumber { get; init; }

    [BsonIgnoreIfDefault]
    [BsonElement("AircraftCode")]
    public string? AircraftCode { get; init; }

    [BsonIgnoreIfDefault]
    [BsonElement("Stopovers")]
    public Stopover[]? Stopovers { get; init; }
}

[BsonIgnoreExtraElements]
public record FareDetails
{
    [BsonIgnoreIfDefault]
    [BsonElement("FareCode")]
    public string? FareCode { get; init; }

    [BsonIgnoreIfDefault]
    [BsonElement("OfficeId")]
    public string? OfficeId { get; init; }

    [BsonIgnoreIfDefault]
    [BsonElement("OfferId")]
    public string? OfferId { get; init; }

    [BsonIgnoreIfDefault]
    [BsonElement("OfferType")]
    [BsonRepresentation(BsonType.String)]
    public OfferType? OfferType { get; init; }

    [BsonIgnoreIfDefault]
    [BsonElement("LastTicketDate")]
    public DateTime? LastTicketDate { get; init; }
}

[BsonIgnoreExtraElements]
public record PriceElement
{
    [BsonElement("Base")] public required decimal Base { get; init; }

    [BsonIgnoreIfDefault]
    [BsonElement("Tax")]
    public decimal Tax { get; init; }

    [BsonIgnoreIfDefault]
    [BsonElement("MarginIncluded")]
    public decimal MarginIncluded { get; init; }
    
    [BsonIgnoreIfDefault]
    [BsonElement("PackagesAdditionalMargin")]
    public decimal PackagesAdditionalMargin { get; init; }
}

[BsonIgnoreExtraElements]
public record Source
{
    [BsonElement("Id")] public required long Id { get; init; }

    [BsonElement("Name")] public required string Name { get; init; }

    [BsonIgnoreIfDefault]
    [BsonElement("Organic")]
    public bool Organic => ModelExtensions.IsOrganic(Name);
}

[BsonIgnoreExtraElements]
public class Stopover
{
    [BsonIgnoreIfDefault]
    [BsonElement("AirportCode")]
    public required string AirportCode { get; init; }

    [BsonIgnoreIfDefault]
    [BsonElement("ArrivalDate")]
    public DateTime? ArrivalDate { get; init; }

    [BsonIgnoreIfDefault]
    [BsonElement("DepartureDate")]
    public DateTime? DepartureDate { get; init; }
}

public enum OfferType
{
    Regular,
    TourOperator
}

[Flags]
public enum TripType
{
    None = 0,
    OneWay = 1,
    Outbound = 2,
    Inbound = 4,
    RoundTrip = 8
}