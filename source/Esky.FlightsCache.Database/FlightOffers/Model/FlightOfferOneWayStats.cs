using MongoDB.Bson.Serialization.Attributes;

namespace Esky.FlightsCache.Database.FlightOffers.Model;

[BsonIgnoreExtraElements]
public record FlightOfferOneWayStats : FlightOfferStats, IRemovableFlightOfferOneWay
{
    [BsonIgnoreIfDefault]
    [BsonElement("IsOneWay")]
    public bool IsOneWay { get; init; }

    [BsonIgnoreIfDefault]
    [BsonElement("IsOutbound")]
    public bool IsOutbound { get; init; }

    [BsonIgnoreIfDefault]
    [BsonElement("IsInbound")]
    public bool IsInbound { get; init; }
    
    [BsonIgnoreIfDefault]
    [BsonElement("MinStay")]
    public int MinStay { get; init; }
    
    [BsonIgnore]
    public TripType TripType
    {
        get
        {
            var tripType = TripType.None;
            if (IsOneWay) tripType |= TripType.OneWay;
            if (IsOutbound) tripType |= TripType.Outbound;
            if (IsInbound) tripType |= TripType.Inbound;
            return tripType;
        }
        init
        {
            IsOneWay = value.HasFlag(TripType.OneWay);
            IsOutbound = value.HasFlag(TripType.Outbound);
            IsInbound = value.HasFlag(TripType.Inbound);
        }
    }
}