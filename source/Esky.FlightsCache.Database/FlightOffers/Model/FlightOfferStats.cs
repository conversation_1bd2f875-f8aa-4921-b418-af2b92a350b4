using MongoDB.Bson.Serialization.Attributes;
using System;

namespace Esky.FlightsCache.Database.FlightOffers.Model;

[BsonIgnoreExtraElements]
public abstract record FlightOfferStats
{
    [BsonId] public string Id { get; init; }
    
    [BsonElement("Route")] public Route Route { get; init; }
    
    [BsonElement("Provider")] public required int Provider { get; init; }
    
    [BsonIgnoreIfDefault]
    [BsonElement("Supplier")]
    public string? Supplier { get; init; }
    
    [BsonElement("DepartureDay")] public DateTime DepartureDay { get; init; }
    
    [BsonElement("Source")] public Source Source { get; init; }
    
    [BsonElement("Flights")] public int NumberOfFlights { get; init; }
    
    [BsonElement("ExpirationDate")] public DateTime ExpirationDate { get; init; }
    
    [BsonElement("SendDate")] public DateTime SendDate { get; init; }
    
    [BsonElement("RefreshDate")] public DateTime RefreshDate { get; init; }
    
    [BsonElement("ModificationDate")] public DateTime ModificationDate { get; init; }
    
    [BsonElement("MinStops")] public int MinStops { get; init; }
    
    [BsonElement("MinRefPrice")] public int MinRefPrice { get; init; }
}