using MongoDB.Bson.Serialization.Attributes;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Esky.FlightsCache.Database.FlightOffers.Model;

[BsonIgnoreExtraElements]
public record FlightOfferRoundTrip : FlightOfferBase, IIdentifiableFlightOfferRoundTrip, IRemovableFlightOfferRoundTrip
{
    [BsonIgnoreIfDefault]
    [BsonElement("ReturnDepartureDate")]
    [BsonDateTimeOptions(Kind = DateTimeKind.Utc)]
    public DateTime ReturnDepartureDate { get; init; } //local

    [BsonIgnoreIfDefault]
    [BsonElement("ReturnArrivalDate")]
    [BsonDateTimeOptions(Kind = DateTimeKind.Utc)]
    public DateTime ReturnArrivalDate { get; init; } //local
    
    [BsonIgnoreIfDefault]
    [BsonElement("StayLength")]
    public StayLength StayLength { get; init; }
    
    [BsonIgnoreIfDefault]
    [BsonElement("InboundLeg")]
    public Leg InboundLeg { get; init; }
    
    [BsonIgnoreIfDefault]
    [BsonElement("LengthOfStayTypes")]
    public LengthOfStayTypeEnum[] LengthOfStayTypes { get; set; }
    
    [BsonElement("Packages")]
    public PackagesDetails Packages { get; set; }
    
    [BsonElement("Airlines")]
    public string[] Airlines => OutboundLeg.Segments.Concat(InboundLeg.Segments).Select(s => s.Airline).Distinct().OrderBy(x => x).ToArray();
    
    [BsonIgnore]
    public TripType TripType => TripType.RoundTrip;

    [BsonIgnore] public IEnumerable<(string Airline, string FlightNumber)> InboundFlightNumbers => InboundLeg?.Segments.Select(s => (s.Airline, s.FlightNumber));

    [BsonIgnore]
    public static readonly FlightOfferRoundTrip Empty = new()
    {
        Provider = 0,
        ReadProvider = 0,
        DepartureDate = default,
        ArrivalDate = default,
        Route = null,
        OutboundLeg = null,
        AirlineGroup = null,
        PriceElements = null,
        Currency = null,
        RefPrice = 0,
        PriceHash = 0,
        Source = null,
        ModificationDate = default,
        RefreshDate = default,
        SendDate = default,
        ExpirationDate = default
    };
    
    [BsonIgnoreExtraElements]
    public record PackagesDetails
    {
        [BsonElement("StayLength")]
        public required int StayLength { get; init; }
        [BsonElement("CheckIn")]
        public required DateTime CheckIn { get; init; }
    }
}

[BsonIgnoreExtraElements]
public record StayLength
{
    [BsonElement("DepartureToDeparture")] public required int DepartureToDeparture { get; init; }

    [BsonElement("ArrivalToDeparture")] public required int ArrivalToDeparture { get; init; }
}

public enum LengthOfStayTypeEnum
{
    /// <summary>
    ///     Any day 30 days forward
    /// </summary>
    Any = 0,

    /// <summary>
    ///     From friday do sunday
    /// </summary>
    Weekends = 1,

    /// <summary>
    ///     0 to 3 days
    /// </summary>
    Range0To3Days = 2,

    /// <summary>
    ///     4 to 7 days
    /// </summary>
    Range4To7Days = 3,

    /// <summary>
    ///     8 to 14 days
    /// </summary>
    Range8To14Days = 4,

    /// <summary>
    ///     15 to 21 days
    /// </summary>
    Range15To21Days = 5,

    /// <summary>
    ///     Continental - 1 to 14 days, Intercontinental - 7 to 30 days
    /// </summary>
    Custom = 6
}