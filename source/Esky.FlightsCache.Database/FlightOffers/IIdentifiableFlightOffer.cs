using Esky.FlightsCache.Database.Configuration;
using Esky.FlightsCache.Database.FlightOffers.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Esky.FlightsCache.Database.FlightOffers;

public interface IIdentifiableFlightOfferOneWay
{
    string Departure { get; }
    string Arrival { get; }
    DateTime DepartureDate { get; }
    int Provider { get; }
    string Supplier { get; }
    TripType TripType { get; }
    IEnumerable<(string Airline, string FlightNumber)> OutboundFlightNumbers { get; }
}

public interface IIdentifiableFlightOfferRoundTrip
{
    string Departure { get; }
    string Arrival { get; }
    DateTime DepartureDate { get; }
    DateTime ReturnDepartureDate { get; }
    int Provider { get; }
    string Supplier { get; }
    TripType TripType { get; }
    IEnumerable<(string Airline, string FlightNumber)> OutboundFlightNumbers { get; }
    IEnumerable<(string Airline, string FlightNumber)> InboundFlightNumbers { get; }
}

public static class IIdentifiableFlightOfferExtensions
{
    private const string _supplierSeparator = ".";
    
    public static async Task<string> CreateId(this IIdentifiableFlightOfferOneWay flightOffer, ISupplierStorage supplierStorage)
    {
        var supplierId = string.IsNullOrEmpty(flightOffer.Supplier) ? (int?)null : await supplierStorage.GetSupplierId(flightOffer.Supplier);

        var str = new StringBuilder();

        str.Append($"{flightOffer.Departure}{flightOffer.Arrival}{flightOffer.DepartureDate:yyMMdd}");

        str.Append($"{flightOffer.Provider}{EncodeFlags(flightOffer.TripType)}");

        if (supplierId.HasValue) str.Append($"{_supplierSeparator}{supplierId}");

        str.Append("||");

        if (flightOffer.OutboundFlightNumbers != null && flightOffer.OutboundFlightNumbers.Any()) str.Append($"{string.Join("|", flightOffer.OutboundFlightNumbers.Select(x => $"{x.Airline}{x.FlightNumber}"))}");

        return str.ToString();
    }

    public static async Task<string> CreateId(this IIdentifiableFlightOfferRoundTrip flightOffer, ISupplierStorage supplierStorage)
    {
        var supplierId = string.IsNullOrEmpty(flightOffer.Supplier) ? (int?)null : await supplierStorage.GetSupplierId(flightOffer.Supplier);

        var str = new StringBuilder();

        str.Append($"{flightOffer.Departure}{flightOffer.Arrival}{flightOffer.DepartureDate:yyMMdd}");

        str.Append($"{flightOffer.ReturnDepartureDate:yyMMdd}");

        str.Append($"{flightOffer.Provider}{EncodeFlags(flightOffer.TripType)}");

        if (supplierId.HasValue) str.Append($"{_supplierSeparator}{supplierId}");

        str.Append("||");

        if (flightOffer.OutboundFlightNumbers != null && flightOffer.OutboundFlightNumbers.Any()) str.Append($"{string.Join("|", flightOffer.OutboundFlightNumbers.Select(x => $"{x.Airline}{x.FlightNumber}"))}");

        if (flightOffer.InboundFlightNumbers != null && flightOffer.InboundFlightNumbers.Any()) str.Append($"||{string.Join("|", flightOffer.InboundFlightNumbers.Select(x => $"{x.Airline}{x.FlightNumber}"))}");

        return str.ToString();
    }

    private static string EncodeFlags(TripType tripType)
    {
        if (tripType.HasFlag(TripType.OneWay) || tripType.HasFlag(TripType.RoundTrip))
        {
            return string.Empty;
        }

        if (tripType.HasFlag(TripType.Outbound) && tripType.HasFlag(TripType.Inbound))
        {
            return "S";
        }

        if (tripType.HasFlag(TripType.Outbound))
        {
            return "O";
        }

        if (tripType.HasFlag(TripType.Inbound))
        {
            return "I";
        }

        return string.Empty;
    }
}