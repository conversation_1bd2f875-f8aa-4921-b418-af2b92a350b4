using Esky.FlightsCache.Database.FlightOffers.Model;
using Esky.FlightsCache.MessageContract;
using Esky.FlightsCache.ProviderMapping;
using Esky.FlightsCache.ResultFilters.AirportCode;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Esky.FlightsCache.Database.FlightOffers;

public interface IFlightOffersToDeleteGenerator
{
    Task<RemoveOneWayEntity[]> GenerateFlightOffersToDeleteOneWay(FlightOfferOneWay[] owFlightOffers, CacheRequest cacheRequest);
    Task<RemoveRoundTripEntity[]> GenerateFlightOffersToDeleteRoundTrip(FlightOfferRoundTrip[] rtFlightOffers, CacheRequest cacheRequest);
}

public class FlightOffersToDeleteGenerator(IAirportsRepository airportsRepository, IFlightsCacheProviderConverter flightsCacheProviderConverter) : IFlightOffersToDeleteGenerator
{
    public async Task<RemoveOneWayEntity[]> GenerateFlightOffersToDeleteOneWay(FlightOfferOneWay[] owFlightOffers, CacheRequest cacheRequest)
    {
        if (cacheRequest.SourceDescription.PartnerCode?.StartsWith("GFS") ?? false) return [];
        if (cacheRequest.SourceDescription.Name == "flightsearch-api-additional") return [];
        
        if (cacheRequest.SourceDescription.SearchReturnDepartureDate is null && cacheRequest.Flights.Count == 0)
        {
            var emptyOwRequest = await CreateForEmptyOwRequest(cacheRequest);
            return emptyOwRequest is null ? [] : [emptyOwRequest];
        }

        if (owFlightOffers.Length == 0) return [];

        var (searchDepartureAirports, searchArrivalAirports) = await GetAirports(cacheRequest.SourceDescription);

        var owToDelete = owFlightOffers
            .Select(flightOffer => (flightOffer.Departure, flightOffer.Arrival, flightOffer.TripType, flightOffer.Provider, flightOffer.Supplier))
            .Where(e => IsWithinSearchCodes(e.Departure, e.Arrival))
            .Distinct()
            .GroupBy(x => (x.Departure, x.Arrival))
            .Select(e =>
                {
                    var isReturn = searchDepartureAirports.Contains(e.Key.Arrival);
                    var deleteFrom = isReturn
                        ? cacheRequest.CommandOptions.DeleteOptions.DeleteReturnDepartureDayFrom
                        : cacheRequest.CommandOptions.DeleteOptions.DeleteDepartureDayFrom;
                    var deleteTo = isReturn
                        ? cacheRequest.CommandOptions.DeleteOptions.DeleteReturnDepartureDayTo
                        : cacheRequest.CommandOptions.DeleteOptions.DeleteDepartureDayTo;

                    if (!deleteFrom.HasValue || !deleteTo.HasValue) return null;

                    return new RemoveOneWayEntity
                    {
                        DepartureFrom = DateTime.SpecifyKind(deleteFrom.Value, DateTimeKind.Utc),
                        DepartureTo = DateTime.SpecifyKind(deleteTo.Value, DateTimeKind.Utc),
                        ProviderSuppliers = e.Select(x => (x.Provider, x.Supplier)).ToHashSet(),
                        Routes = [(e.Key.Departure, e.Key.Arrival)],
                        TripTypes = e.Select(x => x.TripType).ToHashSet(),
                        ModificationDateEarlierThan = owFlightOffers.Min(x => x.ModificationDate)
                    };
                }
            )
            .Where(x => x != null);

        return owToDelete.Distinct().ToArray();

        bool IsWithinSearchCodes(string departure, string arrival)
        {
            return searchDepartureAirports.Contains(departure) && searchArrivalAirports.Contains(arrival)
                   || searchArrivalAirports.Contains(departure) && searchDepartureAirports.Contains(arrival);
        }
    }

    private async Task<RemoveOneWayEntity> CreateForEmptyOwRequest(CacheRequest cacheRequest)
    {
        if (cacheRequest.CommandOptions.DeleteOptions.ProviderCodes == null) return null;
        
        var (searchDepartureAirports, searchArrivalAirports) = await GetAirports(cacheRequest.SourceDescription);

        if (!searchDepartureAirports.Contains(cacheRequest.SourceDescription.SearchDepartureCode) || 
            !searchArrivalAirports.Contains(cacheRequest.SourceDescription.SearchArrivalCode))
        {
            return null; //when empty request with multiports - do not remove
        }

        var routes = searchDepartureAirports.SelectMany(departure => searchArrivalAirports.Select(arrival => (Departure: departure, Arrival: arrival))).ToHashSet();

        var providerSuppliers = cacheRequest.CommandOptions.DeleteOptions.ProviderCodes.Select(x => (x, cacheRequest.SourceDescription.Supplier)).ToHashSet();
        
        var cacheProviderSuppliers = new List<(int Provider, string Supplier)>();

        foreach (var (provider, supplier) in providerSuppliers)
        {
            var cacheProviderCodes = flightsCacheProviderConverter.GetCacheProviderCodes(provider, supplier);

            if (cacheProviderCodes.Count == 0) cacheProviderCodes.Add(provider);

            cacheProviderSuppliers.AddRange(cacheProviderCodes.Select(x => (x, supplier)));
        }

        return new RemoveOneWayEntity
        {
            DepartureFrom = DateTime.SpecifyKind(cacheRequest.CommandOptions.DeleteOptions.DeleteDepartureDayFrom.Date, DateTimeKind.Utc),
            DepartureTo = DateTime.SpecifyKind(cacheRequest.CommandOptions.DeleteOptions.DeleteDepartureDayTo.Date, DateTimeKind.Utc),
            ProviderSuppliers = cacheProviderSuppliers.ToHashSet(),
            Routes = routes,
            TripTypes = [TripType.OneWay | TripType.Outbound | TripType.Inbound, TripType.OneWay | TripType.Outbound, TripType.Inbound],
            ModificationDateEarlierThan = DateTime.UtcNow
        };
    }

    public async Task<RemoveRoundTripEntity[]> GenerateFlightOffersToDeleteRoundTrip(FlightOfferRoundTrip[] rtFlightOffers, CacheRequest cacheRequest)
    {
        if (cacheRequest.SourceDescription.Name == "flightsearch-api-additional") return [];
        if ((cacheRequest.SourceDescription.PartnerCode?.StartsWith("GFS") ?? false)
            || rtFlightOffers.Length == 0
            || cacheRequest.CommandOptions.DeleteOptions.DeleteReturnDepartureDayFrom == null
            || cacheRequest.CommandOptions.DeleteOptions.DeleteReturnDepartureDayTo == null) return [];

        var (searchDepartureAirports, searchArrivalAirports) = await GetAirports(cacheRequest.SourceDescription);

        var rtToDelete = rtFlightOffers
            .Select(flightOffer => (flightOffer.Departure, flightOffer.Arrival, flightOffer.Provider, flightOffer.Supplier))
            .Where(e => IsWithinSearchCodes(e.Departure, e.Arrival))
            .Distinct()
            .GroupBy(x => (x.Departure, x.Arrival))
            .Select(e =>
                {
                    return new RemoveRoundTripEntity
                    {
                        DepartureFrom = DateTime.SpecifyKind(cacheRequest.CommandOptions.DeleteOptions.DeleteDepartureDayFrom, DateTimeKind.Utc),
                        DepartureTo = DateTime.SpecifyKind(cacheRequest.CommandOptions.DeleteOptions.DeleteDepartureDayTo, DateTimeKind.Utc),
                        StayLength = (cacheRequest.CommandOptions.DeleteOptions.DeleteReturnDepartureDayFrom.Value - cacheRequest.CommandOptions.DeleteOptions.DeleteDepartureDayFrom).Days,
                        ProviderSuppliers = e.Select(x => (x.Provider, x.Supplier)).ToHashSet(),
                        Routes = [(e.Key.Departure, e.Key.Arrival)],
                        ModificationDateEarlierThan = rtFlightOffers.Min(x => x.ModificationDate)
                    };
                }
            );

        return rtToDelete.Distinct().ToArray();

        bool IsWithinSearchCodes(string departure, string arrival)
        {
            return searchDepartureAirports.Contains(departure) && searchArrivalAirports.Contains(arrival)
                   || searchArrivalAirports.Contains(departure) && searchDepartureAirports.Contains(arrival);
        }
        
    }

    private async Task<(string[] searchDepartureAirports, string[] searchArrivalAirports)> GetAirports(SourceDescription sourceDescription)
    {
        var airports = await airportsRepository.GetAirportsAsync();

        var searchDepartureAirports = airports.GetAirports(sourceDescription.SearchDepartureCode);
        var searchArrivalAirports = airports.GetAirports(sourceDescription.SearchArrivalCode);
        
        return (searchDepartureAirports, searchArrivalAirports);
    }
}

public class RemoveOneWayEntity : RemoveEntityBase
{
    public HashSet<TripType> TripTypes { get; init; }
}

public class RemoveRoundTripEntity : RemoveEntityBase
{
    public int StayLength { get; init; }
}

public class RemoveEntityBase
{
    public HashSet<(string Departure, string Arrival)> Routes { get; init; }

    public IEnumerable<string> RouteStrings => Routes.Select(x => $"{x.Departure}-{x.Arrival}");

    public DateTime DepartureFrom { get; init; }

    public DateTime DepartureTo { get; init; }

    public HashSet<(int Provider, string? Supplier)> ProviderSuppliers { get; init; }

    public DateTime ModificationDateEarlierThan { get; init; }
}