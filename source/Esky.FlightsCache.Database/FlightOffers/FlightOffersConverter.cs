using Esky.FlightsCache.Common.Extensions;
using Esky.FlightsCache.Common.Model;
using Esky.FlightsCache.Database.Configuration;
using Esky.FlightsCache.Database.FlightOffers.Model;
using Esky.FlightsCache.Database.Helpers;
using Esky.FlightsCache.MessageContract;
using Esky.FlightsCache.Processing.Helpers;
using Esky.FlightsCache.ResultFilters.Contract;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using FareDetails = Esky.FlightsCache.Database.FlightOffers.Model.FareDetails;
using OfferType = Esky.FlightsCache.Database.FlightOffers.Model.OfferType;

namespace Esky.FlightsCache.Database.FlightOffers;

public interface IFlightOffersConverter
{
    Task<(FlightOfferOneWay[], FlightOfferRoundTrip[])> Convert(List<FlightWrapper> flights, long sourceId, CacheRequest cacheRequest);
    FlightOfferOneWayStats[] GetStats(FlightOfferOneWay[] owFlightOffers);
    FlightOfferRoundTripStats[] GetStats(FlightOfferRoundTrip[] rtFlightOffers);
}

public class FlightOffersConverter(IRoundTripMergeOptions roundTripMergeOptions, ISupplierStorage supplierStorage, IPriceHashCalculator priceHashCalculator) : IFlightOffersConverter
{
    public async Task<(FlightOfferOneWay[], FlightOfferRoundTrip[])> Convert(List<FlightWrapper> flights, long sourceId, CacheRequest cacheRequest)
    {
        var flightOfferOneWays = new List<FlightOfferOneWay>();
        var flightOfferRoundTrips = new List<FlightOfferRoundTrip>();

        foreach (var flight in flights)
        {
            if (flight.IsRoundTrip())
            {
                flightOfferRoundTrips.Add(await ConvertToFlightOfferRoundTrip(flight, sourceId, cacheRequest));
            }
            else
            {
                flightOfferOneWays.Add(await ConvertToFlightOfferOneWay(flight, sourceId, cacheRequest));
            }
        }

        return (flightOfferOneWays.ToArray(), flightOfferRoundTrips.ToArray());
    }

    public FlightOfferOneWayStats[] GetStats(FlightOfferOneWay[] owFlightOffers)
    {
        if (owFlightOffers.Length == 0) return [];

        var stats = owFlightOffers
            .GroupBy(x => x.Id.Split("||")[0])
            .Select(g => new FlightOfferOneWayStats
            {
                Id = g.Key,
                Provider = g.First().Provider,
                Supplier = g.First().Supplier,
                DepartureDay = g.First().DepartureDay,
                Route = new Route
                {
                    Departure = g.First().Route.Departure,
                    Arrival = g.First().Route.Arrival,
                    MultiportDeparture = g.First().Route.MultiportDeparture,
                    MultiportArrival = g.First().Route.MultiportArrival
                },
                TripType = g.First().TripType,
                MinStay = g.First().MinStay,
                NumberOfFlights = g.Count(),
                Source = new Source
                {
                    Id = g.First().Source.Id,
                    Name = g.First().Source.Name
                },
                MinRefPrice = g.Min(x => x.RefPrice),
                MinStops = g.Min(x=> x.MaxStops),
                ExpirationDate = g.Min(x => x.ExpirationDate),
                SendDate = g.Min(x => x.SendDate),
                RefreshDate = g.Min(x => x.RefreshDate),
                ModificationDate = g.Min(x => x.ModificationDate)
            })
            .ToArray();

        return stats;
    }

    public FlightOfferRoundTripStats[] GetStats(FlightOfferRoundTrip[] rtFlightOffers)
    {
        if (rtFlightOffers.Length == 0) return [];

        var stats = rtFlightOffers
            .GroupBy(x => x.Id.Split("||")[0])
            .Select(g => new FlightOfferRoundTripStats
            {
                Id = g.Key,
                Provider = g.First().Provider,
                Supplier = g.First().Supplier,
                DepartureDay = g.First().DepartureDay,
                Route = new Route
                {
                    Departure = g.First().Route.Departure,
                    Arrival = g.First().Route.Arrival,
                    MultiportDeparture = g.First().Route.MultiportDeparture,
                    MultiportArrival = g.First().Route.MultiportArrival
                },
                StayLength = g.First().StayLength,
                NumberOfFlights = g.Count(),
                Source = new Source
                {
                    Id = g.First().Source.Id,
                    Name = g.First().Source.Name
                },
                MinRefPrice = g.Min(x => x.RefPrice),
                MinStops = g.Min(x=> x.MaxStops),
                ExpirationDate = g.Min(x => x.ExpirationDate),
                SendDate = g.Min(x => x.SendDate),
                RefreshDate = g.Min(x => x.RefreshDate),
                ModificationDate = g.Min(x => x.ModificationDate)
            })
            .ToArray();

        return stats;
    }

    private async Task<FlightOfferOneWay> ConvertToFlightOfferOneWay(FlightWrapper flight, long sourceId, CacheRequest cacheRequest)
    {
        var outboundLeg = flight.Legs.First();

        var priceElements = PriceElementsHelper.Create(outboundLeg, null);
        var availableSeatsCount = flight.Legs.Min(x => x.AvailableSeatsCount);
        var totalFaresLeft = flight.Legs.Min(x => x.TotalFaresLeft);
        var priceHash = priceHashCalculator.CalculateHash(totalFaresLeft, availableSeatsCount, priceElements.ToDictionary(x => x.Key, x => x.Value.Base + x.Value.Tax));
        var offer = new FlightOfferOneWay
        {
            Provider = flight.ProviderCode,
            ReadProvider = flight.ReadProviderCode,
            Supplier = flight.Supplier,
            DepartureDate = DateTime.SpecifyKind(outboundLeg.DepartureDate, DateTimeKind.Utc),
            ArrivalDate = DateTime.SpecifyKind(outboundLeg.ArrivalDate, DateTimeKind.Utc),
            Route = CreateRoute(outboundLeg),
            MinStay = outboundLeg.SeparationOptions.MinStay,
            TripType = GetTripType(outboundLeg, flight.ReadProviderCode, flight.Supplier),
            OutboundLeg = CreateLeg(outboundLeg),
            AirlineGroup = outboundLeg.AirlineCode,
            ValidatingCarrier = flight.ValidatingCarrier,
            MaxStops = flight.Legs.Max(x => x.Segments.Count) - 1,
            PriceElements = priceElements,
            PriceHash = priceHash,
            Currency = outboundLeg.CurrencyCode,
            RefPrice = GetRefPrice(outboundLeg, null),
            ExpirationDate = DateTimeExtensions.Min(flight.ExpirationDate.AsDateTimeWithoutSeconds(), outboundLeg.DepartureDate.AddHours(12)),
            ModificationDate = DateTime.UtcNow,
            SendDate = cacheRequest.SourceDescription.SendDate ?? DateTime.UtcNow,
            RefreshDate = outboundLeg.DataTimestamp ?? cacheRequest.SourceDescription.SendDate ?? DateTime.UtcNow,
            Source = new Source { Id = sourceId, Name = cacheRequest.SourceDescription.Name },
            AvailableSeatsCount = availableSeatsCount,
            TotalFaresLeft = totalFaresLeft,
            IsIntercontinental = outboundLeg.DepartureAirportDetails.ContinentCode != outboundLeg.ArrivalAirportDetails.ContinentCode,
            Packages = new FlightOfferOneWay.PackagesDetails { CheckIn = PackagesHelper.GetCheckIn(outboundLeg), CheckOut = PackagesHelper.GetCheckOut(outboundLeg) }
        };

        offer.Id = await offer.CreateId(supplierStorage);

        return offer;
    }

    private async Task<FlightOfferRoundTrip> ConvertToFlightOfferRoundTrip(FlightWrapper flight, long sourceId, CacheRequest cacheRequest)
    {
        var outboundLeg = flight.Legs.First();
        var inboundLeg = flight.Legs.Last();
        var priceElements = PriceElementsHelper.Create(outboundLeg, inboundLeg);
        var availableSeatsCount = flight.Legs.Min(x => x.AvailableSeatsCount);
        var totalFaresLeft = flight.Legs.Min(x => x.TotalFaresLeft);
        var priceHash = priceHashCalculator.CalculateHash(totalFaresLeft, availableSeatsCount, priceElements.ToDictionary(x => x.Key, x => x.Value.Base + x.Value.Tax));
        var intercontinental = outboundLeg.DepartureAirportDetails.ContinentCode != outboundLeg.ArrivalAirportDetails.ContinentCode;
        var stayLength = StayLengthHelper.GetDepartureToDeparture(outboundLeg, inboundLeg);

        var offer = new FlightOfferRoundTrip
        {
            Provider = flight.ProviderCode,
            ReadProvider = flight.ReadProviderCode,
            Supplier = flight.Supplier,
            DepartureDate = DateTime.SpecifyKind(outboundLeg.DepartureDate, DateTimeKind.Utc),
            ArrivalDate = DateTime.SpecifyKind(outboundLeg.ArrivalDate, DateTimeKind.Utc),
            ReturnDepartureDate = DateTime.SpecifyKind(inboundLeg.DepartureDate, DateTimeKind.Utc),
            ReturnArrivalDate = DateTime.SpecifyKind(inboundLeg.ArrivalDate, DateTimeKind.Utc),
            Route = CreateRoute(outboundLeg),
            StayLength = new StayLength { DepartureToDeparture = stayLength, ArrivalToDeparture = StayLengthHelper.GetArrivalToDeparture(outboundLeg, inboundLeg) },
            OutboundLeg = CreateLeg(outboundLeg),
            InboundLeg = CreateLeg(inboundLeg),
            AirlineGroup = outboundLeg.AirlineCode,
            ValidatingCarrier = flight.ValidatingCarrier,
            MaxStops = flight.Legs.Max(x => x.Segments.Count) - 1,
            PriceElements = priceElements,
            PriceHash = priceHash,
            Currency = outboundLeg.CurrencyCode,
            RefPrice = GetRefPrice(outboundLeg, inboundLeg),
            ExpirationDate = DateTimeExtensions.Min(flight.ExpirationDate.AsDateTimeWithoutSeconds(), outboundLeg.DepartureDate.AddHours(12)),
            ModificationDate = DateTime.UtcNow,
            SendDate = cacheRequest.SourceDescription.SendDate ?? DateTime.UtcNow,
            RefreshDate = outboundLeg.DataTimestamp ?? cacheRequest.SourceDescription.SendDate ?? DateTime.UtcNow,
            Source = new Source { Id = sourceId, Name = cacheRequest.SourceDescription.Name },
            AvailableSeatsCount = availableSeatsCount,
            TotalFaresLeft = totalFaresLeft,
            IsIntercontinental = intercontinental,
            LengthOfStayTypes = GetLengthOfStayTypes(outboundLeg.DepartureDate, stayLength, intercontinental).ToArray(),
            Packages = new FlightOfferRoundTrip.PackagesDetails { CheckIn = PackagesHelper.GetCheckIn(outboundLeg), StayLength = PackagesHelper.GetStayLength(outboundLeg, inboundLeg) }
        };

        offer.Id = await offer.CreateId(supplierStorage);

        return offer;
    }

    private static Route CreateRoute(FlightCacheLeg leg)
    {
        return new Route
        {
            Departure = leg.DepartureCode,
            Arrival = leg.ArrivalCode,
            MultiportDeparture = leg.DepartureAirportDetails.Multiport,
            MultiportArrival = leg.ArrivalAirportDetails.Multiport
        };
    }

    private static Leg CreateLeg(FlightCacheLeg leg)
    {
        return leg is null ? null : new Leg
        {
            FlightTime = (int)leg.FlightTime.TotalMinutes,
            Segments = leg.Segments.Select(ConvertToSegment).ToList()
        };
    }

    private static Segment ConvertToSegment(FlightCacheSegment segment)
    {
        return new Segment
        {
            DepartureCode = segment.DepartureCode,
            ArrivalCode = segment.ArrivalCode,
            DepartureDate = DateTime.SpecifyKind(segment.DepartureDate, DateTimeKind.Utc),
            ArrivalDate = DateTime.SpecifyKind(segment.ArrivalDate, DateTimeKind.Utc),
            Airline = segment.AirlineCode,
            FlightNumber = segment.FlightNumber,
            OperatingAirlineCode = segment.AirlineCode != segment.OperatingAirlineCode ? segment.OperatingAirlineCode : null,
            OperatingFlightNumber = segment.AirlineCode != segment.OperatingAirlineCode ? segment.OperatingFlightNumber : null,
            BookingClass = segment.BookingClass,
            AircraftCode = segment.AircraftCode,
            IsBaggageIncludedInPrice = segment.IsBaggageIncludedInPrice,
            FareDetails = new FareDetails
            {
                FareCode = segment.FareDetails?.FareCode,
                OfficeId = segment.FareDetails?.OfficeId,
                OfferId = segment.FareDetails?.OfferId,
                OfferType = (OfferType?)segment.FareDetails?.OfferType ?? OfferType.Regular,
                LastTicketDate = segment.FareDetails?.LastTicketDate
            },
            Stopovers = segment.Stopovers?.Count > 0
                ? segment.Stopovers.Select(s => new Stopover { AirportCode = s.AirportCode, ArrivalDate = s.ArrivalDate, DepartureDate = s.DepartureDate, }).ToArray()
                : null
        };
    }

    private static int GetRefPrice(FlightCacheLeg outboundLeg, FlightCacheLeg inboundLeg)
    {
        var outboundOnePaxPrice = outboundLeg.AdultPrices?.OrderBy(x => x.MinimumNumberOfPaxes).FirstOrDefault();
        var inboundOnePaxPrice = inboundLeg?.AdultPrices?.OrderBy(x => x.MinimumNumberOfPaxes).FirstOrDefault();

        var basePrice = (outboundOnePaxPrice?.BasePrice ?? 0) + (inboundOnePaxPrice?.BasePrice ?? 0);
        var taxPrice = (outboundOnePaxPrice?.TaxPrice ?? 0) + (inboundOnePaxPrice?.TaxPrice ?? 0);
        var refPriceFxRate = outboundLeg.ConversionRatioToReferenceCurrency * 100;
        var totalPrice = basePrice + taxPrice;

        try
        {
            return decimal.ToInt32(totalPrice * refPriceFxRate);
        }
        catch (OverflowException ex)
        {
            throw new InvalidPriceException(totalPrice, outboundLeg.CurrencyCode, ex);
        }
    }

    private TripType GetTripType(FlightCacheLeg outboundLeg, int readProvider, string supplier)
    {
        if (roundTripMergeOptions.CanMergeIntoRoundTrip(readProvider, supplier) &&
            (outboundLeg.SeparationOptions.Options.HasFlag(SeparationOptionEnum.OneWay)
             || outboundLeg.SeparationOptions.Options.HasFlag(SeparationOptionEnum.RoundTripOutbound)))
        {
            return (TripType)outboundLeg.SeparationOptions.Options | TripType.OneWay | TripType.Outbound;
        }

        return (TripType)outboundLeg.SeparationOptions.Options;
    }
    
    private static IEnumerable<LengthOfStayTypeEnum> GetLengthOfStayTypes(
        DateTime departureDate,
        int stayLength,
        bool isIntercontinental)
    {
        if (stayLength is < 1 or > 30)
        {
            yield break;
        }

        yield return LengthOfStayTypeEnum.Any;

        if ((departureDate.DayOfWeek == DayOfWeek.Friday && stayLength <= 3)
            || (departureDate.DayOfWeek == DayOfWeek.Saturday && stayLength <= 2))
        {
            yield return LengthOfStayTypeEnum.Weekends;
        }

        switch (stayLength)
        {
            case <= 3:
                yield return LengthOfStayTypeEnum.Range0To3Days;
                break;
            case <= 7:
                yield return LengthOfStayTypeEnum.Range4To7Days;
                break;
            case <= 14:
                yield return LengthOfStayTypeEnum.Range8To14Days;
                break;
            case <= 21:
                yield return LengthOfStayTypeEnum.Range15To21Days;
                break;
        }

        if (isIntercontinental)
        {
            if (stayLength is >= 7 and <= 30)
            {
                yield return LengthOfStayTypeEnum.Custom;
            }
        }
        else
        {
            if (stayLength is >= 1 and <= 14)
            {
                yield return LengthOfStayTypeEnum.Custom;
            }
        }
    }
}