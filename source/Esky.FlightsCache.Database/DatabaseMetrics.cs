using System.Collections.Generic;
using System.Diagnostics.Metrics;

namespace Esky.FlightsCache.Database
{
    public static class DatabaseMetrics
    {
        private static readonly Meter _meter = new("FlightsCache.Database");

        private static readonly KeyValuePair<string, object> _owTag = new("Type", "OW");
        private static readonly KeyValuePair<string, object> _rtTag = new("Type", "RT");
        private static KeyValuePair<string, object> ServerTag(string server) => new("server", server);

        private static readonly Counter<int> _added = _meter.CreateCounter<int>("flights-added-mongo", "row",
            "Number of flights added per flight type [OW/RT]");

        private static readonly Histogram<int> _mongoFlightsUpsertTime =
            _meter.CreateHistogram<int>("flights-upsert-time-mongo", "ms");

        public static readonly Histogram<int> SqlFlightsInsertTime =
            _meter.CreateHistogram<int>("flights-insert-time-sql", "ms");

        public static readonly Counter<int> SqlDeleteOldCacheRecordsCounter =
            _meter.CreateCounter<int>("flights-old-cache-records-deleted-sql", "row");

        public static readonly Histogram<int> SqlDeleteOldCacheRecordsTime =
            _meter.CreateHistogram<int>("flights-old-cache-records-delete-time-sql", "ms");

        public static readonly Histogram<int> GenerateSourceIdTime =
            _meter.CreateHistogram<int>("flights-generate-source-id-time-sql", "ms");
        
        public static readonly Counter<long> RecalculationErrors = _meter.CreateCounter<long>("monthlyaggregator_recalculation-errors", "row",
            "Number of recalculation errors");

        public static void RecordMongoUpsertTime(int upsertTime, string server)
        {
            _mongoFlightsUpsertTime.Record(upsertTime, ServerTag(server));
        }
        
        public static void MongoAddedOw(int count, string server)
        {
            _added.Add(count, _owTag, ServerTag(server));
        }

        public static void MongoAddedRt(int count, string server)
        {
            _added.Add(count, _rtTag, ServerTag(server));
        }
    }
}