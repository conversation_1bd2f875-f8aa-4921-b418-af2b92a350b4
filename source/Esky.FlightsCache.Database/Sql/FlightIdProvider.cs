using Esky.FlightsCache.Common;
using Microsoft.Extensions.Options;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Esky.FlightsCache.Database.Sql
{
    public class FlightIdProvider : BaseSequenceProvider, IFlightIdProvider
    {
        public FlightIdProvider(IOptions<CacheRequestProcessorConfiguration> configuration)
            : base(configuration.Value.TargetDbConnectionString)
        {
        }

        public async Task<List<int>> GetIdsAsync(int rangeSize)
        {
            if (rangeSize == 0)
            {
                return await Task.FromResult(new List<int>());
            }

            return await GetInt32SequenceValuesAsync("dbo.FlightIdSequence", rangeSize);
        }
    }
}