using Dapper;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Data.SqlClient;
using System.Linq;
using System.Threading.Tasks;

namespace Esky.FlightsCache.Database.Sql
{
    public class DatabaseFacade
    {
        public static async Task ExecuteStoreProcedureNonQueryAsync(string connectionString, string procedureName,
            DbParameter[] parameters, int timeoutInSeconds = 30)
        {
            using (var connection = new SqlConnection(connectionString))
            using (var command = CreateStoredProcedureCommand(procedureName, parameters, timeoutInSeconds, connection))
            {
                await connection.OpenAsync();
                await command.ExecuteNonQueryAsync();
            }
        }

        public static async Task<int> ExecuteStoreProcedureScalarAsync(string connectionString, string procedureName,
            DbParameter[] parameters, int timeoutInSeconds = 30)
        {
            using (var connection = new SqlConnection(connectionString))
            using (var command = CreateStoredProcedureCommand(procedureName, parameters, timeoutInSeconds, connection))
            {
                await connection.OpenAsync();
                var result = await command.ExecuteScalarAsync();

                return Convert.ToInt32(result);
            }
        }

        public static async Task<int> ExecuteNonQueryAsync(string connectionString, string sql,
            DbParameter[] parameters = null, int timeoutInSeconds = 30)
        {
            using (var connection = new SqlConnection(connectionString))
            {
                using (var sqlCommand = new SqlCommand(sql, connection))
                {
                    sqlCommand.CommandType = CommandType.Text;
                    sqlCommand.CommandTimeout = timeoutInSeconds;
                    if (parameters?.Any() == true)
                    {
                        sqlCommand.Parameters.AddRange(parameters);
                    }

                    await connection.OpenAsync();
                    var result = await sqlCommand.ExecuteNonQueryAsync();

                    return Convert.ToInt32(result);
                }
            }
        }

        public static DbParameter CreateParameter(string parameterName, DbType type, object value)
        {
            DbParameter param = new SqlParameter(parameterName, type) { Value = value ?? DBNull.Value };
            return param;
        }

        public static async Task BulkInsertAsync(string connectionString, DataTable table)
        {
            using (var sqlConnection = new SqlConnection(connectionString))
            {
                await sqlConnection.OpenAsync();

                var transaction = sqlConnection.BeginTransaction();

                using (var bulk = new SqlBulkCopy(sqlConnection, SqlBulkCopyOptions.Default, transaction))
                {
                    bulk.DestinationTableName = table.TableName;
                    bulk.BatchSize = table.Rows.Count;
                    await bulk.WriteToServerAsync(table);

                    transaction.Commit();
                }
            }
        }

        public static async Task<IEnumerable<dynamic>> FillDataSetByStoreProcedureAsync(string connectionString,
            string storeProcedure, DbParameter[] parameters, int timeoutInSeconds = 30)
        {
            using (var connection = new SqlConnection(connectionString))
            {
                try
                {
                    // await connection.OpenAsync();

                    IDbConnection dbConnection = connection;

                    var dynParameters = new DynamicParameters();

                    foreach (var par in parameters)
                    {
                        dynParameters.Add(par.ParameterName, par.Value, par.DbType);
                    }

                    return await dbConnection.QueryAsync(
                        new CommandDefinition(
                            storeProcedure,
                            dynParameters,
                            commandType: CommandType.StoredProcedure,
                            commandTimeout: timeoutInSeconds
                        ));
                }
                finally
                {
                    connection.Close();
                }
            }
        }

        public static async Task<DataSet> FillDataSetAsync(string connectionString, string sql,
            DbParameter[] parameters)
        {
            using (var connection = new SqlConnection(connectionString))
            using (var command = new SqlCommand(sql, connection))
            {
                await connection.OpenAsync();

                if (parameters != null)
                {
                    command.Parameters.AddRange(parameters);
                }

                var result = await command.ExecuteReaderAsync();

                var dataSet = new DataSet();

                dataSet.Tables.Add().Load(result);

                return dataSet;
            }
        }

        private static SqlCommand CreateStoredProcedureCommand(string storeProcedureName, DbParameter[] parameters,
            int timeoutInSeconds, SqlConnection connection)
        {
            var sqlCommand = new SqlCommand(storeProcedureName, connection)
            {
                CommandType = CommandType.StoredProcedure,
                CommandTimeout = timeoutInSeconds
            };

            if (parameters != null)
            {
                sqlCommand.Parameters.AddRange(parameters);
            }

            return sqlCommand;
        }
    }
}