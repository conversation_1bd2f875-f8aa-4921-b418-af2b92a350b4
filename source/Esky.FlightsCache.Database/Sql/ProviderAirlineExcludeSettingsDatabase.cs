using Esky.FlightsCache.Common;
using Esky.FlightsCache.ResultFilters.ProviderAirlineExclude;
using Microsoft.Extensions.Options;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading.Tasks;

namespace Esky.FlightsCache.Database.Sql
{
    public class ProviderAirlineExcludeSettingsDatabase : IProviderAirlineExcludeSettingsDatabase
    {
        private readonly string _connectionString;

        public ProviderAirlineExcludeSettingsDatabase(IOptions<CacheRequestProcessorConfiguration> configuration)
        {
            _connectionString = configuration.Value.TargetDbConnectionString;
        }

        public async Task<IEnumerable<(int ProviderCode, string AirlineCode)>> GetProviderAirlineExcludeSettingsAsync()
        {
            var query = "SELECT [ProviderCode], [AirlineCode] FROM [dbo].[ProviderAirlineExcludeSettings]";

            var ds = await DatabaseFacade.FillDataSetAsync(_connectionString, query, null);

            return ds.Tables[0]
                .AsEnumerable()
                .Select(row =>
                (
                    ProviderCode: (int)(byte)row["ProviderCode"],
                    AirlineCode: ((string)row["AirlineCode"]).Trim()
                ))
                .ToArray();
        }
    }
}