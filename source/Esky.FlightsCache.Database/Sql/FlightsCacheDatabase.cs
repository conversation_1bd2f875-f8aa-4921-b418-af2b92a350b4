using Esky.FlightsCache.Common;
using Esky.FlightsCache.Common.Extensions;
using Esky.FlightsCache.MessageContract;
using Esky.FlightsCache.Processing.Helpers;
using Esky.FlightsCache.ResultFilters.FlightListFilters.RelationalDatabaseOnly;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Data.SqlClient;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Esky.FlightsCache.Database.Sql
{
    public class FlightsCacheDatabase : IFlightsCacheDatabase
    {
        private readonly string _connectionString;
        private readonly IFlightIdProvider _flightIdProvider;
        private readonly IEnumerable<IFlightListFilter> _flightListFilters;
        private readonly ILogger<FlightsCacheDatabase> _logger;

        public FlightsCacheDatabase(
            IOptions<CacheRequestProcessorConfiguration> configuration,
            IEnumerable<IFlightListFilter> flightListFilters, 
            IFlightIdProvider flightIdProvider,
            ILogger<FlightsCacheDatabase> logger)
        {
            _flightListFilters = flightListFilters;
            _flightIdProvider = flightIdProvider;
            _logger = logger;
            var config = configuration.Value;
            _connectionString = config.TargetDbConnectionString;
        }

        public async Task<int> DeleteOldCacheRecordsAsync(SourceDescription sourceDescription, CommandDataOptions commandOptions, List<int> providerCodes, long sourceId, bool isOW)
        {
            if (!providerCodes.Any())
            {
                return 0;
            }

            var startTime = Stopwatch.GetTimestamp();

            var (query, dbParameters) = CreateDeleteOldCacheRecordsQueryAndParameters(sourceDescription, commandOptions, providerCodes, sourceId, isOW);
            
            try
            {
                var ds = await DatabaseFacade.FillDataSetByStoreProcedureAsync(_connectionString, query,
                    dbParameters.ToArray());

                if (ds == null || ds.Count() == 0)
                {
                    return 0;
                }

                var firstRow = (IDictionary<string, object>)ds.First();
                var deletedRows = Convert.ToInt32(firstRow["DeletedRows"]);
                var deleteTime = (int)Stopwatch.GetElapsedTime(startTime).TotalMilliseconds;
                DatabaseMetrics.SqlDeleteOldCacheRecordsTime.Record(deleteTime);
                DatabaseMetrics.SqlDeleteOldCacheRecordsCounter.Add(deletedRows);

                return deletedRows;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message + ", Details: " + query, ex);
            }
        }

        public async Task<long> GenerateSourceIdAsync(CacheRequest commandData)
        {
            using var activity = Activities.Source.StartActivity();
            var startTimestamp = Stopwatch.GetTimestamp();

            var dbParameters = CreateGenerateSourceIdDbParameters(commandData);
            IEnumerable<dynamic> ds;

            try
            {
                ds = await DatabaseFacade.FillDataSetByStoreProcedureAsync(_connectionString, "cache.GenerateSourceId",
                    dbParameters.ToArray());
            }
            catch (Exception ex)
            {
                var serializedSourceDescription = JsonConvert.SerializeObject(commandData.SourceDescription);
                var serializedCommandOptions = JsonConvert.SerializeObject(commandData.CommandOptions);

                throw new Exception(
                    $"FlightsCacheDatabase.GenerateSourceId executed with exception for SourceDescription: {serializedSourceDescription}, CommandOptions: {serializedCommandOptions}"
                    , ex);
            }

            if (ds.Count() == 0)
            {
                throw new Exception("Can't generate source id for message");
            }

            var firstRow = (IDictionary<string, object>)ds.First();

            var generateSourceTime = (int)Stopwatch.GetElapsedTime(startTimestamp).TotalMilliseconds;
            DatabaseMetrics.GenerateSourceIdTime.Record(generateSourceTime);

            return Convert.ToInt64(firstRow["Id"]);
        }

        public async Task StoreFlights(List<FlightCache> flights, long sourceId, bool isDateRangeFeed)
        {
            using var activity = Activities.Source.StartActivity();
            if (flights == null)
            {
                throw new ArgumentNullException(nameof(flights));
            }

            foreach (var flightListFilter in _flightListFilters)
            {
                var filterResult = flightListFilter.Filter(flights, null);

                if (!string.IsNullOrWhiteSpace(filterResult.ErrorMessage))
                {
                    _logger.LogInformation("[{SourceId}] Flights filter error message: {Message}", sourceId,
                        filterResult.ErrorMessage);
                }

                flights = filterResult.Flights;
            }

            await AssignPrimaryKeysAsync(flights);

            var owDt = GetFlightOwDataTable();
            var rtDt = GetFlightRtDataTable();

            var uniqueLegs = new HashSet<string>();

            foreach (var flight in flights)
            {
                if (flight.IsRoundTrip())
                {
                    AddFlightAsRoundTripRecord(flight, rtDt, sourceId);
                }
                else
                {
                    AddFlightAsOneWayRecord(flight, owDt, sourceId, uniqueLegs);
                }
            }

            await InsertFlightsAsync(owDt, "[dbo].[InsertFlightOw]");
            await InsertFlightsAsync(rtDt, "[dbo].[InsertFlightRt]");
        }

        public Task StartImportStatisticalMeasure()
        {
            return DatabaseFacade.ExecuteStoreProcedureNonQueryAsync(_connectionString,
                "[dbo].[StartImportStatisticalMeasures]", new DbParameter[0]);
        }

        private async Task AssignPrimaryKeysAsync(IReadOnlyList<FlightCache> flightsEntities)
        {
            var numberOfRows = flightsEntities.Count;
            var ids = await _flightIdProvider.GetIdsAsync(numberOfRows);

            foreach (var x in flightsEntities.Zip(ids, (flight, id) => new { flight, id }))
            {
                x.flight.Id = x.id;
            }
        }

        public static string TruncateString(string value, int maxLength)
        {
            if (string.IsNullOrEmpty(value))
            {
                return value;
            }

            return value.Length <= maxLength ? value : value.Substring(0, maxLength);
        }

        #region Private Methods

        private void AddFlightAsOneWayRecord(FlightCache flight, DataTable flightsDataTable, long sourceId,
            ISet<string> uniqueLegs)
        {
            foreach (var leg in flight.Legs)
            {
                var row = flightsDataTable.NewRow();

                var hash = GetUniqueHash(flight.ProviderCode, leg.GetSegmentsHash(), leg.DepartureCode, leg.ArrivalCode,
                    leg.DepartureDate, leg.ArrivalDate, leg.SeparationOptions);
                if (!uniqueLegs.Add(hash))
                {
                    continue;
                }

                flightsDataTable.Rows.Add(row);

                if (leg.SeparationOptions == null)
                {
                    continue;
                }

                var adultOnePaxPrice = leg.AdultPrices?.OrderBy(x => x.MinimumNumberOfPaxes).FirstOrDefault();
                var providerBasePrice = adultOnePaxPrice?.BasePrice ?? 0M;
                var providerTaxPrice = adultOnePaxPrice?.TaxPrice ?? 0M;

                try
                {
                    row["Id"] = flight.Id;
                    row["DepartureHashCode"] = leg.GetDepartureHashCode();
                    row["ArrivalHashCode"] = leg.GetArrivalHashCode();
                    row["ExpirationDate"] = flight.ExpirationDate.AsSqlSmallDateTime();
                    row["ProviderCode"] = Convert.ToByte(flight.ProviderCode);
                    row["DepartureDate"] = leg.DepartureDate;
                    row["ArrivalDate"] = leg.ArrivalDate;
                    row["SourceId"] = sourceId;
                    row["AirlineCode"] = leg.AirlineCode;
                    row["IsOneWay"] = leg.SeparationOptions.Options.HasFlag(SeparationOptionEnum.OneWay);
                    row["IsRoundTripOutbound"] =
                        leg.SeparationOptions.Options.HasFlag(SeparationOptionEnum.RoundTripOutbound);
                    row["IsRoundTripInbound"] =
                        leg.SeparationOptions.Options.HasFlag(SeparationOptionEnum.RoundTripInbound);
                    row["MinStay"] = leg.SeparationOptions.MinStay;
                    row["RefPrice"] = decimal.ToInt32((providerBasePrice + providerTaxPrice) *
                                                      leg.ConversionRatioToReferenceCurrency * 100);
                }
                catch (Exception ex)
                {
                    throw new Exception(
                        $"Error adding row for provider: {flight.ProviderCode}, airline: '{leg.AirlineCode}' " +
                        $"({leg.DepartureDate:MM.dd} {leg.DepartureCode}-{leg.ArrivalCode}): {ex.Message}",
                        ex);
                }
            }
        }

        private void AddFlightAsRoundTripRecord(FlightCache flight, DataTable flightsDataTable, long sourceId)
        {
            var firstLeg = flight.Legs.First();
            var secondLeg = flight.Legs.Last();
            var row = flightsDataTable.NewRow();

            var firstLegAdultPrice = firstLeg.AdultPrices?.OrderBy(x => x.MinimumNumberOfPaxes).FirstOrDefault();
            var secondLegAdultPrice = secondLeg.AdultPrices?.OrderBy(x => x.MinimumNumberOfPaxes).FirstOrDefault();

            var providerBasePrice = SumPrices(firstLegAdultPrice?.BasePrice, secondLegAdultPrice?.BasePrice) ?? 0M;
            var providerTaxPrice = SumPrices(firstLegAdultPrice?.TaxPrice, secondLegAdultPrice?.TaxPrice) ?? 0M;

            row["Id"] = flight.Id;
            row["DepartureHashCode"] = firstLeg.GetDepartureHashCode();
            row["ArrivalHashCode"] = firstLeg.GetArrivalHashCode();
            row["ExpirationDate"] = flight.ExpirationDate.AsSqlSmallDateTime();
            row["ProviderCode"] = Convert.ToByte(flight.ProviderCode);
            row["DepartureDate"] = firstLeg.DepartureDate;
            row["ArrivalDate"] = firstLeg.ArrivalDate;
            row["ReturnDepartureDate"] = secondLeg.DepartureDate;
            row["ReturnArrivalDate"] = secondLeg.ArrivalDate;
            row["SourceId"] = sourceId;
            row["AirlineCode"] = firstLeg.AirlineCode;
            row["RefPrice"] = decimal.ToInt32((providerBasePrice + providerTaxPrice) *
                                              firstLeg.ConversionRatioToReferenceCurrency * 100);

            flightsDataTable.Rows.Add(row);
        }

        private List<DbParameter> CreateGenerateSourceIdDbParameters(CacheRequest commandData)
        {
            var dbParameters = new List<DbParameter>
            {
                DatabaseFacade.CreateParameter("@Name", DbType.String, commandData.SourceDescription.Name),
                DatabaseFacade.CreateParameter("@DepartureCode", DbType.String,
                    commandData.SourceDescription.SearchDepartureCode),
                DatabaseFacade.CreateParameter("@ArrivalCode", DbType.String,
                    commandData.SourceDescription.SearchArrivalCode),
                DatabaseFacade.CreateParameter("@DepartureDay", DbType.DateTime,
                    commandData.SourceDescription.SearchDepartureDate),
                DatabaseFacade.CreateParameter("@SendDate", DbType.DateTime,
                    commandData.SourceDescription.SendDate),
                DatabaseFacade.CreateParameter("@MachineName", DbType.String,
                    commandData.SourceDescription.MachineName),
                DatabaseFacade.CreateParameter("@Provider", DbType.String, commandData.SourceDescription.Provider),
                DatabaseFacade.CreateParameter("@PartnerCode", DbType.String,
                    commandData.SourceDescription.PartnerCode),
                DatabaseFacade.CreateParameter("@PaxConfiguration", DbType.String,
                    commandData.SourceDescription.PaxConfiguration),
                DatabaseFacade.CreateParameter("@GroupName", DbType.String, commandData.CommandOptions.GroupName),
                DatabaseFacade.CreateParameter("@SessionId", DbType.String,
                    commandData.SourceDescription.SessionId),
                DatabaseFacade.CreateParameter("@Flex", DbType.Int32, commandData.SourceDescription.Flex)
            };

            if (commandData.SourceDescription.SearchReturnDepartureDate.HasValue
                && commandData.SourceDescription.SearchReturnDepartureDate.Value != DateTime.MinValue)
            {
                dbParameters.Add(DatabaseFacade.CreateParameter("@ReturnDepartureDay", DbType.DateTime,
                    commandData.SourceDescription.SearchReturnDepartureDate.Value));
            }

            return dbParameters;
        }

        private static (string Query, List<DbParameter> Parameters) CreateDeleteOldCacheRecordsQueryAndParameters(SourceDescription sourceDescription, CommandDataOptions commandOptions, List<int> providerCodes, long sourceId, bool isOW)
        {
            string query;
            
            // Common parameters:
            var dbParameters = new List<DbParameter>
            {
                DatabaseFacade.CreateParameter("@ProviderCodes", DbType.String, string.Join(',', providerCodes)),
                DatabaseFacade.CreateParameter("@DepartureCode", DbType.String, sourceDescription.SearchDepartureCode),
                DatabaseFacade.CreateParameter("@ArrivalCode", DbType.String, sourceDescription.SearchArrivalCode),
                DatabaseFacade.CreateParameter("@SourceId", DbType.Int64, sourceId)
            };

            if (sourceDescription.MaxStay.HasValue)
            {
                query = "cache.DeleteRoundTripFlightsWithDaysForwardByDestination_v2";
                dbParameters.Add(DatabaseFacade.CreateParameter("@DeleteDepartureDayFrom", DbType.DateTime, commandOptions.DeleteOptions.DeleteDepartureDayFrom));
                dbParameters.Add(DatabaseFacade.CreateParameter("@DeleteDepartureDayTo", DbType.DateTime, commandOptions.DeleteOptions.DeleteDepartureDayTo));
                dbParameters.Add(DatabaseFacade.CreateParameter("@DaysForward", DbType.Int32, sourceDescription.MaxStay));
            }
            else
            {
                query = "cache.DeleteOneWayFlightsByDestination_v2";
                var airlineCodes = commandOptions.DeleteOptions.AirlineCodes.Distinct().ToList();

                dbParameters.Add(DatabaseFacade.CreateParameter("@DepartureDay", DbType.DateTime, sourceDescription.SearchDepartureDate));
                dbParameters.Add(DatabaseFacade.CreateParameter("@AirlineCodes", DbType.String, string.Join(",", airlineCodes)));
                dbParameters.Add(DatabaseFacade.CreateParameter("@Flex", DbType.Int32, sourceDescription.Flex));
                dbParameters.Add(DatabaseFacade.CreateParameter("@DeleteDepartureDayFrom", DbType.DateTime, commandOptions.DeleteOptions.DeleteDepartureDayFrom));
                dbParameters.Add(DatabaseFacade.CreateParameter("@DeleteDepartureDayTo", DbType.DateTime, commandOptions.DeleteOptions.DeleteDepartureDayTo));

                if (sourceDescription.SearchReturnDepartureDate.HasValue && sourceDescription.SearchReturnDepartureDate.Value != DateTime.MinValue)
                {
                    query = "cache.DeleteRoundTripFlightsByDestination";
                    dbParameters.Add(DatabaseFacade.CreateParameter("@ReturnDepartureDay", DbType.DateTime, sourceDescription.SearchReturnDepartureDate.Value));
                    dbParameters.Add(DatabaseFacade.CreateParameter("@DeleteReturnDepartureDayFrom", DbType.DateTime, commandOptions.DeleteOptions.DeleteReturnDepartureDayFrom));
                    dbParameters.Add(DatabaseFacade.CreateParameter("@DeleteReturnDepartureDayTo", DbType.DateTime, commandOptions.DeleteOptions.DeleteReturnDepartureDayTo));
                }
                else
                {
                    dbParameters.Add(DatabaseFacade.CreateParameter("@IsOW", DbType.Boolean, isOW));
                }
            }

            return (query, dbParameters);
        }

        private DataTable GetFlightOwDataTable()
        {
            var dataTable = new DataTable("cache.FlightOW");
            dataTable.Columns.Add(new DataColumn("Id", typeof(int)) /* { AutoIncrement = true }*/);
            dataTable.Columns.Add(new DataColumn("ExpirationDate", typeof(DateTime)));
            dataTable.Columns.Add(new DataColumn("ProviderCode", typeof(byte)));
            dataTable.Columns.Add(new DataColumn("DepartureDate", typeof(DateTime)));
            dataTable.Columns.Add(new DataColumn("ArrivalDate", typeof(DateTime)));
            dataTable.Columns.Add(new DataColumn("AirlineCode", typeof(string)) { MaxLength = 2 });
            dataTable.Columns.Add(new DataColumn("IsOneWay", typeof(bool)));
            dataTable.Columns.Add(new DataColumn("IsRoundTripOutbound", typeof(bool)));
            dataTable.Columns.Add(new DataColumn("IsRoundTripInbound", typeof(bool)));
            dataTable.Columns.Add(new DataColumn("MinStay", typeof(byte)));
            dataTable.Columns.Add(new DataColumn("SourceId", typeof(long)));
            dataTable.Columns.Add(new DataColumn("DepartureHashCode", typeof(short)));
            dataTable.Columns.Add(new DataColumn("ArrivalHashCode", typeof(short)));
            dataTable.Columns.Add(new DataColumn("RefPrice", typeof(int)));
            return dataTable;
        }


        private DataTable GetFlightRtDataTable()
        {
            var dataTable = new DataTable("cache.FlightRT");
            dataTable.Columns.Add(new DataColumn("Id", typeof(int)) /*{ AutoIncrement = true }*/);
            dataTable.Columns.Add(new DataColumn("ExpirationDate", typeof(DateTime)));
            dataTable.Columns.Add(new DataColumn("ProviderCode", typeof(byte)));
            dataTable.Columns.Add(new DataColumn("DepartureDate", typeof(DateTime)));
            dataTable.Columns.Add(new DataColumn("ArrivalDate", typeof(DateTime)));
            dataTable.Columns.Add(new DataColumn("ReturnDepartureDate", typeof(DateTime)));
            dataTable.Columns.Add(new DataColumn("ReturnArrivalDate", typeof(DateTime)));
            dataTable.Columns.Add(new DataColumn("AirlineCode", typeof(string)) { MaxLength = 2 });
            dataTable.Columns.Add(new DataColumn("SourceId", typeof(long)));
            dataTable.Columns.Add(new DataColumn("DepartureHashCode", typeof(short)));
            dataTable.Columns.Add(new DataColumn("ArrivalHashCode", typeof(short)));
            dataTable.Columns.Add(new DataColumn("RefPrice", typeof(int)));
            return dataTable;
        }

        private string GetUniqueHash(int providerCode, string segmentsHash, string departureCode, string arrivalCode,
            DateTime departureDate, DateTime arrivalDate, SeparationOptions separationOptions)
        {
            var sb = new StringBuilder();
            sb.Append(providerCode);
            sb.Append(segmentsHash);
            sb.Append(departureCode);
            sb.Append(arrivalCode);
            sb.Append(departureDate);
            sb.Append(arrivalDate);
            if (separationOptions != null)
            {
                sb.Append(separationOptions.Options);
                sb.Append(separationOptions.MinStay.ToString());
            }

            return sb.ToString();
        }

        private async Task InsertFlightsAsync(DataTable dt, string procedureName)
        {
            if (dt.AsEnumerable().Any())
            {
                var startTime = Stopwatch.GetTimestamp();

                var parameters = new DbParameter[] { new SqlParameter("@TVP", SqlDbType.Structured) { Value = dt } };
                await DatabaseFacade.ExecuteStoreProcedureNonQueryAsync(_connectionString, procedureName, parameters);

                var executionTime = (int)Stopwatch.GetElapsedTime(startTime).TotalMilliseconds;
                DatabaseMetrics.SqlFlightsInsertTime.Record(executionTime);
            }
        }

        private decimal? SumPrices(decimal? first, decimal? second)
        {
            if (!second.HasValue)
                return first.GetValueOrDefault();
            if (!first.HasValue)
                return second.Value;
            return first.Value + second.Value;
        }

        #endregion
    }
}