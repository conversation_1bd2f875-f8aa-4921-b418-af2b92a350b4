using Esky.FlightsCache.MessageContract;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Esky.FlightsCache.Database.Sql
{
    public interface IFlightsCacheDatabase
    {
        Task<int> DeleteOldCacheRecordsAsync(SourceDescription sourceDescription, CommandDataOptions commandOptions, List<int> providerCodes, long sourceId, bool isOW);

        Task StoreFlights(List<FlightCache> flights, long sourceId, bool isDateRangeFeed);
        Task<long> GenerateSourceIdAsync(CacheRequest commandData);
        Task StartImportStatisticalMeasure();
    }
}