using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Threading.Tasks;

namespace Esky.FlightsCache.Database.Sql
{
    public class BaseSequenceProvider
    {
        private readonly string _connectionString;

        public BaseSequenceProvider(string connectionString)
        {
            _connectionString = connectionString;
        }

        protected async Task<List<int>> GetInt32SequenceValuesAsync(string sequenceName, int rangeSize)
        {
            var sequenceNameParameter = DatabaseFacade.CreateParameter("@sequence_name", DbType.String, sequenceName);
            var rangeSizeParameter = DatabaseFacade.CreateParameter("@range_size", DbType.Int64, rangeSize);
            var rangeFirstValue = DatabaseFacade.CreateParameter("@range_first_value", DbType.Object, 0);
            rangeFirstValue.DbType = DbType.Object;
            rangeFirstValue.Direction = ParameterDirection.Output;
            var sequenceIncrement = DatabaseFacade.CreateParameter("@sequence_increment", DbType.Object, 0);
            sequenceIncrement.DbType = DbType.Object;
            sequenceIncrement.Direction = ParameterDirection.Output;
            var sequenceMinValue = DatabaseFacade.CreateParameter("@sequence_min_value", DbType.Object, 0);
            sequenceMinValue.DbType = DbType.Object;
            sequenceMinValue.Direction = ParameterDirection.Output;
            var sequenceMaxValue = DatabaseFacade.CreateParameter("@sequence_max_value", DbType.Object, 0);
            sequenceMaxValue.DbType = DbType.Object;
            sequenceMaxValue.Direction = ParameterDirection.Output;

            DbParameter[] dbParameters =
            {
                sequenceNameParameter, rangeSizeParameter, rangeFirstValue, sequenceIncrement, sequenceMinValue,
                sequenceMaxValue
            };
            await DatabaseFacade.ExecuteStoreProcedureNonQueryAsync(_connectionString, "sys.sp_sequence_get_range",
                dbParameters);

            var ids = new List<int>();

            var sequenceValue = (int)rangeFirstValue.Value;
            var increment = (int)sequenceIncrement.Value;
            var minValue = (int)sequenceMinValue.Value;
            var maxValue = (int)sequenceMaxValue.Value;
            for (var i = 0; i < rangeSize; i++)
            {
                ids.Add(sequenceValue);
                sequenceValue += increment;
                if (sequenceValue > maxValue || sequenceValue < minValue)
                {
                    sequenceValue -= maxValue;
                    sequenceValue += minValue;
                    sequenceValue--;
                }
            }

            return ids;
        }
    }
}