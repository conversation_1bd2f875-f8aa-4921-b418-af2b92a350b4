using Esky.FlightsCache.Common.Extensions;
using Esky.FlightsCache.MessageContract;

namespace Esky.FlightsCache.Database.Helpers;

public static class StayLengthHelper
{
    public static int GetArrivalToDeparture(FlightCacheLeg outboundLeg, FlightCacheLeg inboundLeg)
    {
        return (inboundLeg.DepartureDate.AsIsoDate() - outboundLeg.ArrivalDate.AsIsoDate()).Days;
    }

    public static int GetDepartureToDeparture(FlightCacheLeg outboundLeg, FlightCacheLeg inboundLeg)
    {
        return (inboundLeg.DepartureDate.AsIsoDate() - outboundLeg.DepartureDate.AsIsoDate()).Days;
    }
}