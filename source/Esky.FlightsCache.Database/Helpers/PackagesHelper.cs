using Esky.FlightsCache.MessageContract;
using System;

namespace Esky.FlightsCache.Database.Helpers;

public static class PackagesHelper
{
    private const int _lateArrivalHour = 4;
    private const int _lateReturnDepartureHour = 4;
    
    public static int GetStayLength(FlightCacheLeg outboundLeg, FlightCacheLeg inboundLeg)
    {
        var returnDepartureDay = inboundLeg.DepartureDate.AddHours(-_lateReturnDepartureHour).Date;
        var arrivalDay = outboundLeg.ArrivalDate.AddHours(-_lateArrivalHour).Date;

        return (returnDepartureDay - arrivalDay).Days;
    }
    
    public static DateTime GetCheckIn(FlightCacheLeg leg)
    {
        return leg.ArrivalDate.AddHours(-_lateArrivalHour).Date;
    }
    
    public static DateTime GetCheckOut(FlightCacheLeg leg)
    {
        return leg.DepartureDate.AddHours(-_lateReturnDepartureHour).Date;
    }
}