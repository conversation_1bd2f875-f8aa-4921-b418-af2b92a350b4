using Esky.FlightsCache.Database.FlightOffers.Model;
using System;
using System.Collections.Generic;

namespace Esky.FlightsCache.Database.Helpers
{
    public static class LengthOfStayHelper
    {
        public static IEnumerable<LengthOfStayTypeEnum> GetLengthOfStayTypes(DateTime departureDate,
            DateTime returnDate, bool isIntercontinental)
        {
            var stayLength = (returnDate - departureDate).Days;

            if (stayLength <= 30)
            {
                yield return LengthOfStayTypeEnum.Any;

                if ((departureDate.DayOfWeek == DayOfWeek.Friday && stayLength <= 3) ||
                    (departureDate.DayOfWeek == DayOfWeek.Saturday && stayLength <= 2))
                {
                    yield return LengthOfStayTypeEnum.Weekends;
                }

                if (stayLength <= 3)
                {
                    yield return LengthOfStayTypeEnum.Range0To3Days;
                }
                else if (stayLength <= 7)
                {
                    yield return LengthOfStayTypeEnum.Range4To7Days;
                }
                else if (stayLength <= 14)
                {
                    yield return LengthOfStayTypeEnum.Range8To14Days;
                }
                else if (stayLength <= 21)
                {
                    yield return LengthOfStayTypeEnum.Range15To21Days;
                }

                if (isIntercontinental)
                {
                    if (stayLength >= 7 && stayLength <= 30)
                    {
                        yield return LengthOfStayTypeEnum.Custom;
                    }
                }
                else
                {
                    if (stayLength >= 1 && stayLength <= 14)
                    {
                        yield return LengthOfStayTypeEnum.Custom;
                    }
                }
            }
        }
    }
}