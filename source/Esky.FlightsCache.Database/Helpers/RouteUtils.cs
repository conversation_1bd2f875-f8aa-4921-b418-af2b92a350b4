using System;

namespace Esky.FlightsCache.Database.Helpers
{
    public static class RouteUtils
    {
        public static (string FirstCode, string SecondCode) ParseRoute(string route)
        {
            var codes = route.Split('-');
            if (codes.Length != 2)
            {
                throw new ArgumentException($"Route '{route}' not in format XXX-YYY");
            }

            return (FirstCode: codes[0], SecondCode: codes[1]);
        }

        public static string InvertRoute(this string route)
        {
            var codes = ParseRoute(route);
            return $"{codes.SecondCode}-{codes.FirstCode}";
        }
    }
}