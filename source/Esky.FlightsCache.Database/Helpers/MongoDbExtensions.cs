using MongoDB.Driver;
using System;
using System.Linq.Expressions;

namespace Esky.FlightsCache.Database.Helpers
{
    public static class MongoDbExtensions
    {
        public static IMongoCollection<T> EnsureTtlIndex<T>(
            this IMongoCollection<T> collection,
            Expression<Func<T, object>> fieldSelector,
            string name,
            TimeSpan expireAfter)
        {
            collection.Indexes
                .CreateOneAsync(
                    new CreateIndexModel<T>(
                        new IndexKeysDefinitionBuilder<T>().Ascending(fieldSelector),
                        new CreateIndexOptions { ExpireAfter = expireAfter, Name = name, Background = true })
                );
            return collection;
        }

        public static IMongoCollection<T> EnsureIndex<T>(this IMongoCollection<T> collection, string indexName,
            Func<IndexKeysDefinitionBuilder<T>, IndexKeysDefinition<T>> indexDefinition,
            Expression<Func<T, bool>> partialFilter = null,
            bool unique = false)
        {
            collection.Indexes
                .CreateOneAsync(
                    new CreateIndexModel<T>(
                        indexDefinition(new IndexKeysDefinitionBuilder<T>()),
                        new CreateIndexOptions<T>
                        {
                            Name = indexName,
                            Background = true,
                            Unique = unique,
                            PartialFilterExpression =
                                partialFilter != null ? Builders<T>.Filter.Where(partialFilter) : null
                        })
                );
            return collection;
        }
    }
}