using System.Text.RegularExpressions;

namespace Esky.FlightsCache.Database.Helpers
{
    internal static partial class ModelExtensions
    {
        [GeneratedRegex("(^flightsearch-api)|(^SearchFlightsCommand_SOAP$)")]
        private static partial Regex OrganicSearchFlightsSourceRegex();
        
        public static bool IsOrganic(string source)
        {
            if (OrganicSearchFlightsSourceRegex().IsMatch(source))
            {
                return true;
            }

            return false;
        }
    }
}