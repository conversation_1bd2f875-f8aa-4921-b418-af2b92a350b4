using Esky.FlightsCache.Common.Extensions;
using Esky.FlightsCache.Database.FlightOffers.Model;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Esky.FlightsCache.Database.Aggregations
{
    public class RouteToCalculate
    {
        private readonly GeneralRouteDefinition _routeDefinition;

        public RouteToCalculate(FlightOfferOneWay[] owFlightOffers)
        {
            var route = owFlightOffers.First().Route;
            _routeDefinition = new GeneralRouteDefinition((route.MultiportDeparture, route.Departure), (route.MultiportArrival, route.Arrival));
            PeriodFrom = owFlightOffers.Min(x => x.DepartureDate).BeginningOfTheMonth();
            PeriodTo = owFlightOffers.Max(x => x.DepartureDate).BeginningOfTheMonth();
            Providers = owFlightOffers.Select(x => x.Provider).Distinct().ToArray();
            ModifiedFlights = owFlightOffers;
        }
        
        public RouteToCalculate(RouteUpdateEvent[] events)
        {
            _routeDefinition = events.First().Route;
            PeriodFrom = events.Min(x => x.YearMonthDate).BeginningOfTheMonth();
            PeriodTo = events.Max(x => x.YearMonthDate).BeginningOfTheMonth();
            Providers = events.SelectMany(x => x.Providers).Distinct().ToArray();
            ModifiedFlights = [];
        }
        
        public IEnumerable<int> Providers { get; }
        public DateTime PeriodFrom { get; }
        public DateTime PeriodTo { get; }
        public FlightOfferOneWay[] ModifiedFlights { get; }

        public string AirportCode1 => _routeDefinition.First.AirportCode;
        public string AirportCode2 => _routeDefinition.Second.AirportCode;
        public string MultiportRoute => _routeDefinition.MultiportRouteString;
        public string AirportRoute => $"{AirportCode1}-{AirportCode2}";
        public string ReturnAirportRoute => $"{AirportCode2}-{AirportCode1}";
    }
}