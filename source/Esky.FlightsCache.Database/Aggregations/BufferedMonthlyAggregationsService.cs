using Esky.FlightsCache.Common;
using Esky.FlightsCache.Database.FlightOffers.Model;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Esky.FlightsCache.Database.Aggregations;

public class BufferedMonthlyAggregationsService(
    IMonthlyAggregationsService decorated,
    IRouteAggregationBuffer routeAggregationBuffer,
    BulkAggregationConfiguration config)
    : IMonthlyAggregationsService
{
    public Task RecalculateMonthlyAggregates(IReadOnlyCollection<FlightOfferOneWay> owFlightOffers)
    {
        if (!config.IsEnabledForOneWay)
        {
            return decorated.RecalculateMonthlyAggregates(owFlightOffers);
        }

        var details = CreateUpdateEvents(owFlightOffers);

        return routeAggregationBuffer.Insert(RouteTypes.OneWay, details);

    }

    public Task RecalculateMonthlyAggregates(IReadOnlyCollection<FlightOfferRoundTrip> rtFlightOffers)
    {
        return decorated.RecalculateMonthlyAggregates(rtFlightOffers);
    }
        
    private static RouteUpdateEvent[] CreateUpdateEvents(IEnumerable<FlightOfferOneWay> owCells)
    {
        return owCells
            .GroupBy(x => new
            {
                Route = new GeneralRouteDefinition((x.Route.MultiportDeparture, x.Route.Departure), (x.Route.MultiportArrival, x.Route.Arrival)),
                Year = (short)x.DepartureDay.Year,
                Month = (byte)x.DepartureDay.Month
            })
            .Select(g => new RouteUpdateEvent(
                g.Key.Route,
                g.Key.Year,
                g.Key.Month,
                g.Select(c => c.Provider).Distinct().ToArray()))
            .ToArray();
    }
}