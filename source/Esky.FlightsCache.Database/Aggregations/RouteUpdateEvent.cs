using System;
using System.Globalization;
using System.Linq;
using System.Text.RegularExpressions;

namespace Esky.FlightsCache.Database.Aggregations;

public partial class RouteUpdateEvent(GeneralRouteDefinition routeDesDefinition, int year, int month, int[] providers)
{
    [GeneratedRegex(@"^(?:(?<dTopRoute>[A-Z]{3,4})-(?<aTopRoute>[A-Z]{3,4}))?(?<dRoute>[A-Z]{3})(?<aRoute>[A-Z]{3})(?<month>\d{4})(?:(?<providers>\d+),?)*$")]
    private static partial Regex EventParseRegex();

    public GeneralRouteDefinition Route { get; } = routeDesDefinition;

    public int[] Providers { get; } = providers;

    public DateTime YearMonthDate { get; } = new(year, month, 1);

    public static RouteUpdateEvent Parse(string value)
    {
        var match = EventParseRegex().Match(value);
        if (!match.Success)
        {
            throw new ApplicationException($"Cannot parse {nameof(RouteUpdateEvent)}:{value}");
        }

        var route = new GeneralRouteDefinition(
            (MultiportCode: match.Groups["dTopRoute"].Value, AirportCode: match.Groups["dRoute"].Value),
            (MultiportCode: match.Groups["aTopRoute"].Value, AirportCode: match.Groups["aRoute"].Value));

        var yearMonthDate = DateTime.ParseExact($"20{match.Groups["month"].Value}", "yyyyMM",
            CultureInfo.InvariantCulture); // TODO: modify at the turn of centuries
        var providers = match.Groups["providers"].Captures.Select(c => int.Parse(c.Value)).ToArray();

        return new RouteUpdateEvent(route, yearMonthDate.Year, yearMonthDate.Month, providers);
    }

    public override string ToString()
    {
        var encodedRoute = $"{Route.MultiportRouteString}{Route.AirportShortRouteString}";

        return $"{encodedRoute}{YearMonthDate:yyMM}{string.Join(",", Providers)}";
    }
}