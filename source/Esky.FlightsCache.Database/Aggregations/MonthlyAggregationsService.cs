using Esky.FlightsCache.Common;
using Esky.FlightsCache.Common.Extensions;
using Esky.FlightsCache.Common.Model;
using Esky.FlightsCache.Database.Aggregations.Model;
using Esky.FlightsCache.Database.FlightOffers.Model;
using Esky.FlightsCache.Database.Helpers;
using Esky.FlightsCache.Database.Routes;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using MongoDB.Driver;
using MongoDB.Driver.Linq;
using MoreLinq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using DateTimeExtensions = Esky.FlightsCache.Common.Extensions.DateTimeExtensions;

namespace Esky.FlightsCache.Database.Aggregations;

public interface IMonthlyAggregationsService
{
    Task RecalculateMonthlyAggregates(IReadOnlyCollection<FlightOfferOneWay> owFlightOffers);
    Task RecalculateMonthlyAggregates(IReadOnlyCollection<FlightOfferRoundTrip> rtFlightOffers);
}

public class MonthlyAggregationsService(
    IOptions<AggregationConfiguration> configuration,
    ILogger<MonthlyAggregationsService> logger,
    DatabaseContext dbContext) : IMonthlyAggregationsService, IRouteAggregationService
{
    private readonly AggregationConfiguration _configuration = configuration.Value;
    private readonly IMongoCollection<RouteDefinition> _routeDefinitions = dbContext.RouteDefinitions;
    private readonly IMongoCollection<FlightOfferOneWay> _flightOffersOneWayCollection = dbContext.FlightOffersOneWayCollection;
    private readonly IMongoCollection<FlightOfferRoundTrip> _flightOffersRoundTripCollection = dbContext.FlightOffersRoundTripCollection;
    private readonly IMongoCollection<MonthlyRoundTripFlightOffer> _monthlyRoundTripAggregationCollection = dbContext.MonthlyRoundTripAggregationCollection;

    public Task RecalculateMonthlyAggregates(IReadOnlyCollection<FlightOfferOneWay> owFlightOffers)
    {
        if (_configuration.IsAggregationFromOwDisabled || owFlightOffers.Count == 0)
        {
            return Task.CompletedTask;
        }
        
        var routesToCalculate = owFlightOffers
            .GroupBy(x => RouteUtils.ParseRoute(x.Route.Id))
            .Select(x => new RouteToCalculate(x.ToArray()));

        return routesToCalculate.ForEachThrottledAsync(_configuration.DegreeOfParallelism, RecalculateSingleRoute);
    }

    public async Task RecalculateMonthlyAggregates(IReadOnlyCollection<FlightOfferRoundTrip> rtFlightOffers)
    {
        if (_configuration.IsRoundTripAggregationDisabled || rtFlightOffers.Count == 0)
        {
            return;
        }

        var routesDictionary = await GetRoutesDictionaryAsync(rtFlightOffers.Select(x => x.Route.Id).Distinct().ToArray());

        var updatedDays = rtFlightOffers.Select(x => new { x.DepartureDay, ReturnDate = x.DepartureDay.AddDays(x.StayLength.DepartureToDeparture) }).Distinct().ToArray();

        var newMonthlyAggregates = rtFlightOffers
            .SelectMany(x => x.LengthOfStayTypes.Select(y => new RoundTripMonthlyAggregation(x, y)))
            .GetMinElementByGroup(x => x.Key, x => x.RoundTrip.RefPrice, x => x.RoundTrip.DepartureDay,
                x => x.RoundTrip.StayLength.DepartureToDeparture)
            .Select(x => ConstructMonthlyRoundTripEntity(x, routesDictionary))
            .ToArray();

        var existingAggregates = await GetStoredMonthlyAggregatesAsync(newMonthlyAggregates.Select(x => x.Id));

        RequiredUpdateActionEnum GetRequiredUpdateAction(MonthlyRoundTripFlightOffer newAggregate, MonthlyRoundTripFlightOffer existingAggregate)
        {
            if (existingAggregate == null)
            {
                return RequiredUpdateActionEnum.ProceedWithUpdate;
            }

            if (newAggregate.RefPrice <= existingAggregate.RefPrice)
            {
                return RequiredUpdateActionEnum.ProceedWithUpdate;
            }

            if (updatedDays.Any(y => existingAggregate.Meta.DepartureDay == y.DepartureDay && existingAggregate.Meta.ReturnDay == y.ReturnDate))
            {
                return RequiredUpdateActionEnum.FullRecalculationRequired;
            }

            return RequiredUpdateActionEnum.SkipUpdate;
        }

        var lookup = newMonthlyAggregates.ToLookup(x => GetRequiredUpdateAction(x, existingAggregates.GetValueOrDefault(x.Id)));

        var freshRtAggregates = await lookup[RequiredUpdateActionEnum.FullRecalculationRequired]
            .Select(GetRoundTripMonthlyAggregationFromDb)
            .Select(async x => ConstructMonthlyRoundTripEntity(await x, routesDictionary))
            .WhenAll();

        newMonthlyAggregates = freshRtAggregates.Where(x => x is not null).Concat(lookup[RequiredUpdateActionEnum.ProceedWithUpdate]).ToArray();

        await UpsertRoundTripMonthlyAggregationsAsync(newMonthlyAggregates);
    }

    public async Task<int> RecalculateMonthlyAggregates(IReadOnlyCollection<RouteUpdateEvent> events)
    {
        if (events.Count == 0) return 0;

        var routes = GetChangedOneWayRoutes(events).ToArray();

        await routes.ForEachThrottledAsync(
            _configuration.DegreeOfParallelism,
            RecalculateSingleRoute);

        return routes.Length;
    }
    
    private async Task<Dictionary<string, MonthlyRoundTripFlightOffer>> GetStoredMonthlyAggregatesAsync(
        IEnumerable<string> ids)
    {
        var monthlyAggregates = await _monthlyRoundTripAggregationCollection.FindAsync(Builders<MonthlyRoundTripFlightOffer>.Filter.In(x => x.Id, ids));
        return (await monthlyAggregates.ToListAsync()).ToDictionary(x => x.Id);
    }

    private async Task<RoundTripMonthlyAggregation> GetRoundTripMonthlyAggregationFromDb(MonthlyRoundTripFlightOffer staleAggregate)
    {
        var now = DateTime.UtcNow;

        var totalDays = (int)(staleAggregate.Meta.Month.AddMonths(1) - staleAggregate.Meta.Month).TotalDays;
        var departureDays = Enumerable.Range(0, totalDays).Select(x => staleAggregate.Meta.Month.AddDays(x)).ToArray();

        var cheapestRoundTrip = await _flightOffersRoundTripCollection
            .Aggregate(new AggregateOptions { Hint = "calendar" })
            .Match(x =>
                x.Route.Multiport == staleAggregate.Meta.MultiportRoute &&
                x.Route.Id == staleAggregate.RouteDefinition.Id &&
                x.Provider == staleAggregate.Meta.Provider &&
                departureDays.Contains(x.DepartureDay) &&
                x.LengthOfStayTypes.Contains(staleAggregate.Meta.LengthOfStayType) &&
                x.Supplier == staleAggregate.Meta.Supplier &&
                x.ExpirationDate > now)
            .Sort(Builders<FlightOfferRoundTrip>.Sort.Ascending(x => x.RefPrice))
            .FirstOrDefaultAsync();

        if (cheapestRoundTrip is null) return null;

        return new RoundTripMonthlyAggregation(cheapestRoundTrip, staleAggregate.Meta.LengthOfStayType);
    }

    private static MonthlyRoundTripFlightOffer ConstructMonthlyRoundTripEntity(RoundTripMonthlyAggregation x, Dictionary<string, RouteDefinition> routesDictionary)
    {
        if (x == null) return null;

        var routeDefinition = routesDictionary.GetValueOrDefault(x.Key.Route) ?? new RouteDefinition { Id = x.Key.Route };

        var standardScore = CalculateStandardScore(x.RoundTrip.RefPrice, routeDefinition.StatisticalMeasures);
        var countryStandardScore = CalculateStandardScore(x.RoundTrip.RefPrice, routeDefinition.CountryStatisticalMeasures);

        var flightDetails = new FlightDetails
        {
            PriceElements = x.RoundTrip.PriceElements,
            Currency = x.RoundTrip.Currency,
            Provider = x.RoundTrip.Provider,
            Legs = [x.RoundTrip.OutboundLeg, x.RoundTrip.InboundLeg],
            FlightOfferId = x.RoundTrip.Id
        };

        return new MonthlyRoundTripFlightOffer
        {
            Meta = new MonthlyRoundTripFlightOffer.AggregationMetadata
            {
                LengthOfStayType = x.Key.LengthOfStayType,
                Month = x.Key.Month,
                DepartureDay = x.RoundTrip.DepartureDay,
                ReturnDay = x.RoundTrip.DepartureDay.AddDays(x.RoundTrip.StayLength.DepartureToDeparture),
                Provider = x.Key.Provider,
                Supplier = x.Key.Supplier,
                MultiportRoute = x.RoundTrip.Route.Multiport
            },
            RefPrice = x.RoundTrip.RefPrice,
            Flights = [flightDetails],
            ModificationDate = DateTime.UtcNow,
            ExpirationDate = DateTimeExtensions.Min(x.RoundTrip.ExpirationDate, x.RoundTrip.DepartureDay),
            RefreshDate = x.RoundTrip.RefreshDate,
            RouteDefinition = new RouteDefinition
            {
                Id = routeDefinition.Id, ExtraElements = routeDefinition.ExtraElements, IsSmartOfferRoute = routeDefinition.IsSmartOfferRoute || IsDirectFlight(flightDetails)
            },
            StandardScore = ToIntegerStandardScore(standardScore),
            CountryStandardScore = ToIntegerStandardScore(countryStandardScore),
            MeanRefPrice = Convert.ToInt32(routeDefinition.StatisticalMeasures?.MeanRefPrice)
        };
    }

    private IEnumerable<RouteToCalculate> GetChangedOneWayRoutes(IEnumerable<RouteUpdateEvent> events)
    {
        switch (_configuration.BulkAggregation.GroupingMode)
        {
            case BulkAggregationGroupingMode.RouteOnly:
                return events
                    .GroupBy(x => x.Route.AirportCodes)
                    .Select(x => new RouteToCalculate(x.ToArray()));

            case BulkAggregationGroupingMode.RouteAndMonth:
                return events
                    .GroupBy(x => new { Route = x.Route.AirportCodes, Period = x.YearMonthDate })
                    .Select(x => new RouteToCalculate(x.ToArray()));

            case BulkAggregationGroupingMode.RouteAndConsecutiveMonths:
                return events
                    .OrderBy(x => x.YearMonthDate)
                    .GroupBy(x => x.Route.AirportCodes)
                    .Select(group => new
                    {
                        Route = group.Key,
                        Events = group.Segment((current, previous, _) =>
                            previous.YearMonthDate.AddMonths(1) < current.YearMonthDate)
                    })
                    .SelectMany(group => group.Events,
                        (_, ev) => new RouteToCalculate(ev.ToArray()));

            default:
                throw new ArgumentOutOfRangeException(nameof(_configuration.BulkAggregation.GroupingMode));
        }
    }

    private async Task RecalculateSingleRoute(RouteToCalculate route)
    {
        try
        {
            var getRoutesTask = GetRoutesDictionaryAsync(route.AirportRoute, route.ReturnAirportRoute);

            var owFlightOffers = await FetchOneWayFlightOffers(route);

            var routesDictionary = await getRoutesTask;
            if (routesDictionary.Count == 0) return;

            await CalculateSingleRouteMultiTicketAggregations(route, owFlightOffers, routesDictionary);
        }
        catch (Exception ex)
        {
            logger.LogWarning(ex,
                "Unable to recalculate single route ({AirportRoute}, period:{PeriodFrom}-{PeriodTo} providers:{ProviderCodes})",
                route.AirportRoute,
                route.PeriodFrom.ToString("yyyy.MM"),
                route.PeriodTo.ToString("yyyy.MM"),
                string.Join(',', route.Providers)
            );

            DatabaseMetrics.RecalculationErrors.Add(1);
        }
    }

    private async Task CalculateSingleRouteMultiTicketAggregations(RouteToCalculate route, List<OneWayFlightOfferRead> owFlightOffers, Dictionary<string, RouteDefinition> routesDictionary)
    {
        if (_configuration.IsMultiticketAggregationDisabled)
        {
            return;
        }
        
        ICollection<MonthlyRoundTripFlightOffer> newAggregates = Enumerable.Concat(
                await CombineAsMonthlyMultiTicketAggregates(owFlightOffers, routesDictionary, route),
                await CombineAsProviderMonthlyMultiTicketAggregates(owFlightOffers, routesDictionary, route)
            )
            .Where(x => AggregationIsPartOfModification(x, route.ModifiedFlights))
            .ToArray();

        await UpsertRoundTripMonthlyAggregationsAsync(newAggregates);
    }

    private async Task<IEnumerable<MonthlyRoundTripFlightOffer>> CombineAsProviderMonthlyMultiTicketAggregates(List<OneWayFlightOfferRead> owFlightOffers, Dictionary<string, RouteDefinition> routesDictionary, RouteToCalculate route)
    {
        if (_configuration.IsProviderMultiticketAggregationDisabled)
        {
            return [];
        }

        // Filter out virtual-providers
        var providerCodes = route.Providers.Where(pc => pc < byte.MaxValue).ToArray();

        var readCells = owFlightOffers
            .Where(x => x.DepartureDay >= route.PeriodFrom.AddMonths(-1))
            .Where(x => x.DepartureDay < route.PeriodTo.AddMonths(1))
            // only providers where cell updated
            .Where(x => providerCodes.Contains(x.Provider))
            .Where(x => x.IsOneWay || x.IsOutbound)
            // combine as multi-tickets and unwind stay types
            .SelectMany(
                x1 => owFlightOffers
                    .Where(x2 => x2.Route != x1.Route)
                    .Where(x2 => x2.Provider == x1.Provider)
                    .Where(x2 => x2.Supplier == x1.Supplier)
                    .Where(x2 => x2.MinStay == x1.MinStay)
                    .Where(x2 => (x1.IsOneWay && x2.IsOneWay) || (x1.IsOutbound && x2.IsInbound))
                    .Where(x2 => x2.DepartureDay >= x1.DepartureDay.AddDays(Math.Max(1, x1.MinStay)))
                    .Where(x2 => x2.DepartureDay <= x1.DepartureDay.AddDays(30))
                    .SelectMany(x2 => GetLengthOfStayTypes(x1, x2).Select(sl => new { Inbound = x2, LengthOfStayType = sl }))
                    // get cheapest inbound per stay type
                    .GetMinElementByGroup(x => x.LengthOfStayType, x => x.Inbound.RefPrice, x => x.Inbound.DepartureDay)
                    .Select(x => new RoundTripMultiTicketMonthlyAggregation(x1, x.Inbound, x.LengthOfStayType, x1.Provider, x1.Supplier)))
            // get cheapest multi-ticket per month
            .GetMinElementByGroup(x => x.Key, x => x.RefPrice, x => x.Outbound.DepartureDay).ToList();

        var flightIds = readCells.SelectMany(x => new[] { x.Inbound.Id, x.Outbound.Id });

        var flightsDict = (await FetchOneWayFlightOffers(route, flightIds))
            .GroupBy(x => x.Id, (_, group) => group.First())
            .ToDictionary(x => x.Id);

        return readCells.Select(x => ConstructMonthlyRoundTripEntity(x, routesDictionary, flightsDict));
    }

    private async Task<IEnumerable<MonthlyRoundTripFlightOffer>> CombineAsMonthlyMultiTicketAggregates(List<OneWayFlightOfferRead> owFlightOffers, Dictionary<string, RouteDefinition> routesDictionary, RouteToCalculate route)
    {
        if (_configuration.IsGlobalMultiticketAggregationDisabled)
        {
            return [];
        }

        var readCells = owFlightOffers
            .Where(x => x.IsOneWay)
            .Where(x => x.DepartureDay >= route.PeriodFrom.AddMonths(-1))
            .Where(x => x.DepartureDay < route.PeriodTo.AddMonths(1))
            // get cheapest per day
            .GetMinElementByGroup(x => new { x.Route, x.DepartureDay }, x => x.RefPrice)
            // combine as multi-tickets and unwind stay types
            .SelectMany(
                x1 => owFlightOffers
                    .Where(x2 => x2.Route != x1.Route)
                    .Where(x2 => x2.DepartureDay >= x1.DepartureDay.AddDays(1))
                    .Where(x2 => x2.DepartureDay <= x1.DepartureDay.AddDays(30))
                    .SelectMany(x2 => GetLengthOfStayTypes(x1, x2).Select(sl => new { Inbound = x2, LengthOfStayType = sl }))
                    // get cheapest inbound per stay type
                    .GetMinElementByGroup(x => x.LengthOfStayType, x => x.Inbound.RefPrice, x => x.Inbound.DepartureDay)
                    .Select(x => new RoundTripMultiTicketMonthlyAggregation(x1, x.Inbound, x.LengthOfStayType)))
            // get cheapest multi-ticket per month
            .GetMinElementByGroup(x => x.Key, x => x.RefPrice, x => x.Outbound.DepartureDay)
            .Where(x => x.Outbound.Provider != x.Inbound.Provider).ToList();

        var flightIds = readCells.SelectMany(x => new[] { x.Inbound.Id, x.Outbound.Id });

        var flightsDict = (await FetchOneWayFlightOffers(route, flightIds)).ToDictionary(x => x.Id);

        return readCells.Select(x => ConstructMonthlyRoundTripEntity(x, routesDictionary, flightsDict));
    }

    private MonthlyRoundTripFlightOffer ConstructMonthlyRoundTripEntity(RoundTripMultiTicketMonthlyAggregation x, Dictionary<string, RouteDefinition> routesDictionary, Dictionary<string, FlightOfferOneWay> flightsDict)
    {
        if (flightsDict.Count < 2) return null;

        var routeDefinition = routesDictionary.GetValueOrDefault(x.Key.Route) ?? new RouteDefinition { Id = x.Key.Route };

        var standardScore = CalculateStandardScore(x.RefPrice, routeDefinition.StatisticalMeasures);
        var countryStandardScore = CalculateStandardScore(x.RefPrice, routeDefinition.CountryStatisticalMeasures);

        var outboundFlight = flightsDict[x.Outbound.Id];
        var inboundFlight = flightsDict[x.Inbound.Id];

        var outboundFlightDetails = new FlightDetails
        {
            PriceElements = outboundFlight.PriceElements,
            Currency = outboundFlight.Currency,
            Provider = x.Outbound.Provider,
            Legs = [outboundFlight.OutboundLeg],
            FlightOfferId = outboundFlight.Id
        };
        var inboundFlightDetails = new FlightDetails
        {
            PriceElements = inboundFlight.PriceElements,
            Currency = inboundFlight.Currency,
            Provider = x.Inbound.Provider,
            Legs = [inboundFlight.OutboundLeg],
            FlightOfferId = inboundFlight.Id
        };

        return new MonthlyRoundTripFlightOffer
        {
            Meta = new MonthlyRoundTripFlightOffer.AggregationMetadata
            {
                LengthOfStayType = x.Key.LengthOfStayType,
                Month = x.Key.Month,
                DepartureDay = x.Outbound.DepartureDay,
                ReturnDay = x.Inbound.DepartureDay,
                Provider = x.Key.Provider,
                Supplier = x.Key.Supplier
            },
            RefPrice = x.RefPrice,
            Flights = [outboundFlightDetails, inboundFlightDetails],
            ModificationDate = DateTime.UtcNow,
            ExpirationDate = DateTimeExtensions.Min(outboundFlight.ExpirationDate, inboundFlight.ExpirationDate),
            RefreshDate = DateTimeExtensions.Min(outboundFlight.RefreshDate, inboundFlight.RefreshDate),
            RouteDefinition = new RouteDefinition
            {
                Id = routeDefinition.Id,
                ExtraElements = routeDefinition.ExtraElements,
                IsSmartOfferRoute = routeDefinition.IsSmartOfferRoute || IsDirectFlight(outboundFlightDetails, inboundFlightDetails)
            },
            StandardScore = ToIntegerStandardScore(standardScore),
            CountryStandardScore = ToIntegerStandardScore(countryStandardScore),
            MeanRefPrice = Convert.ToInt32(routeDefinition.StatisticalMeasures?.MeanRefPrice)
        };
    }

    private static IEnumerable<LengthOfStayTypeEnum> GetLengthOfStayTypes(OneWayFlightOfferRead outboundFlight, OneWayFlightOfferRead inboundFlight)
    {
        return LengthOfStayHelper
            .GetLengthOfStayTypes(outboundFlight.DepartureDay, inboundFlight.DepartureDay, outboundFlight.IsIntercontinental)
            .Cast<LengthOfStayTypeEnum>();
    }
    
    private static decimal CalculateStandardScore(int price, RouteDefinition.StatisticalMeasure statisticalMeasures)
    {
        return statisticalMeasures == null || statisticalMeasures.StdDevRefPrice == 0
            ? decimal.Zero
            : (price - statisticalMeasures.MeanRefPrice) / statisticalMeasures.StdDevRefPrice;
    }
    
    /// <summary>
    /// in case of overflow returns <see cref="int.MinValue"/> or <see cref="int.MaxValue"/> according to passed value
    /// </summary>
    internal static int ToIntegerStandardScore(decimal? value)
    {
        const int multiplier = 1_000_000;
        const decimal min = (decimal)int.MinValue / multiplier;
        const decimal max = (decimal)int.MaxValue / multiplier;

        return value switch
        {
            null => 0,
            < min => int.MinValue,
            > max => int.MaxValue,
            _ => (int)(value * multiplier)
        };
    }

    private Task UpsertRoundTripMonthlyAggregationsAsync(ICollection<MonthlyRoundTripFlightOffer> docs)
    {
        if (docs.Count == 0) return Task.CompletedTask;

        var bulkOps = docs.Select(document =>
            new ReplaceOneModel<MonthlyRoundTripFlightOffer>(Builders<MonthlyRoundTripFlightOffer>.Filter.Where(x => x.Id == document.Id), document) { IsUpsert = true });
        return _monthlyRoundTripAggregationCollection.BulkWriteAsync(bulkOps, new BulkWriteOptions { IsOrdered = false });
    }

    private static bool AggregationIsPartOfModification(MonthlyRoundTripFlightOffer aggregation, FlightOfferOneWay[] modifiedFlights)
    {
        return modifiedFlights.Length == 0 ||
               modifiedFlights.Any(ow =>
                   aggregation.Meta.DepartureDay == ow.DepartureDay &&
                   aggregation.Flights.First().Provider == ow.Provider &&
                   aggregation.RouteDefinition.Id == ow.Route.Id) ||
               modifiedFlights.Any(ow =>
                   aggregation.Meta.ReturnDay == ow.DepartureDay &&
                   aggregation.Flights.Last().Provider == ow.Provider &&
                   aggregation.RouteDefinition.Id != ow.Route.Id);
    }

    private async Task<Dictionary<string, RouteDefinition>> GetRoutesDictionaryAsync(params string[] routes)
    {
        var routesDef = await _routeDefinitions.FindAsync(Builders<RouteDefinition>.Filter.In(x => x.Id, routes));
        return (await routesDef.ToListAsync()).ToDictionary(x => x.Id);
    }
    
    private async Task<List<OneWayFlightOfferRead>> FetchOneWayFlightOffers(RouteToCalculate route)
    {
        var outbound = GetFlights(route.MultiportRoute, route.AirportCode1, route.AirportCode2);
        var inbound = GetFlights(route.MultiportRoute.InvertRoute(), route.AirportCode2, route.AirportCode1);

        await Task.WhenAll(outbound, inbound);

        return [..outbound.Result, ..inbound.Result];

        Task<List<OneWayFlightOfferRead>> GetFlights(string multiportRoute, string routeAirportCode1, string routeAirportCode2)
        {
            var now = DateTime.UtcNow;

            var query = _flightOffersOneWayCollection
                .Aggregate()
                .Match(x => x.Route.Multiport == multiportRoute)
                .Match(x => x.Route.Id == $"{routeAirportCode1}-{routeAirportCode2}")
                .Match(x => x.DepartureDay >= route.PeriodFrom.AddMonths(-1))
                .Match(x => x.DepartureDay < route.PeriodTo.AddMonths(2))
                .Match(x => x.ExpirationDate > now)
                .Project(x => new OneWayFlightOfferRead // additional projection stage to limit sent data size
                {
                    Id = x.Id,
                    Route = x.Route.Id,
                    IsOneWay = x.IsOneWay,
                    IsOutbound = x.IsOutbound,
                    IsInbound = x.IsInbound,
                    MinStay = x.MinStay,
                    DepartureDay = x.DepartureDay,
                    Provider = x.Provider,
                    Supplier = x.Supplier,
                    RefPrice = x.RefPrice,
                    IsIntercontinental = x.IsIntercontinental
                });
            return query.ToListAsync();
        }
    }

    private async Task<List<FlightOfferOneWay>> FetchOneWayFlightOffers(RouteToCalculate route, IEnumerable<string> owIds)
    {
        var outbound = GetFlights(route.MultiportRoute);
        var inbound = GetFlights(route.MultiportRoute.InvertRoute());

        await Task.WhenAll(outbound, inbound);

        return [..outbound.Result, ..inbound.Result];

        Task<List<FlightOfferOneWay>> GetFlights(string multiportRoute)
        {
            var now = DateTime.UtcNow;

            var query = _flightOffersOneWayCollection.AsQueryable()
                .Where(x => x.Route.Multiport == multiportRoute)
                .Where(x => owIds.Contains(x.Id))
                .Where(x => x.ExpirationDate > now);
            return query.ToListAsync();
        }
    }

    private static bool IsDirectFlight(params FlightDetails[] flights)
    {
        return flights.Any(f => f.Legs.Any(l => l.Segments.Count == 1));
    }
}