using Dasync.Collections;
using Esky.FlightsCache.Common;
using Microsoft.Extensions.Logging;
using StackExchange.Redis;
using System;
using System.Collections.Concurrent;
using System.Linq;
using System.Threading.Tasks;

namespace Esky.FlightsCache.Database.Aggregations
{
    public class RedisRouteAggregationBuffer : IRouteAggregationBuffer
    {
        private const string GroupName = "ConsumerGroup";
        private static readonly string ConsumerName = Environment.MachineName;
        private readonly BulkAggregationConfiguration _configuration;
        private readonly long _consumerProcessingTimeoutMs;
        private readonly ILogger<RedisRouteAggregationBuffer> _logger;
        private readonly int _maxElementsToProcess;
        private readonly IRedisConnection _redisConnection;
        private readonly ConcurrentDictionary<string, string> _streamKeys = new();

        public RedisRouteAggregationBuffer(
            IRedisConnection redisConnection,
            BulkAggregationConfiguration configuration,
            ILogger<RedisRouteAggregationBuffer> logger)
        {
            _redisConnection = redisConnection;
            _configuration = configuration;
            _logger = logger;
            _maxElementsToProcess = configuration.MaxElementsToProcess > 0
                ? configuration.MaxElementsToProcess
                : int.MaxValue;

            _consumerProcessingTimeoutMs = _configuration.ProcessingTimeoutInMinutes * 60_000L;
        }

        private IDatabase Database => _redisConnection.Database;

        public async Task InitConsumer(string type)
        {
            try
            {
                await Database.StreamCreateConsumerGroupAsync(BuildStreamKey(type), GroupName, 0);
            }
            catch (RedisServerException e) when (e.Message.Contains("BUSYGROUP"))
            {
                // ignore, group already exists
            }
        }

        public Task Insert(string type, RouteUpdateEvent[] values)
        {
            return values.Any()
                ? values.ToAsyncEnumerable().ForEachAsync(x => Database.StreamAddAsync(
                    BuildStreamKey(type),
                    maxLength: _configuration.MaxBufferLength,
                    useApproximateMaxLength: true,
                    streamField: "_",
                    streamValue: x.ToString(),
                    flags: CommandFlags.FireAndForget))
                : Task.CompletedTask;
        }

        public async Task<(string Id, RouteUpdateEvent Event)[]> GetValues(string type)
        {
            var streamName = BuildStreamKey(type);

            var values = await ClaimPendingValues(streamName);

            if (!values.Any())
            {
                values = await ReadNewValues(streamName);
            }

            return values.Select(val => (val.Id.ToString(), RouteUpdateEvent.Parse(val.Values[0].Value))).ToArray();
        }

        public Task RemoveValues(string type, string[] keys)
        {
            return keys.Any()
                ? Database.StreamAcknowledgeAsync(BuildStreamKey(type), GroupName,
                    Array.ConvertAll(keys, x => (RedisValue)x))
                : Task.CompletedTask;
        }

        private async Task<StreamEntry[]> ClaimPendingValues(string streamName)
        {
            var result = new StreamEntry[0];

            var pendingInfo = await Database.StreamPendingAsync(streamName, GroupName);

            if (pendingInfo.PendingMessageCount > 0)
            {
                var pendingMessageDetails = await Database.StreamPendingMessagesAsync(
                    streamName,
                    GroupName,
                    1,
                    pendingInfo.Consumers.First().Name,
                    pendingInfo.LowestPendingMessageId,
                    pendingInfo.LowestPendingMessageId);

                if (pendingMessageDetails.Any())
                {
                    var oldestPendingMessage = pendingMessageDetails.First();
                    if (oldestPendingMessage.IdleTimeInMilliseconds > _consumerProcessingTimeoutMs)
                    {
                        var pendingMessages = await Database.StreamPendingMessagesAsync(
                            streamName,
                            GroupName,
                            _maxElementsToProcess,
                            oldestPendingMessage.ConsumerName);

                        result = await Database.StreamClaimAsync(
                            streamName,
                            GroupName,
                            ConsumerName,
                            _consumerProcessingTimeoutMs,
                            pendingMessages.Select(x => x.MessageId).ToArray());

                        _logger.LogInformation(
                            "Consumer {ConsumerName} claimed {EntriesCount} entries from {OldestPendingMessageConsumerName}, because they haven't been processed for over {DurationMinutes} minutes",
                            ConsumerName,
                            result.Length,
                            oldestPendingMessage.ConsumerName,
                            (int)TimeSpan.FromMilliseconds(oldestPendingMessage.IdleTimeInMilliseconds).TotalMinutes
                        );
                    }
                }
            }

            return result;
        }

        private async Task<StreamEntry[]> ReadNewValues(string streamName)
        {
            var result =
                await Database.StreamReadGroupAsync(streamName, GroupName, ConsumerName, count: _maxElementsToProcess);

            _logger.LogInformation("Consumer {ConsumerName} retrieved {EntriesCount} entries for processing",
                ConsumerName, result.Length);

            return result;
        }

        private string BuildStreamKey(string type)
        {
            return _streamKeys.GetOrAdd(type, x => $"{_redisConnection.InstanceName}:aggregations:streams:{x}");
        }
    }
}