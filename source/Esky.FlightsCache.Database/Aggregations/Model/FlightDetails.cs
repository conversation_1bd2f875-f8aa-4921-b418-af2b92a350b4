using Esky.FlightsCache.Database.FlightOffers.Model;
using MongoDB.Bson.Serialization.Attributes;
using System.Collections.Generic;

namespace Esky.FlightsCache.Database.Aggregations.Model;

public class FlightDetails
{
    [BsonElement("FlightOfferId")] public string FlightOfferId { get; set; }
    [BsonElement("PriceElements")] public Dictionary<string, PriceElement> PriceElements { get; set; }
    [BsonElement("Currency")] public string Currency { get; set; }
    
    [BsonElement("Provider")]
    [BsonIgnoreIfDefault]
    public int Provider { get; set; }

    [BsonElement("Legs")]
    [BsonIgnoreIfDefault]
    public Leg[] Legs { get; set; }
}