using Esky.FlightsCache.Database.Routes;
using MongoDB.Bson.Serialization.Attributes;
using System;

namespace Esky.FlightsCache.Database.Aggregations.Model;

[BsonIgnoreExtraElements]
public record MonthlyOneWayFlightOffer
{
    public string Id => $"{Meta.Provider}{Meta.Supplier}{RouteDefinition.Id.Remove(3, 1)}{Meta.Month:yyMM}";
    
    [BsonElement("Meta")] public AggregationMetadata Meta { get; set; }
    [BsonElement("RefPrice")] public int RefPrice { get; set; }
    [BsonElement("Flights")] public FlightDetails[] Flights { get; set; }

    [BsonElement("Route")] public RouteDefinition RouteDefinition { get; set; }

    [BsonElement("ExpirationDate")] public DateTime ExpirationDate { get; set; }
    
    [BsonElement("ModificationDate")] public DateTime ModificationDate { get; set; }

    public class AggregationMetadata
    {
        [BsonElement("Month")]
        [BsonDateTimeOptions(DateOnly = true)]
        public DateTime Month { get; set; }

        [BsonElement("DepartureDay")]
        [BsonDateTimeOptions(DateOnly = true)]
        public DateTime DepartureDay { get; set; }

        [BsonElement("Provider")]
        public int Provider { get; set; }
        
        [BsonIgnoreIfDefault]
        [BsonElement("Supplier")]
        public string Supplier { get; set; }
    }
}