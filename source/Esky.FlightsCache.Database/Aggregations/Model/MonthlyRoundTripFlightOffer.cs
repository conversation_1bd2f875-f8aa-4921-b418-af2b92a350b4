using Esky.FlightsCache.Database.FlightOffers.Model;
using Esky.FlightsCache.Database.Routes;
using MongoDB.Bson.Serialization.Attributes;
using System;

namespace Esky.FlightsCache.Database.Aggregations.Model;

[BsonIgnoreExtraElements]
public record MonthlyRoundTripFlightOffer
{
    public string Id => $"{ProviderId}{Meta.Supplier}{RouteId}{Meta.Month:yyMM}|{Meta.LengthOfStayType:D}";

    [BsonIgnore] private string ProviderId => Flights.Length == 1 ? $"R{Meta.Provider}" : $"{Meta.Provider}";

    [BsonIgnore] private string RouteId => RouteDefinition.Id.Remove(3, 1);

    [BsonElement("Meta")] public AggregationMetadata Meta { get; set; }
    [BsonElement("RefPrice")] public int RefPrice { get; set; }
    [BsonElement("Flights")] public FlightDetails[] Flights { get; set; }

    [BsonElement("Route")] public RouteDefinition RouteDefinition { get; set; }
    
    [BsonElement("StandardScore")] public int StandardScore { get; set; }

    [BsonElement("MeanRefPrice")]
    [BsonIgnoreIfDefault]
    public int MeanRefPrice { get; set; }

    [BsonElement("CountryStandardScore")] public int? CountryStandardScore { get; set; }
    
    [BsonElement("ModificationDate")] public DateTime ModificationDate { get; set; }
    [BsonElement("ExpirationDate")] public DateTime ExpirationDate { get; set; }
    [BsonElement("RefreshDate")] public DateTime RefreshDate { get; set; }

    public class AggregationMetadata
    {
        [BsonElement("LengthOfStayType")] public LengthOfStayTypeEnum LengthOfStayType { get; set; }

        [BsonElement("Month")]
        [BsonDateTimeOptions(DateOnly = true)]
        public DateTime Month { get; set; }

        [BsonElement("DepartureDay")]
        [BsonDateTimeOptions(DateOnly = true)]
        public DateTime DepartureDay { get; set; }

        [BsonElement("ReturnDay")]
        [BsonDateTimeOptions(DateOnly = true)]
        public DateTime ReturnDay { get; set; }

        [BsonElement("Provider")] public int Provider { get; set; }
        [BsonIgnoreIfDefault]
        [BsonElement("Supplier")] public string Supplier { get; set; }

        [BsonIgnore] public string MultiportRoute { get; set; }
    }
}