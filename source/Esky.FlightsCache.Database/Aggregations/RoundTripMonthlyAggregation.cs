using Esky.FlightsCache.Common.Extensions;
using Esky.FlightsCache.Database.FlightOffers.Model;
using System;

namespace Esky.FlightsCache.Database.Aggregations
{
    internal class RoundTripMonthlyAggregation(FlightOfferRoundTrip roundTrip, LengthOfStayTypeEnum lengthOfStayType)
    {
        public FlightOfferRoundTrip RoundTrip { get; } = roundTrip;
        public (string Route, LengthOfStayTypeEnum LengthOfStayType, DateTime Month, int Provider, string Supplier) Key { get; } = (
            roundTrip.Route.Id,
            LengthOfStayType: lengthOfStayType,
            Month: roundTrip.DepartureDay.BeginningOfTheMonth(),
            roundTrip.Provider,
            roundTrip.Supplier);
    }
}