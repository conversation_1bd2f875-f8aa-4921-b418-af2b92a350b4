namespace Esky.FlightsCache.Database.Aggregations
{
    public class GeneralRouteDefinition(
        (string MultiportCode, string AirportCode) first,
        (string MultiportCode, string AirportCode) second)
    {
        public (string MultiportCode, string AirportCode) First { get; } = first;
        public (string MultiportCode, string AirportCode) Second { get; } = second;

        public string MultiportRouteString => $"{First.MultiportCode}-{Second.MultiportCode}";
        public string AirportRouteString => $"{First.AirportCode}-{Second.AirportCode}";
        public string AirportShortRouteString => $"{First.AirportCode}{Second.AirportCode}";

        public (string code1, string code2) AirportCodes => (First.AirportCode, Second.AirportCode);

        private bool Equals(GeneralRouteDefinition other)
        {
            return First.Equals(other.First) && Second.Equals(other.Second);
        }

        public override bool Equals(object obj)
        {
            if (ReferenceEquals(null, obj))
            {
                return false;
            }

            if (ReferenceEquals(this, obj))
            {
                return true;
            }

            if (obj.GetType() != GetType())
            {
                return false;
            }

            return Equals((GeneralRouteDefinition)obj);
        }

        public override int GetHashCode()
        {
            unchecked
            {
                return (First.GetHashCode() * 397) ^ Second.GetHashCode();
            }
        }
    }
}