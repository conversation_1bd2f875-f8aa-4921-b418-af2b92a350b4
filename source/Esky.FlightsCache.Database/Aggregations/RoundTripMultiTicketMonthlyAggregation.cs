using Esky.FlightsCache.Common.Extensions;
using Esky.FlightsCache.Database.FlightOffers.Model;
using System;

namespace Esky.FlightsCache.Database.Aggregations;

internal class RoundTripMultiTicketMonthlyAggregation(OneWayFlightOfferRead outbound, OneWayFlightOfferRead inbound, LengthOfStayTypeEnum lengthOfStayType, int provider = 0, string supplier = null)
{
    public OneWayFlightOfferRead Outbound { get; } = outbound;
    public OneWayFlightOfferRead Inbound { get; } = inbound;
    public int RefPrice { get; } = outbound.RefPrice + inbound.RefPrice;
    public (string Route, LengthOfStayTypeEnum LengthOfStayType, DateTime Month, int Provider, string Supplier) Key { get; } = (
        outbound.Route,
        LengthOfStayType: lengthOfStayType,
        Month: outbound.DepartureDay.BeginningOfTheMonth(),
        Provider: provider,
        Supplier: supplier);
}