using Esky.FlightsCache.Database.FlightOffers.Model;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Esky.FlightsCache.Database.Aggregations;

public class OneWayNoopMonthlyAggregationsService : IMonthlyAggregationsService
{
    private readonly IMonthlyAggregationsService _decorated;

    public OneWayNoopMonthlyAggregationsService(IMonthlyAggregationsService decorated)
    {
        _decorated = decorated;
    }

    public Task RecalculateMonthlyAggregates(IReadOnlyCollection<FlightOfferOneWay> owFlightOffers)
    {
        return Task.CompletedTask;
    }

    public Task RecalculateMonthlyAggregates(IReadOnlyCollection<FlightOfferRoundTrip> rtFlightOffers)
    {
        return _decorated.RecalculateMonthlyAggregates(rtFlightOffers);
    }
}