using Esky.FlightsCache.Common;
using Microsoft.Extensions.Options;
using StackExchange.Redis;
using System;

namespace Esky.FlightsCache.Database.Aggregations
{
    public class RedisConnection : IRedisConnection
    {
        private readonly Lazy<IConnectionMultiplexer> _connectionMultiplexer;

        public RedisConnection(IOptions<RedisCachingConfiguration> redisConfiguration)
        {
            var config = redisConfiguration.Value;
            _connectionMultiplexer =
                new Lazy<IConnectionMultiplexer>(() => ConnectionMultiplexer.Connect(config.ConnectionString));
            InstanceName = config.InstanceName;
        }

        public IDatabase Database => _connectionMultiplexer.Value.GetDatabase();

        public string InstanceName { get; }
    }
}