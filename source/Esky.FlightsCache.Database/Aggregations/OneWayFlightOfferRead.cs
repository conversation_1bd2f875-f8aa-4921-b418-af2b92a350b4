using System;

namespace Esky.FlightsCache.Database.Aggregations
{
    internal class OneWayFlightOfferRead
    {
        public string Id { get; set; }
        public string Route { get; set; }
        public bool IsOneWay { get; set; }
        public bool IsOutbound { get; set; }
        public bool IsInbound { get; set; }
        public DateTime DepartureDay { get; set; }
        public int Provider { get; set; }
        public string Supplier { get; set; }
        public int RefPrice { get; set; }
        public bool IsIntercontinental { get; set; }
        public int MinStay { get; set; }
    }
}