using MongoDB.Bson.Serialization.Attributes;
using System;
using System.Collections.Generic;

namespace Esky.FlightsCache.Database.Timetables
{
    public class TimetableItemKey
    {
        private DateTime _departureDate;

        [BsonElement("R")] public string Route { get; init; }

        [BsonElement("DD")]
        [BsonDateTimeOptions(DateOnly = true)]
        public DateTime DepartureDate
        {
            get => _departureDate;
            set => _departureDate = value.Date;
        }

        protected internal virtual string DebuggerDisplay => $"{Route}@{DepartureDate:yyMMdd}";

        public override bool Equals(object obj)
        {
            return obj is TimetableItemKey key &&
                   Route == key.Route &&
                   DepartureDate == key.DepartureDate;
        }

        public override int GetHashCode()
        {
            var hashCode = -1437881995;
            hashCode = (hashCode * -1521134295) + EqualityComparer<string>.Default.GetHashCode(Route);
            hashCode = (hashCode * -1521134295) + DepartureDate.GetHashCode();
            return hashCode;
        }

        public static TimetableItemKey Create(string departureCode, string arrivalCode, DateTime departureDate)
        {
            return new TimetableItemKey() { Route = $"{departureCode}-{arrivalCode}", DepartureDate = departureDate };
        }
    }
}