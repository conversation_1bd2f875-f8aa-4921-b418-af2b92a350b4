using MongoDB.Driver;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Esky.FlightsCache.Database.Timetables;

public interface ITimetableRepository
{
    Task UpsertTimetableItemsAsync(IReadOnlyList<TimetableItem> items);
}

public sealed class TravelFusionTimetableRepository(DatabaseContext databaseContext) : ITimetableRepository
{
    public Task UpsertTimetableItemsAsync(IReadOnlyList<TimetableItem> items)
    {
        if (!items.Any())
        {
            return Task.CompletedTask;
        }

        var bulkOps = items.Select(document =>
            new ReplaceOneModel<TimetableItem>(
                Builders<TimetableItem>.Filter.Where(x => x.Id.Equals(document.Id)),
                document) { IsUpsert = true });

        return databaseContext.TimetableCollection.BulkWriteAsync(bulkOps, new BulkWriteOptions { IsOrdered = false });
    }
}