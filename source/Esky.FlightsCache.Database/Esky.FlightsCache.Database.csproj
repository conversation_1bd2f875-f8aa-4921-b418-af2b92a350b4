<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="AsyncEnumerator" Version="4.0.2" />
        <PackageReference Include="Dapper" Version="2.0.123" />
        <PackageReference Include="Microsoft.CSharp" Version="4.7.0" />
        <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="8.0.0" />
        <PackageReference Include="Microsoft.Extensions.Options" Version="8.0.0" />
        <PackageReference Include="MongoDB.Driver" Version="2.23.1" />
        <PackageReference Include="morelinq" Version="3.3.2" />
        <PackageReference Include="Newtonsoft.Json" Version="13.0.2" />
        <PackageReference Include="StackExchange.Redis" Version="2.6.86" />
        <PackageReference Include="System.Data.DataSetExtensions" Version="4.5.0" />
        <PackageReference Include="System.Data.SqlClient" Version="4.8.5" />
        <PackageReference Include="System.ComponentModel.Annotations" Version="5.0.0" />
        <PackageReference Include="Microsoft.Bcl.AsyncInterfaces" Version="8.0.0" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\Esky.FlightsCache.Common\Esky.FlightsCache.Common.csproj" />
        <ProjectReference Include="..\Esky.FlightsCache.MessageContract\Esky.FlightsCache.MessageContract.csproj" />
        <ProjectReference Include="..\Esky.FlightsCache.ProviderMapping\Esky.FlightsCache.ProviderMapping.csproj" />
        <ProjectReference Include="..\Esky.FlightsCache.ResultFilters\Esky.FlightsCache.ResultFilters.csproj" />
    </ItemGroup>

    <ItemGroup>
        <AssemblyAttribute Include="System.Runtime.CompilerServices.InternalsVisibleToAttribute">
            <_Parameter1>Esky.FlightsCache.Processing.Tests</_Parameter1>
        </AssemblyAttribute>
    </ItemGroup>
</Project>
