using Esky.FlightsCache.ResultFilters.AirportCode;
using MongoDB.Driver;
using MongoDB.Driver.Linq;
using System.Linq;
using System.Threading.Tasks;

namespace Esky.FlightsCache.Database.Airports;

public class AirportsRepository(DatabaseContext dbContext) : IAirportsRepository
{
    public async Task<AirportCollection> GetAirportsAsync()
    {
        var data = await dbContext.AirportsCollection
            .AsQueryable()
            .Select(e => new
            {
                e.Id,
                e.Multiport,
                e.NearbyAirports,
                e.CityCode,
                e.CountryCode,
                e.ContinentCode,
                e.TimeZoneId
            })
            .ToListAsync();

        var airports = data
            .Select(e =>
                new AirportCollection.Airport
                {
                    Code = e.Id,
                    Multiport = e.Multiport,
                    NearbyAirports = e.NearbyAirports,
                    CityCode = e.CityCode,
                    CountryCode = e.CountryCode,
                    ContinentCode = e.ContinentCode,
                    TimeZoneId = e.TimeZoneId
                }
            );

        return new AirportCollection(airports);
    }
}