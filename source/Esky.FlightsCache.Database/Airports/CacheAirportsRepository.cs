using Esky.FlightsCache.ResultFilters.AirportCode;
using Microsoft.Extensions.Caching.Memory;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace Esky.FlightsCache.Database.Airports;

public class CacheAirportsRepository(IAirportsRepository inner, IMemoryCache cache) : IAirportsRepository
{
    private static readonly string _cacheKey = $"Airports_{Guid.NewGuid()}";
    private const int _cacheDurationInMinutes = 60;

    private readonly SemaphoreSlim _semaphoreSlim = new(1, 1);

    public async Task<AirportCollection> GetAirportsAsync()
    {
        if (cache.TryGetValue(_cacheKey, out AirportCollection airports))
        {
            return airports;
        }

        await _semaphoreSlim.WaitAsync();

        try
        {
            if (!cache.TryGetValue(_cacheKey, out airports))
            {
                airports = await inner.GetAirportsAsync();

                cache.Set(_cacheKey, airports, TimeSpan.FromMinutes(_cacheDurationInMinutes));
            }
        }
        finally
        {
            _semaphoreSlim.Release();
        }

        return airports;
    }
}