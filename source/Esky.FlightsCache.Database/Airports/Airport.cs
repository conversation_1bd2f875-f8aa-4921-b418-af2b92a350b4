using MongoDB.Bson.Serialization.Attributes;
using System.Collections.Generic;

namespace Esky.FlightsCache.Database.Airports
{
    [BsonIgnoreExtraElements]
    public class Airport
    {
        public string Id { get; set; }

        [BsonElement("multiport")] public string Multiport { get; set; }
        
        [BsonElement("timeZoneId")] public string TimeZoneId { get; set; }

        [BsonElement("codes")] public List<string> Codes { get; set; }

        [BsonElement("cityCode")] public string CityCode { get; set; }

        [BsonElement("countryCode")] public string CountryCode { get; set; }

        [BsonElement("continentCode")] public string ContinentCode { get; set; }

        [BsonElement("nearbyAirports")] public List<string> NearbyAirports { get; set; }
    }
}