using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using System.Diagnostics;

namespace Esky.FlightsCache.Database.Routes
{
    [DebuggerDisplay("{Id,nq}")]
    public class RouteDefinition
    {
        public string Id { get; set; }

        [BsonElement("statisticalMeasures")]
        [BsonIgnoreIfDefault]
        public StatisticalMeasure StatisticalMeasures { get; set; }

        [BsonElement("countryStatisticalMeasures")]
        [BsonIgnoreIfDefault]
        public StatisticalMeasure CountryStatisticalMeasures { get; set; }

        [BsonElement("isSmartOfferRoute")]
        [BsonIgnoreIfDefault]
        public bool IsSmartOfferRoute { get; set; }

        [BsonElement("wasSmartOfferRoute")]
        [BsonIgnoreIfDefault]
        public bool WasSmartOfferRoute { get; set; }

        [BsonExtraElements] public BsonDocument ExtraElements { get; set; }

        [BsonIgnoreExtraElements]
        public class StatisticalMeasure
        {
            [BsonElement("meanRefPrice")] public decimal MeanRefPrice { get; set; }

            [BsonElement("stdDevRefPrice")] public decimal StdDevRefPrice { get; set; }
        }
    }
}