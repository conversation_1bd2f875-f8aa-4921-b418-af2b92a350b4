<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFrameworks>net7.0;net8.0</TargetFrameworks>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <GeneratePackageOnBuild>true</GeneratePackageOnBuild>
        <Version>2.0.0-rc.1</Version>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Microsoft.AspNetCore.Http.Abstractions" Version="2.3.0"/>
        <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="9.0.2" />
        <PackageReference Include="Microsoft.Extensions.Hosting.Abstractions" Version="9.0.2"/>
        <PackageReference Include="Microsoft.Extensions.Http" Version="9.0.2"/>
        <PackageReference Include="OpenTelemetry" Version="1.11.1" />
        <PackageReference Include="OpenTelemetry.Exporter.OpenTelemetryProtocol" Version="1.11.1" />
        <PackageReference Include="OpenTelemetry.Exporter.Prometheus.AspNetCore" Version="1.7.0-rc.1" />
        <PackageReference Include="OpenTelemetry.Extensions.Hosting" Version="1.11.1" />
        <PackageReference Include="OpenTelemetry.Instrumentation.Hangfire" Version="1.6.0-beta.1" />
        <PackageReference Include="OpenTelemetry.Instrumentation.Http" Version="1.11.0" />
        <PackageReference Include="OpenTelemetry.Instrumentation.Runtime" Version="1.11.0"/>
        <PackageReference Include="OpenTelemetry.Shims.OpenTracing" Version="1.7.0-beta.1" />
    </ItemGroup>

</Project>