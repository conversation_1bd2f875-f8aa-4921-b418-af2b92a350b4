using OpenTelemetry;
using System.Diagnostics;

namespace Esky.FlightsCache.OpenTelemetry
{
    public sealed class TimeBasedFilterProcessor : BaseProcessor<Activity>
    {
        private readonly TimeSpan _thresholdMs;

        public TimeBasedFilterProcessor(TimeSpan thresholdMs)
        {
            _thresholdMs = thresholdMs;
        }

        public override void OnEnd(Activity data)
        {
            if (data.Duration < _thresholdMs)
            {
                data.ActivityTraceFlags &= ~ActivityTraceFlags.Recorded;
            }
        }
    }
}