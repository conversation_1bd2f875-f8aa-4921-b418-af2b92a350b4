namespace Esky.FlightsCache.OpenTelemetry
{
    public class OpenTelemetryOptions
    {
        /// <summary>
        ///     OTEL_EXPORTER_JAEGER_AGENT_HOST env variable
        /// </summary>
        public static readonly string OtelExporterEnvVar = "OTEL_EXPORTER_JAEGER_AGENT_HOST";

        /// <summary>
        ///     Value of <see cref="OtelExporterEnvVar" />
        /// </summary>
        public static string? OtelExporterHost => Environment.GetEnvironmentVariable(OtelExporterEnvVar);

        public static readonly string OtelExporterEndpointEnvVar = "OTEL_EXPORTER_OTLP_ENDPOINT";
        public static string? OtelExporterEndpoint => Environment.GetEnvironmentVariable(OtelExporterEndpointEnvVar);

        /// <summary>
        ///     Uri backwards compatible with Jaeger Otel Exporter env variables
        /// </summary>
        public static Uri OtelExporterUri => new(OtelExporterEndpoint ?? $"http://{OtelExporterHost ?? "localhost"}:4317");

        /// <summary>
        ///     Cannot be null
        /// </summary>
        public required string ServiceName { get; set; }

        /// <summary>
        ///     Comma separated metric names
        /// </summary>
        public string Metrics { get; set; } = string.Empty;

        public string[] EnabledMetrics =>
            Metrics.Split(',', StringSplitOptions.TrimEntries | StringSplitOptions.RemoveEmptyEntries);

        /// <summary>
        ///     Comma separated trace names
        /// </summary>
        public string Traces { get; set; } = string.Empty;

        public string[] EnabledTraces =>
            Traces.Split(',', StringSplitOptions.TrimEntries | StringSplitOptions.RemoveEmptyEntries);

        public JaegerSettings? Jaeger { get; set; }
        public TimeBasedProcessorSettings? TimeBasedProcessor { get; set; }

        public class JaegerSettings
        {
            /// <summary>
            ///     Default value <see cref="OtelExporterHost" /> ?? localhost
            /// </summary>
            public string? AgentHost { get; set; } = OtelExporterHost ?? "localhost";

            /// <summary>
            ///     Default value 5778
            /// </summary>
            public int Port { get; set; } = 5778;

            public Uri Uri => new($"http://{AgentHost}:{Port}/sampling");

            /// <summary>
            ///     Default value 5 minutes
            /// </summary>
            public int SettingsPollIntervalMinutes { get; set; } = 5;
        }

        public class TimeBasedProcessorSettings
        {
            public int ThresholdMs { get; set; }
        }
    }
}