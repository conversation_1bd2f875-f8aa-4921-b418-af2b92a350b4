namespace Esky.FlightsCache.OpenTelemetry
{
    public class RemoteSamplingManagerOptions
    {
        public required string ServiceName { get; set; }

        /// <summary>
        ///     Default value $"http://{<see cref="OpenTelemetryOptions.OtelExporterHost" /> ?? "localhost"}:5778/sampling"
        /// </summary>
        public Uri Uri { get; set; } =
            new($"http://{OpenTelemetryOptions.OtelExporterHost ?? "localhost"}:5778/sampling");

        /// <summary>
        ///     Default value 5 minutes
        /// </summary>
        public TimeSpan PollInterval { get; set; } = TimeSpan.FromMinutes(5);
    }
}