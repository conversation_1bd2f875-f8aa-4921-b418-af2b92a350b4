namespace Esky.FlightsCache.OpenTelemetry
{
    internal class SamplingStrategyResponse
    {
        public SamplingStrategyResponse(
            ProbabilisticSamplingStrategy probabilisticSampling,
            RateLimitingSamplingStrategy rateLimitingSampling,
            PerOperationSamplingStrategies operationSampling)
        {
            ProbabilisticSampling = probabilisticSampling;
            RateLimitingSampling = rateLimitingSampling;
            OperationSampling = operationSampling;
        }

        public ProbabilisticSamplingStrategy? ProbabilisticSampling { get; }
        public RateLimitingSamplingStrategy? RateLimitingSampling { get; }
        public PerOperationSamplingStrategies? OperationSampling { get; }

        public ISamplingStrategy? GetStrategy()
        {
            return EnumerateStrategies().FirstOrDefault(s => s != null);
        }

        private IEnumerable<ISamplingStrategy?> EnumerateStrategies()
        {
            yield return OperationSampling;
            yield return RateLimitingSampling;
            yield return ProbabilisticSampling;
        }
    }
}