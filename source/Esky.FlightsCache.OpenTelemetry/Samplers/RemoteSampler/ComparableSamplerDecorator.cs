using OpenTelemetry.Trace;

namespace Esky.FlightsCache.OpenTelemetry
{
    internal class ComparableSamplerDecorator : <PERSON><PERSON>
    {
        private readonly Sampler _sampler;
        private readonly ISamplingStrategy _strategy;

        public ComparableSamplerDecorator(ISamplingStrategy strategy)
        {
            _strategy = strategy;
            _sampler = strategy.CreateSampler();
        }

        public override SamplingResult ShouldSample(in SamplingParameters samplingParameters)
        {
            return _sampler.ShouldSample(samplingParameters);
        }

        public bool IsEqualTo(ISamplingStrategy strategy)
        {
            return _strategy.Equals(strategy);
        }
    }
}