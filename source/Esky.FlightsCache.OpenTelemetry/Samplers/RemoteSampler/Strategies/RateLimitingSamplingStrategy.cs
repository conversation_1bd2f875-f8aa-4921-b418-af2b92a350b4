using OpenTelemetry.Trace;

namespace Esky.FlightsCache.OpenTelemetry
{
    public record RateLimitingSamplingStrategy(double MaxTracesPerSecond) : ISamplingStrategy
    {
        public Sampler CreateSampler(Sampler? previous = null)
        {
            return new RateLimitingSampler(MaxTracesPerSecond);
        }

        public bool Equals(ISamplingStrategy? other)
        {
            return other is RateLimitingSamplingStrategy s && s.MaxTracesPerSecond.Equals(MaxTracesPerSecond);
        }
    }
}