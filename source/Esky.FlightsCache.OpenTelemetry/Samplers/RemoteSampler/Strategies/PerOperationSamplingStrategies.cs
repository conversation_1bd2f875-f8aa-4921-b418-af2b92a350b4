using OpenTelemetry.Trace;

namespace Esky.FlightsCache.OpenTelemetry
{
    public record PerOperationSamplingStrategies : ISamplingStrategy
    {
        public PerOperationSamplingStrategies(
            double defaultSamplingProbability,
            double defaultLowerBoundTracesPerSecond,
            PerOperationSamplingStrategyList perOperationStrategies)
        {
            DefaultSamplingProbability = defaultSamplingProbability;
            DefaultLowerBoundTracesPerSecond = defaultLowerBoundTracesPerSecond;
            PerOperationStrategies = perOperationStrategies;

            perOperationStrategies.SetDefaultLowerBoundTracesPerSecond(defaultLowerBoundTracesPerSecond);
        }

        public double DefaultSamplingProbability { get; }
        public double DefaultLowerBoundTracesPerSecond { get; }
        public PerOperationSamplingStrategyList PerOperationStrategies { get; }

        public virtual bool Equals(ISamplingStrategy? other)
        {
            return other is PerOperationSamplingStrategies s
                   && s.DefaultSamplingProbability.Equals(DefaultSamplingProbability)
                   && s.DefaultLowerBoundTracesPerSecond.Equals(DefaultLowerBoundTracesPerSecond)
                   && s.PerOperationStrategies.Equals(PerOperationStrategies);
        }

        public Sampler CreateSampler(Sampler? previous = null)
        {
            if (previous is PerOperationSampler p)
            {
                p.Update(this);
                return p;
            }

            return PerOperationSampler.Create(this);
        }
    }
}