using OpenTelemetry.Trace;

namespace Esky.FlightsCache.OpenTelemetry
{
    public record PerOperationSamplingStrategy
        (string Operation, ProbabilisticSamplingStrategy ProbabilisticSampling) : ISamplingStrategy
    {
        public double DefaultLowerBoundTracesPerSecond { get; set; }

        public virtual bool Equals(ISamplingStrategy? other)
        {
            return other is PerOperationSamplingStrategy s
                   && s.Operation == Operation
                   && s.ProbabilisticSampling.Equals(ProbabilisticSampling);
        }

        public Sampler CreateSampler(Sampler? previous = null)
        {
            return new LowerBoundSampler(ProbabilisticSampling, DefaultLowerBoundTracesPerSecond);
        }
    }
}