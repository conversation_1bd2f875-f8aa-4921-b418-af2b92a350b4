using OpenTelemetry.Trace;

namespace Esky.FlightsCache.OpenTelemetry
{
    public record ProbabilisticSamplingStrategy(double SamplingRate) : ISamplingStrategy
    {
        public Sampler CreateSampler(Sampler? previous = null)
        {
            return new TraceIdRatioBasedSampler(SamplingRate);
        }

        public bool Equals(ISamplingStrategy? other)
        {
            return other is ProbabilisticSamplingStrategy s && s.SamplingRate.Equals(SamplingRate);
        }
    }
}