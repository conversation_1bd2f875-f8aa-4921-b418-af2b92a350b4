using System.Collections.ObjectModel;

namespace Esky.FlightsCache.OpenTelemetry
{
    public class PerOperationSamplingStrategyList : Collection<PerOperationSamplingStrategy>,
        IEquatable<PerOperationSamplingStrategyList>
    {
        public bool Equals(PerOperationSamplingStrategyList? other)
        {
            if (other is null)
            {
                return false;
            }

            return Count == other.Count && this.Zip(other).All(e => e.First.Equals(e.Second));
        }

        public void SetDefaultLowerBoundTracesPerSecond(double maxPerSecond)
        {
            foreach (var strategy in this)
            {
                strategy.DefaultLowerBoundTracesPerSecond = maxPerSecond;
            }
        }

        public override string ToString()
        {
            return string.Join(';', this.Select(s => s.ToString()));
        }
    }
}