using Microsoft.Extensions.Logging;
using OpenTelemetry.Trace;
using System.Net.Http.Json;

namespace Esky.FlightsCache.OpenTelemetry
{
    public sealed class RemoteControlledSampler : Sampler, IDisposable
    {
        private readonly CancellationTokenSource _cts = new();
        private readonly Sampler _fallbackSampler;
        private readonly HttpClient _httpClient;
        private readonly ILogger<RemoteControlledSampler> _logger;
        private readonly Timer _timer;
        private readonly Uri _uri;
        private ISamplingStrategy? _currentStrategy;
        private Sampler _sampler;

        public RemoteControlledSampler(
            ILogger<RemoteControlledSampler> logger,
            IHttpClientFactory factory,
            RemoteSamplingManagerOptions options)
            : this(logger, factory, options, new TraceIdRatioBasedSampler(0))
        {
        }

        public RemoteControlledSampler(
            ILogger<RemoteControlledSampler> logger,
            IHttpClientFactory factory,
            RemoteSamplingManagerOptions options,
            Sampler fallbackSampler)
        {
            _httpClient = factory.CreateClient();
            _logger = logger;
            _sampler = fallbackSampler;
            _fallbackSampler = fallbackSampler;
            _uri = new Uri(options.Uri, $"?service={Uri.EscapeDataString(options.ServiceName)}");

            _timer = new Timer(Update, null, TimeSpan.Zero, options.PollInterval);
        }

        public void Dispose()
        {
            _timer.Dispose();
            _cts.Cancel();
        }

        public override SamplingResult ShouldSample(in SamplingParameters samplingParameters)
        {
            return _sampler.ShouldSample(samplingParameters);
        }

        private async void Update(object? _)
        {
            try
            {
                var data = await _httpClient.GetFromJsonAsync<SamplingStrategyResponse>(_uri, _cts.Token);

                var samplingStrategy = data?.GetStrategy();

                if (_currentStrategy?.Equals(samplingStrategy) == true)
                {
                    return;
                }

                _logger.LogInformation("Sampling strategy response changed: {@data}", data);

                _sampler = samplingStrategy?.CreateSampler(_sampler) ?? _fallbackSampler;
                _currentStrategy = samplingStrategy;
            }
            catch (HttpRequestException ex)
            {
                _logger.LogError(ex, "Failed to load sampling strategy");
            }
        }
    }
}