using OpenTelemetry.Trace;
using System.Collections.Concurrent;

namespace Esky.FlightsCache.OpenTelemetry
{
    public class PerOperationSampler : Sampler
    {
        private readonly ConcurrentDictionary<string, ComparableSamplerDecorator> _samplers = new();
        private Sampler _defaultSampler;
        private double _defaultSamplingProbability;

        private PerOperationSampler(double defaultSamplingProbability)
        {
            _defaultSamplingProbability = defaultSamplingProbability;
            _defaultSampler = new TraceIdRatioBasedSampler(defaultSamplingProbability);
        }

        public static PerOperationSampler Create(PerOperationSamplingStrategies data)
        {
            var sampler = new PerOperationSampler(data.DefaultSamplingProbability);
            sampler.Update(data);
            return sampler;
        }

        public override SamplingResult ShouldSample(in SamplingParameters samplingParameters)
        {
            _samplers.TryGetValue(samplingParameters.Name, out var sampler);
            return (sampler ?? _defaultSampler).ShouldSample(samplingParameters);
        }

        public void Update(PerOperationSamplingStrategies data)
        {
            if (!_defaultSamplingProbability.Equals(data.DefaultSamplingProbability))
            {
                _defaultSamplingProbability = data.DefaultSamplingProbability;
                _defaultSampler = new TraceIdRatioBasedSampler(data.DefaultSamplingProbability);
            }

            var activeOperations = new HashSet<string>(data.PerOperationStrategies.Count);

            foreach (var strategy in data.PerOperationStrategies)
            {
                _samplers.AddOrUpdate(
                    strategy.Operation,
                    new ComparableSamplerDecorator(strategy),
                    (_, sampler) =>
                        sampler.IsEqualTo(strategy)
                            ? sampler
                            : new ComparableSamplerDecorator(strategy)
                );

                activeOperations.Add(strategy.Operation);
            }

            foreach (var (operation, _) in _samplers)
            {
                if (!activeOperations.Contains(operation))
                {
                    _samplers.TryRemove(operation, out _);
                }
            }
        }
    }
}