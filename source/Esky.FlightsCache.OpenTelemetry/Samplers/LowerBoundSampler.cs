using OpenTelemetry.Trace;

namespace Esky.FlightsCache.OpenTelemetry
{
    public class LowerBoundSampler : Sampler
    {
        private ISamplingStrategy? _currentStrategy;
        private RateLimitingSampler? _lowerBoundSampler;
        private Sampler? _mainSampler;

        private double _maxTracesPerSecond;

        public LowerBoundSampler(ISamplingStrategy strategy, double lowerBound)
        {
            if (strategy == null)
            {
                throw new ArgumentNullException(nameof(strategy));
            }

            Update(strategy, lowerBound);
        }

        public override SamplingResult ShouldSample(in SamplingParameters samplingParameters)
        {
            var mainSamplingResult = _mainSampler?.ShouldSample(samplingParameters) ??
                                     new SamplingResult(SamplingDecision.Drop);
            var lowerBoundSamplingResult = _lowerBoundSampler?.ShouldSample(samplingParameters) ??
                                           new SamplingResult(SamplingDecision.Drop);

            return mainSamplingResult.Decision != SamplingDecision.Drop
                ? mainSamplingResult
                : lowerBoundSamplingResult;
        }

        public void Update(ISamplingStrategy strategy, double lowerBound)
        {
            if (_currentStrategy == null || !_currentStrategy.Equals(strategy))
            {
                _currentStrategy = strategy;
                _mainSampler = strategy.CreateSampler(_mainSampler);
            }

            if (_lowerBoundSampler == null || !_maxTracesPerSecond.Equals(lowerBound))
            {
                _maxTracesPerSecond = lowerBound;
                _lowerBoundSampler = new RateLimitingSampler(lowerBound);
            }
        }
    }
}