using OpenTelemetry.Trace;

namespace Esky.FlightsCache.OpenTelemetry
{
    public sealed class RateLimitingSampler : Sampler
    {
        private readonly RateLimiter _rateLimiter;

        public RateLimitingSampler(double maxTracesPerSecond)
        {
            _rateLimiter = new RateLimiter(maxTrace<PERSON><PERSON>er<PERSON><PERSON><PERSON>, Math.Max(maxTracesPerSecond, 1.0));
        }

        public override SamplingResult ShouldSample(in SamplingParameters samplingParameters)
        {
            return new SamplingResult(_rateLimiter.CanSample(1.0));
        }
    }

    public class RateLimiter
    {
        private readonly double _creditsPerMillisecond;
        private readonly double _maxBalance;

        private double _balance;
        private long _lastTick;

        public RateLimiter(double creditsPerSecond, double maxBalance)
        {
            _creditsPerMillisecond = creditsPerSecond / 1000;
            _maxBalance = maxBalance;

            _balance = maxBalance;
            _lastTick = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
        }

        public bool CanSample(double itemCost)
        {
            var currentTime = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
            var elapsedTime = currentTime - _lastTick;

            _lastTick = currentTime;
            _balance += elapsedTime * _creditsPerMillisecond;

            if (_maxBalance < _balance)
            {
                _balance = _maxBalance;
            }

            if (itemCost <= _balance)
            {
                _balance -= itemCost;
                return true;
            }

            return false;
        }
    }
}