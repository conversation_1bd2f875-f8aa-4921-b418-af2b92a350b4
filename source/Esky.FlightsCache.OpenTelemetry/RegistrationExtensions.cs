using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using Microsoft.Extensions.Logging;
using OpenTelemetry.Context.Propagation;
using OpenTelemetry.Instrumentation.Http;
using OpenTelemetry.Metrics;
using OpenTelemetry.Resources;
using OpenTelemetry.Shims.OpenTracing;
using OpenTelemetry.Trace;
using OpenTracing;
using OpenTracing.Util;

namespace Esky.FlightsCache.OpenTelemetry
{
    public static class RegistrationExtensions
    {
        private static IServiceCollection? _services;
        private static OpenTelemetryOptions? _options;

        /// <summary>
        ///     Registers OpenTelemetry defaults based on <see cref="OpenTelemetryOptions" />
        /// </summary>
        /// <param name="services">
        ///     <see cref="IServiceCollection" />
        /// </param>
        /// <param name="options">
        ///     <see cref="OpenTelemetryOptions" />
        /// </param>
        /// <param name="resourceConfigurator">Resource configuration</param>
        /// <param name="metricsConfigurator">Metrics configuration before default exporter</param>
        /// <param name="tracesConfigurator">Traces configuration before default exporter</param>
        /// <param name="httpConfigurator"></param>
        /// <param name="hangfireConfigurator"></param>
        /// <returns>
        ///     <see cref="IServiceCollection" />
        /// </returns>
        public static IServiceCollection ConfigureOpenTelemetry(
            this IServiceCollection services,
            OpenTelemetryOptions options,
            Action<ResourceBuilder>? resourceConfigurator = null,
            Action<MeterProviderBuilder>? metricsConfigurator = null,
            Action<TracerProviderBuilder>? tracesConfigurator = null,
            Action<HttpClientTraceInstrumentationOptions>? httpConfigurator = null,
            Action<HangfireInstrumentationOptions>? hangfireConfigurator = null)
        {
            if (options is null)
            {
                throw new ArgumentNullException(nameof(options), "Options cannot be null");
            }

            if (options.ServiceName is null)
            {
                throw new ArgumentNullException(nameof(options.ServiceName), "ServiceName cannot be null");
            }

            _services = services;
            _options = options;

            services
                .AddOpenTelemetry()
                .ConfigureResource(builder => builder
                    .AddService(
                        options.ServiceName,
                        serviceInstanceId: Environment.MachineName
                    )
                    .AddTelemetrySdk()
                    .AddEnvironmentVariableDetector()
                    .Run(resourceConfigurator)
                )
                .WithMetrics(builder => builder
                    .AddRuntimeInstrumentation()
                    .AddMeter(options.EnabledMetrics)
                    .Run(metricsConfigurator)
                    .AddPrometheusExporter()
                )
                .WithTracing(builder => builder
                    .AddSource(options.EnabledTraces)
                    .SetSampler(new ParentBasedSampler(new AlwaysOffSampler()))
                    .AddTimeBasedProcessor(options.TimeBasedProcessor)
                    .AddHttpClientInstrumentation(e => e.Run(httpConfigurator))
                    .AddHangfireInstrumentation(e => e.Run(hangfireConfigurator))
                    .Run(tracesConfigurator)
                    .AddOtlpExporter(opt => opt.Endpoint = OpenTelemetryOptions.OtelExporterUri)
                );

            return services;
        }

        /// <summary>
        ///     Conditionally adds <see cref="RemoteControlledSampler" />
        /// </summary>
        /// <remarks>
        ///     Requires call to <see cref="ConfigureOpenTelemetry" />
        ///     <br />
        ///     If <see cref="OpenTelemetryOptions.JaegerSettings.AgentHost" /> is null or
        ///     <see cref="OpenTelemetryOptions.JaegerSettings.SettingsPollIntervalMinutes" /> == 0 then no services added
        /// </remarks>
        /// <param name="builder">
        ///     <see cref="TracerProviderBuilder" />
        /// </param>
        /// <returns>The same builder for method chaining</returns>
        public static TracerProviderBuilder SetRemoteControlledSampler(this TracerProviderBuilder builder)
        {
            CheckInitialized();

            return builder.SetRemoteControlledSampler(_services!, _options!);
        }

        /// <summary>
        ///     Conditionally adds <see cref="RemoteControlledSampler" />
        /// </summary>
        /// <param name="builder">
        ///     <see cref="TracerProviderBuilder" />
        /// </param>
        /// <param name="services">
        ///     <see cref="IServiceCollection" />
        /// </param>
        /// <param name="options">
        ///     If <see cref="OpenTelemetryOptions.JaegerSettings.AgentHost" /> is null or
        ///     <see cref="OpenTelemetryOptions.JaegerSettings.SettingsPollIntervalMinutes" /> == 0 then no services added
        /// </param>
        /// <returns>The same builder for method chaining</returns>
        public static TracerProviderBuilder SetRemoteControlledSampler(this TracerProviderBuilder builder,
            IServiceCollection services, OpenTelemetryOptions options)
        {
            if (options.Jaeger?.AgentHost is null || options.Jaeger.SettingsPollIntervalMinutes == 0)
            {
                return builder;
            }

            services
                .AddLogging()
                .AddHttpClient()
                .TryAddSingleton(sp =>
                    new RemoteControlledSampler(
                        sp.GetRequiredService<ILogger<RemoteControlledSampler>>(),
                        sp.GetRequiredService<IHttpClientFactory>(),
                        new RemoteSamplingManagerOptions
                        {
                            Uri = options.Jaeger.Uri,
                            PollInterval = TimeSpan.FromMinutes(options.Jaeger.SettingsPollIntervalMinutes),
                            ServiceName = options.ServiceName
                        }
                    )
                );

            builder.SetSampler<RemoteControlledSampler>();

            return builder;
        }

        /// <summary>
        ///     Conditionally adds <see cref="TimeBasedFilterProcessor" />
        /// </summary>
        /// <param name="builder">
        ///     <see cref="TracerProviderBuilder" />
        /// </param>
        /// <param name="options">
        ///     If null or <see cref="OpenTelemetryOptions.TimeBasedProcessorSettings.ThresholdMs" /> == 0 then
        ///     no processor added
        /// </param>
        /// <returns>The same builder for method chaining</returns>
        public static TracerProviderBuilder AddTimeBasedProcessor(this TracerProviderBuilder builder,
            OpenTelemetryOptions.TimeBasedProcessorSettings? options)
        {
            return options is null || options.ThresholdMs == 0
                ? builder
                : builder.AddProcessor(new TimeBasedFilterProcessor(TimeSpan.FromMilliseconds(options.ThresholdMs)));
        }

        /// <summary>
        ///     Register default middlewares
        /// </summary>
        /// <param name="app">
        ///     <see cref="IApplicationBuilder" />
        /// </param>
        /// <returns>The same builder for method chaining</returns>
        public static IApplicationBuilder UseOpenTelemetryDefaults(this IApplicationBuilder app)
        {
            return app.UseOpenTelemetryPrometheusScrapingEndpoint();
        }


        /// <summary>
        ///     Replaces ITracer implementation for backward compability with OpenTracing
        /// </summary>
        /// <param name="services">
        ///     <see cref="IServiceCollection" />
        /// </param>
        /// <returns>The same builder for method chaining</returns>
        /// <remarks>Requires call to <see cref="ConfigureOpenTelemetry" /></remarks>
        public static IServiceCollection RegisterOpenTracingShim(this IServiceCollection services)
        {
            CheckInitialized();

            services.Replace(
                new ServiceDescriptor(
                    typeof(ITracer),
                    sp =>
                    {
                        var tracerProvider = sp.GetRequiredService<TracerProvider>();
                        // ArgumentExceptionCatchBuilderTracer is a workaround until issue with shim is resolved
                        // TODO: check for new nuget versions and remove ArgumentExceptionCatchBuilderTracer wrapper
                        var wrappedShim = new ArgumentExceptionCatchBuilderTracer(new TracerShim(tracerProvider, Propagators.DefaultTextMapPropagator));
                        GlobalTracer.RegisterIfAbsent(wrappedShim);
                        return wrappedShim;
                    },
                    ServiceLifetime.Singleton
                )
            );

            return services;
        }

        private static void CheckInitialized()
        {
            if (_services is null || _options is null)
            {
                throw new Exception($"Call '{nameof(ConfigureOpenTelemetry)}' on '{nameof(IServiceCollection)}' first");
            }
        }

        private static T Run<T>(this T builder, Action<T>? configure = null)
        {
            configure?.Invoke(builder);
            return builder;
        }
    }
}