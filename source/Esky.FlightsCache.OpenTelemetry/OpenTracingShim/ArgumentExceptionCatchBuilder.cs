using OpenTracing;
using OpenTracing.Tag;

namespace Esky.FlightsCache.OpenTelemetry
{
    public sealed class ArgumentExceptionCatchBuilder : ISpanBuilder
    {
        private readonly ISpanBuilder _inner;

        public ArgumentExceptionCatchBuilder(ISpanBuilder inner)
        {
            _inner = inner;
        }

        public ISpanBuilder AsChildOf(ISpanContext parent)
        {
            return _inner.AsChildOf(parent);
        }

        public ISpanBuilder AsChildOf(ISpan parent)
        {
            return _inner.AsChildOf(parent);
        }

        public ISpanBuilder AddReference(string referenceType, ISpanContext referencedContext)
        {
            return _inner.AddReference(referenceType, referencedContext);
        }

        public ISpanBuilder IgnoreActiveSpan()
        {
            return _inner.IgnoreActiveSpan();
        }

        public ISpanBuilder WithTag(string key, string value)
        {
            return _inner.WithTag(key, value);
        }

        public ISpanBuilder WithTag(string key, bool value)
        {
            return _inner.WithTag(key, value);
        }

        public ISpanBuilder WithTag(string key, int value)
        {
            return _inner.WithTag(key, value);
        }

        public ISpanBuilder WithTag(string key, double value)
        {
            return _inner.WithTag(key, value);
        }

        public ISpanBuilder WithTag(BooleanTag tag, bool value)
        {
            return _inner.WithTag(tag, value);
        }

        public ISpanBuilder WithTag(IntOrStringTag tag, string value)
        {
            return _inner.WithTag(tag, value);
        }

        public ISpanBuilder WithTag(IntTag tag, int value)
        {
            return _inner.WithTag(tag, value);
        }

        public ISpanBuilder WithTag(StringTag tag, string value)
        {
            return _inner.WithTag(tag, value);
        }

        public ISpanBuilder WithStartTimestamp(DateTimeOffset timestamp)
        {
            return _inner.WithStartTimestamp(timestamp);
        }

        public IScope StartActive()
        {
            return TryCatch(() => _inner.StartActive(), NoopScope.Instance);
        }

        public IScope StartActive(bool finishSpanOnDispose)
        {
            return TryCatch(() => _inner.StartActive(finishSpanOnDispose), NoopScope.Instance);
        }

        public ISpan Start()
        {
            return TryCatch(() => _inner.Start(), NoopSpan.Instance);
        }

        private static T TryCatch<T>(Func<T> start, T noOp)
        {
            try
            {
                return start();
            }
            catch (ArgumentException)
            {
                return noOp;
            }
        }
    }
}