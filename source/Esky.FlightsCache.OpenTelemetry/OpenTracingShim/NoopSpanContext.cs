using OpenTracing;

namespace Esky.FlightsCache.OpenTelemetry
{
    public sealed class NoopSpanContext : ISpanContext
    {
        public static readonly NoopSpanContext Instance = new();
        private NoopSpanContext() { }
        public string? TraceId => null;
        public string? SpanId => null;

        public IEnumerable<KeyValuePair<string, string>> GetBaggageItems()
        {
            return Enumerable.Empty<KeyValuePair<string, string>>();
        }
    }
}