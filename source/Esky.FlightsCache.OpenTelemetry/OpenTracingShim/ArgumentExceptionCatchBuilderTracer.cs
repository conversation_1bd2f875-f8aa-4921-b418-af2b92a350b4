using OpenTracing;
using OpenTracing.Propagation;

namespace Esky.FlightsCache.OpenTelemetry
{
    public sealed class ArgumentExceptionCatchBuilderTracer : ITracer
    {
        private readonly ITracer _inner;

        public ArgumentExceptionCatchBuilderTracer(ITracer inner)
        {
            _inner = inner;
        }

        public ISpanBuilder BuildSpan(string operationName)
        {
            return new ArgumentExceptionCatchBuilder(_inner.BuildSpan(operationName));
        }

        public void Inject<TCarrier>(ISpanContext spanContext, IFormat<TCarrier> format, TCarrier carrier)
        {
            _inner.Inject(spanContext, format, carrier);
        }

        public ISpanContext Extract<TCarrier>(IFormat<TCarrier> format, TCarrier carrier)
        {
            return _inner.Extract(format, carrier);
        }

        public IScopeManager ScopeManager => _inner.ScopeManager;
        public ISpan ActiveSpan => _inner.ActiveSpan;
    }
}