<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" Sdk="Microsoft.Docker.Sdk">
  <PropertyGroup Label="Globals">
    <ProjectVersion>2.1</ProjectVersion>
    <DockerTargetOS>Linux</DockerTargetOS>
    <ProjectGuid>************************************</ProjectGuid>
    <DockerLaunchAction>LaunchBrowser</DockerLaunchAction>
    <DockerServiceUrl>{Scheme}://localhost:{ServicePort}/swagger</DockerServiceUrl>
    <DockerServiceName>esky.flightscache.api</DockerServiceName>
  </PropertyGroup>
  <ItemGroup>
    <None Include="docker-compose.yml" />
    <None Include=".dockerignore" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="definitions.json" />
    <Content Include="mockServiceInitializer.json" />
    <Content Include="rabbitmq.conf" />
  </ItemGroup>
</Project>