
# Esky Flights Cache repository

- [Esky Flights Cache repository](#esky-flights-cache-repository)
- [Esky.FlightsCache.CacheRequestConsumer](#eskyflightscachecacherequestconsumer)
  - [Links](#links)
    - [Deployment](#deployment)
    - [Observability](#observability)
- [Esky.FlightsCache.MonthlyAggregator](#eskyflightscachemonthlyaggregator)
  - [Links](#links-2)
    - [Deployment](#deployment-2)
    - [Observability](#observability-2)
- [C4 diagram](#c4-diagram)


# Esky.FlightsCache.CacheRequestConsumer

Consumer is taking messages of type **CacheRequests** from the queue (RabbitMQ), transform the data and save flights to databases (MongoDB and MS SQL).

## Links

### Deployment

- [Docker images registry](https://console.cloud.google.com/gcr/images/esky-ets-flightscontent-pro/eu/esky.flightscache.cacherequestconsumer?project=esky-ets-flightscontent-pro)
- [Spinnaker pipeline](https://spinnaker.eskyspace.com/#/applications/flightscache/executions?pipeline=esky.flightscache.robotsconsumers)
- [YAML definitions](https://stash.eskyspace.com/projects/DEP/repos/flightscontent/browse/esky.flightscache.cacherequestconsumer)
- [Kubernetes deployment](https://console.cloud.google.com/kubernetes/deployment/europe-west1-c/esky-ets-flightscontent-pro-k8s-0/default/esky-flightscache-cacherequestconsumer/overview?project=esky-ets-flightscontent-pro)

### Observability

- Grafana dashboards
  - [Flights stats from MongoDB & CacheRequests queue](https://grafanasre.eskyspace.com/d/7j0t0d8Wk/ets-flightspromocache-mongo)
  - [Kubernetes application details](https://grafanasre.eskyspace.com/d/E-KNLNhZz/k8s-application-details?orgId=1&var-cluster=PROM-ETS-FLIGHTSCONTENT-PRO&var-app=esky.flightscache.cacherequestconsumer&var-namespace=default&var-resolution=__auto_interval_resolution&var-release=es-flightscontent)
  - [RabbitMQ universal dashboard](https://grafanasre.eskyspace.com/d/t8ktV6cVz/rabbitmq-universal?var-job=gcp-rabbitmq-flightscache&var-node=172.28.95.177&var-port=9000&var-mq_instance=gcp-rabbitmq-flightscache&var-source=Prometheus-techsre&var-env=pro&from=now-3h&to=now&orgId=1)
- [Kibana logs](https://kibana.eskyspace.com/app/dashboards#/view/fb75f710-1489-11ec-86a0-5773fbcdbe68?_a=(filters:!((query:(match_phrase:(Application.keyword:esky-flightscache-cacherequestconsumer))))))
- [RabbitMQ web panel](http://rabbitmq-flightscache.service.gcp-pro.consul.:12345/)

# Esky.FlightsCache.MonthlyAggregator

Produces monthly aggregations for calendar views.

## Links

### Deployment

- [Docker images registry](https://console.cloud.google.com/gcr/images/esky-ets-flightscontent-pro/eu/esky.flightscache.monthlyaggregator?project=esky-ets-flightscontent-pro)
- [Spinnaker pipeline](https://spinnaker.eskyspace.com/#/applications/flightscache/executions?pipeline=esky.flightscache.monthlyaggregator)
- [YAML definitions](https://stash.eskyspace.com/projects/DEP/repos/flightscontent/browse/esky.flightscache.monthlyaggregator)

### Observability

- Grafana
  - [Application dashboard](https://grafanasre.eskyspace.com/d/RHKL394Sz/flightscache-monthlyaggregator)
  - [Kubernetes application details](https://grafanasre.eskyspace.com/d/E-KNLNhZz/k8s-application-details?orgId=1&var-cluster=PROM-ETS-FLIGHTSCONTENT-PRO&var-app=esky.flightscache.monthlyaggregator&var-namespace=default&var-resolution=__auto_interval_resolution&var-release=es-flightscontent)
- [Kibana logs](https://kibana.eskyspace.com/app/dashboards#/view/fb75f710-1489-11ec-86a0-5773fbcdbe68?_a=(filters:!((query:(match_phrase:(Application.keyword:esky-flightscache-monthlyaggregator))))))

# C4 diagram
![](<docs/FlightsCacheRepo-diagram/Flights%20Cache%20Repository.svg>)