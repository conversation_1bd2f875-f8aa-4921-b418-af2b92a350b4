@startuml Flights Cache Repository
!include  https://raw.githubusercontent.com/plantuml-stdlib/C4-PlantUML/master/C4_Context.puml

title System Context diagram for esky-flights-cache repository
Enterprise_Boundary(b0, "ESKY") {
    Enterprise_Boundary(b1, "Flights cache") {
        System(CacheRequestConsumer, "CacheRequest consumer", "Consumes messages CacheRequests")
        System_Ext(RobotsConsumer, "Robots consumer")
        System_Ext(CacheImporter, "Cache Importer")

        System(MonthlyAggregator, "MonthlyAggregator", "Produces monthly aggregations")

        SystemDb(MongoDB, "Flights' DB", "MongoDB")
        SystemDb(MsSQL, "Flights' DB", "MS SQL")
        SystemDb(Queue, "CacheRequests' Queue", "RabbitMq")
        SystemDb(Redis, "Redis", "Redis")
    }
    System_Ext(FlightSearchIntegration, "FlightsSearchAPI")
}

Rel(RobotsConsumer, Queue, "Publish CacheRequests")
Rel(FlightSearchIntegration, Queue, "Publish CacheRequests")
Rel(CacheImporter, Queue, "Publish CacheRequests")
Rel(Queue, CacheRequestConsumer, "Consumes CacheRequests")
Rel(CacheRequestConsumer, MongoDB, "Stores Flights")
Rel(CacheRequestConsumer, MsSQL, "Stores Flights")
Rel(CacheRequestConsumer, Redis, "Stores routes for monthly aggregates")

Rel(CacheRequestConsumer, MongoDB, "Stores monthly aggregates")
Rel(Redis, MonthlyAggregator, "Gets routes to process")
Rel(MongoDB, MonthlyAggregator, "Gets flights")
Rel(MonthlyAggregator, MongoDB, "Stores monthly aggregates")

@enduml