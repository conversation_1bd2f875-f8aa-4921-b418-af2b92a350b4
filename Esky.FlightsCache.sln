
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.4.33110.190
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Esky.FlightsCache.MessageContract", "source\Esky.FlightsCache.MessageContract\Esky.FlightsCache.MessageContract.csproj", "{F2ED2126-EA49-4FE8-9ACC-05830FC93846}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Esky.FlightsCache.Processing", "source\Esky.FlightsCache.Processing\Esky.FlightsCache.Processing.csproj", "{41463335-828B-4739-9B62-BEF50882F969}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Esky.FlightsCache.ServiceBus", "source\Esky.FlightsCache.ServiceBus\Esky.FlightsCache.ServiceBus.csproj", "{CFD6CA7B-22D7-4CD0-AE0B-7DDCA311E7F7}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Esky.FlightsCache.ProviderMapping", "source\Esky.FlightsCache.ProviderMapping\Esky.FlightsCache.ProviderMapping.csproj", "{811069D2-6D56-45BE-89E3-A81FCD451AD3}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Esky.FlightsCache.Database", "source\Esky.FlightsCache.Database\Esky.FlightsCache.Database.csproj", "{224C7A7C-0D61-4BDB-932B-491CA222A880}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "CacheProcessing", "CacheProcessing", "{E80F53A9-5C1B-4BFF-8932-E707620B9D6F}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Esky.FlightsCache.Common", "source\Esky.FlightsCache.Common\Esky.FlightsCache.Common.csproj", "{62A43860-561D-40E7-9C7A-08C473F9796F}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Esky.FlightsCache.CurrencyProvider", "source\Esky.FlightsCache.CurrencyProvider\Esky.FlightsCache.CurrencyProvider.csproj", "{7A1BA265-4414-4F94-B59E-1E51B6207EF1}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Esky.FlightsCache.PartnerSettings", "source\Esky.FlightsCache.PartnerSettings\Esky.FlightsCache.PartnerSettings.csproj", "{AD2C0EE5-30C7-4827-9CA2-6CB2AE06B2E8}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Esky.FlightsCache.ObjectDirectory", "source\Esky.FlightsCache.ObjectDirectory\Esky.FlightsCache.ObjectDirectory.csproj", "{0D579495-0CDC-4DFE-A699-11D46B6D60D7}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "ExternalServices", "ExternalServices", "{58BF9DC9-D47D-4DB0-AAEA-417863D6A373}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Esky.FlightsCache.RuleEngine", "source\Esky.FlightsCache.RuleEngine\Esky.FlightsCache.RuleEngine.csproj", "{AC4834ED-09FD-4BCF-8F41-706D278B545A}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Esky.FlightsCache.ResultFilters", "source\Esky.FlightsCache.ResultFilters\Esky.FlightsCache.ResultFilters.csproj", "{196E3C53-0C29-40FF-B752-C142234439D3}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Esky.FlightsCache.Processing.Tests", "source\Esky.FlightsCache.Processing.Tests\Esky.FlightsCache.Processing.Tests.csproj", "{32DCACAA-6559-4901-A1AA-6D5704FD25C0}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{A4B116C9-38E3-40C5-8A82-FEC484C67A94}"
	ProjectSection(SolutionItems) = preProject
		.editorconfig = .editorconfig
		.github\workflows\actions_master.yml = .github\workflows\actions_master.yml
		.github\workflows\actions_pr.yml = .github\workflows\actions_pr.yml
		docker-entrypoint-initdb.d\mongo-init.js = docker-entrypoint-initdb.d\mongo-init.js
		infrastructure\database\setup_sharding.mongo = infrastructure\database\setup_sharding.mongo
	EndProjectSection
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Esky.FlightsCache.OpenTelemetry", "source\Esky.FlightsCache.OpenTelemetry\Esky.FlightsCache.OpenTelemetry.csproj", "{5383364C-F24B-4D31-A21B-7DB17592A0E5}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Packages", "Packages", "{5C7F2F72-BA79-428D-9966-ADB9D5E93278}"
EndProject
Project("{E53339B2-1760-4266-BCC7-CA923CBCF16C}") = "docker-compose", "docker-compose.dcproj", "{E8CAD839-3801-4CD0-82B8-5FF5F5F39A86}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Applications", "Applications", "{D39FD830-23E3-4E10-8D70-2A47F4327589}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Esky.FlightsCache.CacheRequestConsumer", "source\Esky.FlightsCache.CacheRequestConsumer\Esky.FlightsCache.CacheRequestConsumer.csproj", "{26D01F9E-759B-4EC8-881F-EF9FE1E495BF}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Esky.FlightsCache.MonthlyAggregator", "source\Esky.FlightsCache.MonthlyAggregator\Esky.FlightsCache.MonthlyAggregator.csproj", "{4FC2CB14-8608-4521-AB4F-745D9FEE1C1E}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Esky.FlightsCache.MonthlyAggregator.Tests", "source\Esky.FlightsCache.MonthlyAggregator.Tests\Esky.FlightsCache.MonthlyAggregator.Tests.csproj", "{337CB12D-4085-4407-AB38-C82B5D8663FC}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Esky.FlightsCache.CacheRequestConsumer.Tests", "source\Esky.FlightsCache.CacheRequestConsumer.Tests\Esky.FlightsCache.CacheRequestConsumer.Tests.csproj", "{B5E3132F-981A-4FEA-BC17-7AAD5ABAA067}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "CacheRequestConsumer", "CacheRequestConsumer", "{C407F693-544A-4DC8-8DCE-6625975DAC8D}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "MonthlyAggregator", "MonthlyAggregator", "{1F2B42C1-8998-4BCE-828D-24993A61E1E2}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Esky.FlightsCache.Integration.Tests", "source\Esky.FlightsCache.Integration.Tests\Esky.FlightsCache.Integration.Tests.csproj", "{B3376AF6-FE91-4358-AB52-21424AEEFF39}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{F2ED2126-EA49-4FE8-9ACC-05830FC93846}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F2ED2126-EA49-4FE8-9ACC-05830FC93846}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F2ED2126-EA49-4FE8-9ACC-05830FC93846}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F2ED2126-EA49-4FE8-9ACC-05830FC93846}.Release|Any CPU.Build.0 = Release|Any CPU
		{41463335-828B-4739-9B62-BEF50882F969}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{41463335-828B-4739-9B62-BEF50882F969}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{41463335-828B-4739-9B62-BEF50882F969}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{41463335-828B-4739-9B62-BEF50882F969}.Release|Any CPU.Build.0 = Release|Any CPU
		{CFD6CA7B-22D7-4CD0-AE0B-7DDCA311E7F7}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{CFD6CA7B-22D7-4CD0-AE0B-7DDCA311E7F7}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{CFD6CA7B-22D7-4CD0-AE0B-7DDCA311E7F7}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{CFD6CA7B-22D7-4CD0-AE0B-7DDCA311E7F7}.Release|Any CPU.Build.0 = Release|Any CPU
		{811069D2-6D56-45BE-89E3-A81FCD451AD3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{811069D2-6D56-45BE-89E3-A81FCD451AD3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{811069D2-6D56-45BE-89E3-A81FCD451AD3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{811069D2-6D56-45BE-89E3-A81FCD451AD3}.Release|Any CPU.Build.0 = Release|Any CPU
		{224C7A7C-0D61-4BDB-932B-491CA222A880}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{224C7A7C-0D61-4BDB-932B-491CA222A880}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{224C7A7C-0D61-4BDB-932B-491CA222A880}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{224C7A7C-0D61-4BDB-932B-491CA222A880}.Release|Any CPU.Build.0 = Release|Any CPU
		{62A43860-561D-40E7-9C7A-08C473F9796F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{62A43860-561D-40E7-9C7A-08C473F9796F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{62A43860-561D-40E7-9C7A-08C473F9796F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{62A43860-561D-40E7-9C7A-08C473F9796F}.Release|Any CPU.Build.0 = Release|Any CPU
		{7A1BA265-4414-4F94-B59E-1E51B6207EF1}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7A1BA265-4414-4F94-B59E-1E51B6207EF1}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7A1BA265-4414-4F94-B59E-1E51B6207EF1}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7A1BA265-4414-4F94-B59E-1E51B6207EF1}.Release|Any CPU.Build.0 = Release|Any CPU
		{AD2C0EE5-30C7-4827-9CA2-6CB2AE06B2E8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{AD2C0EE5-30C7-4827-9CA2-6CB2AE06B2E8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{AD2C0EE5-30C7-4827-9CA2-6CB2AE06B2E8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{AD2C0EE5-30C7-4827-9CA2-6CB2AE06B2E8}.Release|Any CPU.Build.0 = Release|Any CPU
		{0D579495-0CDC-4DFE-A699-11D46B6D60D7}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0D579495-0CDC-4DFE-A699-11D46B6D60D7}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0D579495-0CDC-4DFE-A699-11D46B6D60D7}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0D579495-0CDC-4DFE-A699-11D46B6D60D7}.Release|Any CPU.Build.0 = Release|Any CPU
		{AC4834ED-09FD-4BCF-8F41-706D278B545A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{AC4834ED-09FD-4BCF-8F41-706D278B545A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{AC4834ED-09FD-4BCF-8F41-706D278B545A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{AC4834ED-09FD-4BCF-8F41-706D278B545A}.Release|Any CPU.Build.0 = Release|Any CPU
		{196E3C53-0C29-40FF-B752-C142234439D3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{196E3C53-0C29-40FF-B752-C142234439D3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{196E3C53-0C29-40FF-B752-C142234439D3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{196E3C53-0C29-40FF-B752-C142234439D3}.Release|Any CPU.Build.0 = Release|Any CPU
		{32DCACAA-6559-4901-A1AA-6D5704FD25C0}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{32DCACAA-6559-4901-A1AA-6D5704FD25C0}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{32DCACAA-6559-4901-A1AA-6D5704FD25C0}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{32DCACAA-6559-4901-A1AA-6D5704FD25C0}.Release|Any CPU.Build.0 = Release|Any CPU
		{5383364C-F24B-4D31-A21B-7DB17592A0E5}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5383364C-F24B-4D31-A21B-7DB17592A0E5}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5383364C-F24B-4D31-A21B-7DB17592A0E5}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5383364C-F24B-4D31-A21B-7DB17592A0E5}.Release|Any CPU.Build.0 = Release|Any CPU
		{E8CAD839-3801-4CD0-82B8-5FF5F5F39A86}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E8CAD839-3801-4CD0-82B8-5FF5F5F39A86}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E8CAD839-3801-4CD0-82B8-5FF5F5F39A86}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E8CAD839-3801-4CD0-82B8-5FF5F5F39A86}.Release|Any CPU.Build.0 = Release|Any CPU
		{26D01F9E-759B-4EC8-881F-EF9FE1E495BF}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{26D01F9E-759B-4EC8-881F-EF9FE1E495BF}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{26D01F9E-759B-4EC8-881F-EF9FE1E495BF}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{26D01F9E-759B-4EC8-881F-EF9FE1E495BF}.Release|Any CPU.Build.0 = Release|Any CPU
		{4FC2CB14-8608-4521-AB4F-745D9FEE1C1E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{4FC2CB14-8608-4521-AB4F-745D9FEE1C1E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{4FC2CB14-8608-4521-AB4F-745D9FEE1C1E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{4FC2CB14-8608-4521-AB4F-745D9FEE1C1E}.Release|Any CPU.Build.0 = Release|Any CPU
		{337CB12D-4085-4407-AB38-C82B5D8663FC}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{337CB12D-4085-4407-AB38-C82B5D8663FC}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{337CB12D-4085-4407-AB38-C82B5D8663FC}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{337CB12D-4085-4407-AB38-C82B5D8663FC}.Release|Any CPU.Build.0 = Release|Any CPU
		{B5E3132F-981A-4FEA-BC17-7AAD5ABAA067}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B5E3132F-981A-4FEA-BC17-7AAD5ABAA067}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B5E3132F-981A-4FEA-BC17-7AAD5ABAA067}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B5E3132F-981A-4FEA-BC17-7AAD5ABAA067}.Release|Any CPU.Build.0 = Release|Any CPU
		{B3376AF6-FE91-4358-AB52-21424AEEFF39}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B3376AF6-FE91-4358-AB52-21424AEEFF39}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B3376AF6-FE91-4358-AB52-21424AEEFF39}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B3376AF6-FE91-4358-AB52-21424AEEFF39}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{F2ED2126-EA49-4FE8-9ACC-05830FC93846} = {5C7F2F72-BA79-428D-9966-ADB9D5E93278}
		{41463335-828B-4739-9B62-BEF50882F969} = {C407F693-544A-4DC8-8DCE-6625975DAC8D}
		{CFD6CA7B-22D7-4CD0-AE0B-7DDCA311E7F7} = {5C7F2F72-BA79-428D-9966-ADB9D5E93278}
		{811069D2-6D56-45BE-89E3-A81FCD451AD3} = {5C7F2F72-BA79-428D-9966-ADB9D5E93278}
		{224C7A7C-0D61-4BDB-932B-491CA222A880} = {E80F53A9-5C1B-4BFF-8932-E707620B9D6F}
		{62A43860-561D-40E7-9C7A-08C473F9796F} = {E80F53A9-5C1B-4BFF-8932-E707620B9D6F}
		{7A1BA265-4414-4F94-B59E-1E51B6207EF1} = {58BF9DC9-D47D-4DB0-AAEA-417863D6A373}
		{AD2C0EE5-30C7-4827-9CA2-6CB2AE06B2E8} = {58BF9DC9-D47D-4DB0-AAEA-417863D6A373}
		{0D579495-0CDC-4DFE-A699-11D46B6D60D7} = {58BF9DC9-D47D-4DB0-AAEA-417863D6A373}
		{AC4834ED-09FD-4BCF-8F41-706D278B545A} = {58BF9DC9-D47D-4DB0-AAEA-417863D6A373}
		{196E3C53-0C29-40FF-B752-C142234439D3} = {E80F53A9-5C1B-4BFF-8932-E707620B9D6F}
		{32DCACAA-6559-4901-A1AA-6D5704FD25C0} = {C407F693-544A-4DC8-8DCE-6625975DAC8D}
		{5383364C-F24B-4D31-A21B-7DB17592A0E5} = {5C7F2F72-BA79-428D-9966-ADB9D5E93278}
		{26D01F9E-759B-4EC8-881F-EF9FE1E495BF} = {C407F693-544A-4DC8-8DCE-6625975DAC8D}
		{4FC2CB14-8608-4521-AB4F-745D9FEE1C1E} = {1F2B42C1-8998-4BCE-828D-24993A61E1E2}
		{337CB12D-4085-4407-AB38-C82B5D8663FC} = {1F2B42C1-8998-4BCE-828D-24993A61E1E2}
		{B5E3132F-981A-4FEA-BC17-7AAD5ABAA067} = {C407F693-544A-4DC8-8DCE-6625975DAC8D}
		{C407F693-544A-4DC8-8DCE-6625975DAC8D} = {D39FD830-23E3-4E10-8D70-2A47F4327589}
		{1F2B42C1-8998-4BCE-828D-24993A61E1E2} = {D39FD830-23E3-4E10-8D70-2A47F4327589}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {6387F566-3849-45CA-8C9B-7596A1111BB1}
	EndGlobalSection
EndGlobal
