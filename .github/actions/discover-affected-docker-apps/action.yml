name: 'Discover affected Dockerfile'
description: 'Get affected docker images'
inputs:
  workflow-id:
    required: true
  branch:
    default: 'master'
outputs:
  matrix-json:
    description: "strategy matrix as json minified - '{}' if empty"
    value: ${{ steps.affected.outputs.matrix-json }}
runs:
  using: "composite"
  steps:
    - name: setup node 
      uses: actions/setup-node@v4
      with:
        node-version: 18

    - name: setup dotnet
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: |
          7
          8
        
    - name: set SHAs
      uses: nrwl/nx-set-shas@v3
      id: SHAs
      with:
        main-branch-name: ${{ inputs.branch }}
        workflow-id: ${{ inputs.workflow-id }}
        
    - name: get affected
      shell: bash
      id: affected
      run: |
        dotnet tool install docker-affected --version 1.0.0 --create-manifest-if-needed
        dotnet tool run docker-affected --commit-from ${{ steps.SHAs.outputs.base }} --commit-to ${{ github.sha }}
        echo "matrix-json=$(cat docker-affected.json)" >> $GITHUB_OUTPUT