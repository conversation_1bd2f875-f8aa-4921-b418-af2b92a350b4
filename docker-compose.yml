version: '3.4'

services:
  esky.flightscache.monthlyaggregator:
    image: eskyflightscachemonthlyaggregator
    build:
      context: .
      dockerfile: source/Esky.FlightsCache.MonthlyAggregator/Dockerfile
    depends_on:
      - rabbitmq
      - mongo
      - sql.server
      - redis
    environment:
      ASPNETCORE_ENVIRONMENT: Development
      NLog__targets__rabbit__compression: None
      
  esky.flightscache.cacherequestconsumer:
    image: eskyflightscachecacherequestconsumer
    build:
      context: .
      dockerfile: source/Esky.FlightsCache.CacheRequestConsumer/Dockerfile
    depends_on: 
      - rabbitmq
      - mongo
      - sql.server
      - redis
    environment:
      ASPNETCORE_ENVIRONMENT: Development
      NLog__targets__rabbit__compression: None
      
  rabbitmq:
    image: masstransit/rabbitmq
    environment:
      RABBITMQ_DEFAULT_USER: cacheuser
      RABBITMQ_DEFAULT_PASS: cachepassword
    ports:
      # AMQP protocol port
      - "5672:5672"
      # HTTP management UI
      - "15672:15672"
    extra_hosts:
      - "host.docker.internal:host-gateway"
    healthcheck:
      test: ["CMD", "bash", "-c", "echo hi > /dev/tcp/localhost/5672"]
      interval: 5s
      timeout: 25s
      retries: 5
    volumes:
      - "./rabbitmq.conf:/etc/rabbitmq/conf.d/rabbitmq.conf"
      - "./definitions.json:/etc/rabbitmq/definitions.json"

  mongo: 
    image: mongo:7.0
    ports:
      - "27018:27017"
    volumes:
      - mongovolume:/data/db
      - ./docker-entrypoint-initdb.d/mongo-init.js:/docker-entrypoint-initdb.d/mongo-init.js:ro
    extra_hosts:
      - "host.docker.internal:host-gateway"

  sqldatabaseupgrader:
    image: eu.gcr.io/esky-ets-flightscontent-pro/esky.flightscache.sqldatabaseupgrader:stable
    environment:
      ConnectionStrings__FlightsPromoCache: Server=sql.server,1433;Database=IBE_FlightsPromoCache;uid=sa;pwd=S3creT_pAss!;Connection Timeout=30;TrustServerCertificate=True
    depends_on:
      - sql.server
    restart: on-failure
  
  sql.server:
    image: europe-docker.pkg.dev/esky-common/esky-docker-virtual/mssql/server:2022-latest
    ports:
      - "1433:1433"
    environment:
      ACCEPT_EULA: Y
      MSSQL_PID: Developer
      MSSQL_SA_PASSWORD: S3creT_pAss!
    command:
      - /bin/bash
      - -c
      - |
        # Launch MSSQL and send to background
        /opt/mssql/bin/sqlservr &
        pid=$$!
        # Wait for it to be available
        echo "Waiting for MS SQL to be available"
        /opt/mssql-tools/bin/sqlcmd -l 30 -h-1 -V1 -U sa -P $$MSSQL_SA_PASSWORD -Q "SET NOCOUNT ON SELECT \"SQL Server is up\" , @@servername"
        is_up=$$?
        while [ $$is_up -ne 0 ] ; do
          echo -e $$(date)
          /opt/mssql-tools/bin/sqlcmd -l 30 -h-1 -V1 -U sa -P $$MSSQL_SA_PASSWORD -Q "SET NOCOUNT ON SELECT \"SQL Server is up\" , @@servername"
          is_up=$$?
          sleep 5
        done
        # Wait on the sqlserver process
        wait $$pid
    volumes:
        - sqlvolume:/var/opt/mssql
    extra_hosts:
      - "host.docker.internal:host-gateway"

  mockserver:
    image: mockserver/mockserver
    ports:
      - 1090:1090
    environment:
      LOG_LEVEL: "DEBUG"
      SERVER_PORT: 1090
      MOCKSERVER_PROPERTY_FILE: /config/mockserver.properties
      MOCKSERVER_INITIALIZATION_JSON_PATH: /config/mockServiceInitializer.json
      MOCKSERVER_LIVENESS_HTTP_GET_PATH: /health
    volumes:
      -  ./mockServiceInitializer.json:/config/mockServiceInitializer.json 
    extra_hosts:
      - "host.docker.internal:host-gateway"

  redis:
    image: redis:6.2-alpine
    ports:
      - "6379:6379"
    command: redis-server --loglevel warning --requirepass k8rBZeXosd
    volumes:
      - redis:/data/redis
    extra_hosts:
      - "host.docker.internal:host-gateway"

volumes:
  mongovolume:
  sqlvolume:
  redis: